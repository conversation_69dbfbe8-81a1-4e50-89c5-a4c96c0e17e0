{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Consumer Affairs',\r\n      href: '/customer/consumer-affairs',\r\n      icon: 'ri-shield-user-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/consumer-affairs',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/standards': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/consumer-affairs': 'Loading Consumer Affairs...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM;gBACpC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;aACD;kDAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM;gBACjC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;+CAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,MAAM,gBAAgB;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,cAAc,OAAO;kEAAC,CAAA;4BACpB,OAAO,QAAQ,CAAC;wBAClB;;gBACF;;YAEA,4DAA4D;YAC5D,MAAM,QAAQ,WAAW,eAAe;YACxC;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,uBAAuB,CAAC;QAC1B;0DAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAChD,MAAM,eAA0C;gBAC9C,aAAa;gBACb,sBAAsB;gBACtB,0BAA0B;gBAC1B,oCAAoC;gBACpC,sBAAsB;gBACtB,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,8BAA8B;gBAC9B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;YAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;YAC1D,WAAW;YACX,uBAAuB;QACzB;qDAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,2CAA2C;YAC3C,OAAO,QAAQ,CAAC;QAClB;qDAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,sBAAsB,CAAC;QACzB;yDAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,qCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,6LAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,6LAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,6LAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,6LAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,6LAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,6LAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,6LAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,gIAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,6LAAC,qIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlVM;;QAGa,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACC,kIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,aAAU;;;KAN7B;uCAoVS", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/cacheService.ts"], "sourcesContent": ["// Cache service for API responses to reduce rate limiting\n\ninterface CacheItem<T> {\n  data: T;\n  timestamp: number;\n  expiresAt: number;\n}\n\nclass CacheService {\n  private cache = new Map<string, CacheItem<any>>();\n  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL\n\n  /**\n   * Set cache item with TTL\n   */\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\n    const now = Date.now();\n    const item: CacheItem<T> = {\n      data,\n      timestamp: now,\n      expiresAt: now + ttl\n    };\n    \n    this.cache.set(key, item);\n    console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);\n  }\n\n  /**\n   * Get cache item if not expired\n   */\n  get<T>(key: string): T | null {\n    const item = this.cache.get(key);\n    \n    if (!item) {\n      console.log(`Cache MISS: ${key}`);\n      return null;\n    }\n\n    const now = Date.now();\n    if (now > item.expiresAt) {\n      console.log(`Cache EXPIRED: ${key}`);\n      this.cache.delete(key);\n      return null;\n    }\n\n    console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);\n    return item.data as T;\n  }\n\n  /**\n   * Check if cache has valid item\n   */\n  has(key: string): boolean {\n    return this.get(key) !== null;\n  }\n\n  /**\n   * Delete cache item\n   */\n  delete(key: string): boolean {\n    console.log(`Cache DELETE: ${key}`);\n    return this.cache.delete(key);\n  }\n\n  /**\n   * Clear all cache\n   */\n  clear(): void {\n    console.log('Cache CLEAR: All items');\n    this.cache.clear();\n  }\n\n  /**\n   * Get cache stats\n   */\n  getStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys())\n    };\n  }\n\n  /**\n   * Clean expired items\n   */\n  cleanup(): void {\n    const now = Date.now();\n    let cleaned = 0;\n\n    for (const [key, item] of this.cache.entries()) {\n      if (now > item.expiresAt) {\n        this.cache.delete(key);\n        cleaned++;\n      }\n    }\n\n    if (cleaned > 0) {\n      console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);\n    }\n  }\n\n  /**\n   * Get or set pattern - fetch data if not cached\n   */\n  async getOrSet<T>(\n    key: string,\n    fetcher: () => Promise<T>,\n    ttl: number = this.defaultTTL\n  ): Promise<T> {\n    // Try to get from cache first\n    const cached = this.get<T>(key);\n    if (cached !== null) {\n      return cached;\n    }\n\n    // Fetch fresh data\n    console.log(`Cache FETCH: ${key}`);\n    const data = await fetcher();\n    \n    // Store in cache\n    this.set(key, data, ttl);\n    \n    return data;\n  }\n\n  /**\n   * Invalidate cache by pattern\n   */\n  invalidatePattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    let invalidated = 0;\n\n    for (const key of this.cache.keys()) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n        invalidated++;\n      }\n    }\n\n    if (invalidated > 0) {\n      console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);\n    }\n  }\n}\n\n// Create singleton instance\nexport const cacheService = new CacheService();\n\n// Cache keys constants\nexport const CACHE_KEYS = {\n  LICENSE_TYPES: 'license-types',\n  LICENSE_CATEGORIES: 'license-categories',\n  LICENSE_CATEGORIES_BY_TYPE: (typeId: string) => `license-categories-type-${typeId}`,\n  USER_APPLICATIONS: 'user-applications',\n  APPLICATION: (id: string) => `application-${id}`,\n} as const;\n\n// Cache TTL constants (in milliseconds)\nexport const CACHE_TTL = {\n  SHORT: 2 * 60 * 1000,      // 2 minutes\n  MEDIUM: 5 * 60 * 1000,     // 5 minutes\n  LONG: 15 * 60 * 1000,      // 15 minutes\n  VERY_LONG: 60 * 60 * 1000, // 1 hour\n} as const;\n\n// Auto cleanup every 5 minutes\nsetInterval(() => {\n  cacheService.cleanup();\n}, 5 * 60 * 1000);\n\nexport default cacheService;\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;AAQ1D,MAAM;IACI,QAAQ,IAAI,MAA8B;IAC1C,aAAa,IAAI,KAAK,KAAK;IAEnC;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAqB;YACzB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;QACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa,EAAE,IAAI,GAAG,CAAC;IACvD;IAEA;;GAEC,GACD,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE5B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK;YAChC,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,GAAG,CAAC;QAChE,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,IAAI,GAAW,EAAW;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;IAC3B;IAEA;;GAEC,GACD,OAAO,GAAW,EAAW;QAC3B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA;;GAEC,GACD,QAAc;QACZ,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA;;GAEC,GACD,WAA6C;QAC3C,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,UAAU;QAEd,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC9C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,UAAU,GAAG;YACf,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,cAAc,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,MAAc,IAAI,CAAC,UAAU,EACjB;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK;QACjC,MAAM,OAAO,MAAM;QAEnB,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;QAEpB,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QACzB,IAAI,cAAc;QAElB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,cAAc,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,yBAAyB,EAAE,SAAS;QAC3F;IACF;AACF;AAGO,MAAM,eAAe,IAAI;AAGzB,MAAM,aAAa;IACxB,eAAe;IACf,oBAAoB;IACpB,4BAA4B,CAAC,SAAmB,CAAC,wBAAwB,EAAE,QAAQ;IACnF,mBAAmB;IACnB,aAAa,CAAC,KAAe,CAAC,YAAY,EAAE,IAAI;AAClD;AAGO,MAAM,YAAY;IACvB,OAAO,IAAI,KAAK;IAChB,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAEA,+BAA+B;AAC/B,YAAY;IACV,aAAa,OAAO;AACtB,GAAG,IAAI,KAAK;uCAEG", "debugId": null}}, {"offset": {"line": 1062, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseTypeService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\n\n// Types\nexport interface LicenseType {\n  license_type_id: string;\n  name: string;\n  code?: string;\n  description: string;\n  validity: number;\n  created_at: string;\n  updated_at: string;\n  created_by?: string;\n  updated_by?: string;\n  creator?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  updater?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n}\n\nexport interface CreateLicenseTypeDto {\n  name: string;\n  description: string;\n  validity: number;\n}\n\nexport interface UpdateLicenseTypeDto {\n  name?: string;\n  description?: string;\n  validity?: number;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    itemsPerPage: number;\n    totalItems?: number;\n    currentPage?: number;\n    totalPages?: number;\n    sortBy: [string, string][];\n    searchBy: string[];\n    search: string;\n    select: string[];\n    filter?: Record<string, string | string[]>;\n  };\n  links: {\n    first?: string;\n    previous?: string;\n    current: string;\n    next?: string;\n    last?: string;\n  };\n}\n\nexport type LicenseTypesResponse = PaginatedResponse<LicenseType>;\n\nexport interface PaginateQuery {\n  page?: number;\n  limit?: number;\n  sortBy?: string[];\n  searchBy?: string[];\n  search?: string;\n  filter?: Record<string, string | string[]>;\n}\n\nexport const licenseTypeService = {\n  // Get all license types with pagination\n  async getLicenseTypes(query: PaginateQuery = {}): Promise<LicenseTypesResponse> {\n    const params = new URLSearchParams();\n\n    if (query.page) params.set('page', query.page.toString());\n    if (query.limit) params.set('limit', query.limit.toString());\n    if (query.search) params.set('search', query.search);\n    if (query.sortBy) {\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\n    }\n    if (query.searchBy) {\n      query.searchBy.forEach(search => params.append('searchBy', search));\n    }\n    if (query.filter) {\n      Object.entries(query.filter).forEach(([key, value]) => {\n        if (Array.isArray(value)) {\n          value.forEach(v => params.append(`filter.${key}`, v));\n        } else {\n          params.set(`filter.${key}`, value);\n        }\n      });\n    }\n\n    const response = await apiClient.get(`/license-types?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get license type by ID\n  async getLicenseType(id: string): Promise<LicenseType> {\n    const response = await apiClient.get(`/license-types/${id}`);\n    return response.data;\n  },\n\n  // Create new license type\n  async createLicenseType(licenseTypeData: CreateLicenseTypeDto): Promise<LicenseType> {\n    const response = await apiClient.post('/license-types', licenseTypeData);\n    return response.data;\n  },\n\n  // Update license type\n  async updateLicenseType(id: string, licenseTypeData: UpdateLicenseTypeDto): Promise<LicenseType> {\n    const response = await apiClient.put(`/license-types/${id}`, licenseTypeData);\n    return response.data;\n  },\n\n  // Delete license type\n  async deleteLicenseType(id: string): Promise<{ message: string }> {\n    const response = await apiClient.delete(`/license-types/${id}`);\n    return response.data;\n  },\n\n  // Get all license types (simple list for dropdowns) with caching\n  async getAllLicenseTypes(): Promise<LicenseType[]> {\n    return cacheService.getOrSet(\n      CACHE_KEYS.LICENSE_TYPES,\n      async () => {\n        console.log('Fetching license types from API...');\n        // Reduce limit to avoid rate limiting\n        const response = await this.getLicenseTypes({ limit: 100 });\n        return response.data;\n      },\n      CACHE_TTL.LONG // Cache for 15 minutes\n    );\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAwEO,MAAM,qBAAqB;IAChC,wCAAwC;IACxC,MAAM,iBAAgB,QAAuB,CAAC,CAAC;QAC7C,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,OAAO,QAAQ,IAAI;QAC1E,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,mBAAkB,eAAqC;QAC3D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,kBAAkB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU,EAAE,eAAqC;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI,EAAE;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,eAAe,EAAE,IAAI;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,iEAAiE;IACjE,MAAM;QACJ,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,kIAAA,CAAA,aAAU,CAAC,aAAa,EACxB;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,eAAe,CAAC;gBAAE,OAAO;YAAI;YACzD,OAAO,SAAS,IAAI;QACtB,GACA,kIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;AACF", "debugId": null}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseCategoryService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { LicenseType } from './licenseTypeService';\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\n\n// Types\nexport interface LicenseCategory {\n  license_category_id: string;\n  license_type_id: string;\n  parent_id?: string;\n  name: string;\n  fee: string;\n  description: string;\n  authorizes: string;\n  created_at: string;\n  updated_at: string;\n  created_by?: string;\n  updated_by?: string;\n  license_type?: LicenseType;\n  parent?: LicenseCategory;\n  children?: LicenseCategory[];\n  creator?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  updater?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n}\n\nexport interface CreateLicenseCategoryDto {\n  license_type_id: string;\n  parent_id?: string;\n  name: string;\n  fee: string;\n  description: string;\n  authorizes: string;\n}\n\nexport interface UpdateLicenseCategoryDto {\n  license_type_id?: string;\n  parent_id?: string;\n  name?: string;\n  fee?: string;\n  description?: string;\n  authorizes?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    itemsPerPage: number;\n    totalItems?: number;\n    currentPage?: number;\n    totalPages?: number;\n    sortBy: [string, string][];\n    searchBy: string[];\n    search: string;\n    select: string[];\n    filter?: Record<string, string | string[]>;\n  };\n  links: {\n    first?: string;\n    previous?: string;\n    current: string;\n    next?: string;\n    last?: string;\n  };\n}\n\nexport type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;\n\nexport interface PaginateQuery {\n  page?: number;\n  limit?: number;\n  sortBy?: string[];\n  searchBy?: string[];\n  search?: string;\n  filter?: Record<string, string | string[]>;\n}\n\nexport const licenseCategoryService = {\n  // Get all license categories with pagination\n  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {\n    const params = new URLSearchParams();\n\n    if (query.page) params.set('page', query.page.toString());\n    if (query.limit) params.set('limit', query.limit.toString());\n    if (query.search) params.set('search', query.search);\n    if (query.sortBy) {\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\n    }\n    if (query.searchBy) {\n      query.searchBy.forEach(search => params.append('searchBy', search));\n    }\n    if (query.filter) {\n      Object.entries(query.filter).forEach(([key, value]) => {\n        if (Array.isArray(value)) {\n          value.forEach(v => params.append(`filter.${key}`, v));\n        } else {\n          params.set(`filter.${key}`, value);\n        }\n      });\n    }\n\n    const response = await apiClient.get(`/license-categories?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get license category by ID\n  async getLicenseCategory(id: string): Promise<LicenseCategory> {\n    const response = await apiClient.get(`/license-categories/${id}`);\n    return response.data;\n  },\n\n  // Get license categories by license type\n  async getLicenseCategoriesByType(licenseTypeId: string): Promise<LicenseCategory[]> {\n    const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\n    return response.data;\n  },\n\n  // Create new license category\n  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {\n    const response = await apiClient.post('/license-categories', licenseCategoryData);\n    return response.data;\n  },\n\n  // Update license category\n  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {\n    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);\n    return response.data;\n  },\n\n  // Delete license category\n  async deleteLicenseCategory(id: string): Promise<{ message: string }> {\n    const response = await apiClient.delete(`/license-categories/${id}`);\n    return response.data;\n  },\n\n  // Get all license categories (simple list for dropdowns) with caching\n  async getAllLicenseCategories(): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      CACHE_KEYS.LICENSE_CATEGORIES,\n      async () => {\n        console.log('Fetching license categories from API...');\n        // Reduce limit to avoid rate limiting\n        const response = await this.getLicenseCategories({ limit: 100 });\n        return response.data;\n      },\n      CACHE_TTL.LONG // Cache for 15 minutes\n    );\n  },\n\n  // Get hierarchical tree of categories for a license type with caching\n  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      `category-tree-${licenseTypeId}`,\n      async () => {\n        console.log(`Fetching category tree for license type: ${licenseTypeId}`);\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);\n        return response.data;\n      },\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\n    );\n  },\n\n  // Get root categories (no parent) for a license type with caching\n  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      `root-categories-${licenseTypeId}`,\n      async () => {\n        console.log(`Fetching root categories for license type: ${licenseTypeId}`);\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);\n        return response.data;\n      },\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\n    );\n  },\n\n  // Get license categories for parent selection dropdown\n  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\n    try {\n      const params = excludeId ? { excludeId } : {};\n      console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);\n\n      // Try the new endpoint first\n      try {\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });\n\n\n        if (response.data && Array.isArray(response.data.data)) {\n          console.log('✅ Valid array response with', response.data.data.length, 'items')\n          return response.data.data;\n        } else {\n          console.warn('⚠️ API returned non-array data:', response.data);\n          return [];\n        }\n      } catch (newEndpointError) {\n        console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);\n\n        // Fallback to existing endpoint\n        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\n        console.log('🔄 Fallback response:', response.data);\n\n        if (response.data && Array.isArray(response.data)) {\n          // Filter out the excluded category if specified\n          let categories = response.data;\n          if (excludeId) {\n            categories = categories.filter(cat => cat.license_category_id !== excludeId);\n          }\n          console.log('✅ Fallback successful with', categories.length, 'items');\n          return categories;\n        } else {\n          console.warn('⚠️ Fallback also returned non-array data:', response.data);\n          return [];\n        }\n      }\n    } catch (error) {\n\n      return [];\n    }\n  },\n\n  // Get potential parent categories for a license type\n  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\n    const params = excludeId ? { excludeId } : {};\n    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });\n    return response.data;\n  },\n};\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;AAmFO,MAAM,yBAAyB;IACpC,6CAA6C;IAC7C,MAAM,sBAAqB,QAAuB,CAAC,CAAC;QAClD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,oBAAmB,EAAU;QACjC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,yCAAyC;IACzC,MAAM,4BAA2B,aAAqB;QACpD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC3F,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,MAAM,uBAAsB,mBAA6C;QACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU,EAAE,mBAA6C;QACnF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU;QACpC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,sEAAsE;IACtE,MAAM;QACJ,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,kIAAA,CAAA,aAAU,CAAC,kBAAkB,EAC7B;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAAE,OAAO;YAAI;YAC9D,OAAO,SAAS,IAAI;QACtB,GACA,kIAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,sEAAsE;IACtE,MAAM,iBAAgB,aAAqB;QACzC,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,cAAc,EAAE,eAAe,EAChC;YACE,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,eAAe;YACvE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,SAAS,IAAI;QACtB,GACA,kIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,kEAAkE;IAClE,MAAM,mBAAkB,aAAqB;QAC3C,OAAO,kIAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,gBAAgB,EAAE,eAAe,EAClC;YACE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,eAAe;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,SAAS,IAAI;QACtB,GACA,kIAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,uDAAuD;IACvD,MAAM,iCAAgC,aAAqB,EAAE,SAAkB;QAC7E,IAAI;YACF,MAAM,SAAS,YAAY;gBAAE;YAAU,IAAI,CAAC;YAC5C,QAAQ,GAAG,CAAC,mDAAmD,eAAe,gBAAgB;YAE9F,6BAA6B;YAC7B,IAAI;gBACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,qBAAqB,CAAC,EAAE;oBAAE;gBAAO;gBAGxH,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBACtD,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBACtE,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO;oBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;oBAC7D,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,IAAI,CAAC,4CAA4C;gBAEzD,gCAAgC;gBAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;gBAC3F,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;gBAElD,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACjD,gDAAgD;oBAChD,IAAI,aAAa,SAAS,IAAI;oBAC9B,IAAI,WAAW;wBACb,aAAa,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,mBAAmB,KAAK;oBACpE;oBACA,QAAQ,GAAG,CAAC,8BAA8B,WAAW,MAAM,EAAE;oBAC7D,OAAO;gBACT,OAAO;oBACL,QAAQ,IAAI,CAAC,6CAA6C,SAAS,IAAI;oBACvE,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YAEd,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAoB,aAAqB,EAAE,SAAkB;QACjE,MAAM,SAAS,YAAY;YAAE;QAAU,IAAI,CAAC;QAC5C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,kBAAkB,CAAC,EAAE;YAAE;QAAO;QACrH,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1284, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/config/licenseTypeStepConfig.ts"], "sourcesContent": ["/**\n * License Type Step Configuration System\n * Defines which form steps are required for each license type\n */\n\nexport interface StepConfig {\n  id: string;\n  name: string;\n  component: string;\n  route: string;\n  required: boolean;\n  description: string;\n  estimatedTime: string; // in minutes\n}\n\nexport interface LicenseTypeStepConfig {\n  licenseTypeId: string;\n  name: string;\n  description: string;\n  steps: StepConfig[];\n  estimatedTotalTime: string;\n  requirements: string[];\n}\n\n// Base steps that can be used across license types\nconst BASE_STEPS: Record<string, StepConfig> = {\n  applicantInfo: {\n    id: 'applicant-info',\n    name: 'Applicant Information',\n    component: 'ApplicantInfo',\n    route: 'applicant-info',\n    required: true,\n    description: 'Personal or company information of the applicant',\n    estimatedTime: '5'\n  },\n  companyProfile: {\n    id: 'company-profile',\n    name: 'Company Profile',\n    component: 'CompanyProfile',\n    route: 'company-profile',\n    required: true,\n    description: 'Company structure, shareholders, and directors',\n    estimatedTime: '10'\n  },\n  management: {\n    id: 'management',\n    name: 'Management Structure',\n    component: 'Management',\n    route: 'management',\n    required: false,\n    description: 'Management team and organizational structure',\n    estimatedTime: '8'\n  },\n  professionalServices: {\n    id: 'professional-services',\n    name: 'Professional Services',\n    component: 'ProfessionalServices',\n    route: 'professional-services',\n    required: false,\n    description: 'External consultants and service providers',\n    estimatedTime: '6'\n  },\n  businessInfo: {\n    id: 'business-info',\n    name: 'Business Information',\n    component: 'BusinessInfo',\n    route: 'business-info',\n    required: true,\n    description: 'Business description and operational plan',\n    estimatedTime: '7'\n  },\n  serviceScope: {\n    id: 'service-scope',\n    name: 'Service Scope',\n    component: 'ServiceScope',\n    route: 'service-scope',\n    required: true,\n    description: 'Services offered and geographic coverage',\n    estimatedTime: '8'\n  },\n  businessPlan: {\n    id: 'business-plan',\n    name: 'Business Plan',\n    component: 'BusinessPlan',\n    route: 'business-plan',\n    required: true,\n    description: 'Market analysis and financial projections',\n    estimatedTime: '15'\n  },\n  legalHistory: {\n    id: 'legal-history',\n    name: 'Legal History',\n    component: 'LegalHistory',\n    route: 'legal-history',\n    required: true,\n    description: 'Legal compliance and regulatory history',\n    estimatedTime: '5'\n  },\n  reviewSubmit: {\n    id: 'review-submit',\n    name: 'Review & Submit',\n    component: 'ReviewSubmit',\n    route: 'review-submit',\n    required: true,\n    description: 'Review all information and submit application',\n    estimatedTime: '10'\n  }\n};\n\n// License type specific configurations\nexport const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {\n  telecommunications: {\n    licenseTypeId: 'telecommunications',\n    name: 'Telecommunications License',\n    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '74 minutes',\n    requirements: [\n      'Business registration certificate',\n      'Tax compliance certificate',\n      'Technical specifications',\n      'Financial statements',\n      'Management CVs',\n      'Network coverage plans'\n    ]\n  },\n\n  postal_services: {\n    licenseTypeId: 'postal_services',\n    name: 'Postal Services License',\n    description: 'License for postal and courier service providers',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '42 minutes',\n    requirements: [\n      'Business registration certificate',\n      'Fleet inventory',\n      'Service coverage map',\n      'Insurance certificates',\n      'Premises documentation'\n    ]\n  },\n\n  standards_compliance: {\n    licenseTypeId: 'standards_compliance',\n    name: 'Standards Compliance License',\n    description: 'License for standards compliance and certification services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '59 minutes',\n    requirements: [\n      'Accreditation certificates',\n      'Technical competency proof',\n      'Quality management system',\n      'Laboratory facilities documentation',\n      'Staff qualifications'\n    ]\n  },\n\n  broadcasting: {\n    licenseTypeId: 'broadcasting',\n    name: 'Broadcasting License',\n    description: 'License for radio and television broadcasting services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '63 minutes',\n    requirements: [\n      'Broadcasting equipment specifications',\n      'Content programming plan',\n      'Studio facility documentation',\n      'Transmission coverage maps',\n      'Local content compliance plan'\n    ]\n  },\n\n  spectrum_management: {\n    licenseTypeId: 'spectrum_management',\n    name: 'Spectrum Management License',\n    description: 'License for radio frequency spectrum management and allocation',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '74 minutes',\n    requirements: [\n      'Spectrum usage plan',\n      'Technical interference analysis',\n      'Equipment type approval',\n      'Frequency coordination agreements',\n      'Monitoring capabilities documentation'\n    ]\n  },\n\n  clf: {\n    licenseTypeId: 'clf',\n    name: 'CLF License',\n    description: 'Consumer Lending and Finance license',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '50 minutes',\n    requirements: [\n      'Financial institution license',\n      'Capital adequacy documentation',\n      'Risk management framework',\n      'Consumer protection policies',\n      'Anti-money laundering procedures'\n    ]\n  }\n};\n\n// License type name to config key mapping\nconst LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {\n  'telecommunications': 'telecommunications',\n  'postal services': 'postal_services',\n  'postal_services': 'postal_services',\n  'standards compliance': 'standards_compliance',\n  'standards_compliance': 'standards_compliance',\n  'broadcasting': 'broadcasting',\n  'spectrum management': 'spectrum_management',\n  'spectrum_management': 'spectrum_management',\n  'clf': 'clf',\n  'consumer lending and finance': 'clf'\n};\n\n// Helper functions\nexport const getLicenseTypeStepConfig = (licenseTypeId: string): LicenseTypeStepConfig | null => {\n  console.log('getLicenseTypeStepConfig called with:', licenseTypeId);\n  console.log('Available configs:', Object.keys(LICENSE_TYPE_STEP_CONFIGS));\n  console.log('UUID to code map:', licenseTypeUUIDToCodeMap);\n\n  // First try direct lookup\n  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];\n  if (config) {\n    console.log('Found config via direct lookup:', config.name);\n    return config;\n  }\n\n  // Try normalized lookup\n  const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');\n  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];\n  if (config) {\n    console.log('Found config via normalized lookup:', config.name);\n    return config;\n  }\n\n  // Try name mapping\n  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];\n  if (mappedKey) {\n    console.log('Found config via name mapping:', mappedKey);\n    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];\n  }\n\n  // If licenseTypeId looks like a UUID, try to get the code from license types\n  if (isUUID(licenseTypeId)) {\n    console.log('Detected UUID, trying to get code...');\n    const code = getLicenseTypeCodeFromUUID(licenseTypeId);\n    console.log('Got code from UUID:', code);\n    if (code) {\n      const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];\n      if (foundConfig) {\n        console.log('Found config via UUID mapping:', foundConfig.name);\n        return foundConfig;\n      }\n    }\n  }\n\n  console.log('No config found for license type:', licenseTypeId);\n  return null;\n};\n\n// Helper function to check if a string is a UUID\nconst isUUID = (str: string): boolean => {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(str);\n};\n\n// Helper function to get license type code from UUID\n// This will be populated by the license type service\nlet licenseTypeUUIDToCodeMap: Record<string, string> = {};\n\nexport const setLicenseTypeUUIDToCodeMap = (map: Record<string, string>) => {\n  licenseTypeUUIDToCodeMap = map;\n};\n\nconst getLicenseTypeCodeFromUUID = (uuid: string): string | null => {\n  return licenseTypeUUIDToCodeMap[uuid] || null;\n};\n\nexport const getStepByRoute = (licenseTypeId: string, stepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  return config.steps.find(step => step.route === stepRoute) || null;\n};\n\nexport const getStepByIndex = (licenseTypeId: string, stepIndex: number): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config || stepIndex < 0 || stepIndex >= config.steps.length) return null;\n  \n  return config.steps[stepIndex];\n};\n\nexport const getStepIndex = (licenseTypeId: string, stepRoute: string): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return -1;\n  \n  return config.steps.findIndex(step => step.route === stepRoute);\n};\n\nexport const getTotalSteps = (licenseTypeId: string): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.length : 0;\n};\n\nexport const getRequiredSteps = (licenseTypeId: string): StepConfig[] => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.filter(step => step.required) : [];\n};\n\nexport const getOptionalSteps = (licenseTypeId: string): StepConfig[] => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.filter(step => !step.required) : [];\n};\n\nexport const calculateProgress = (licenseTypeId: string, completedSteps: string[]): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return 0;\n  \n  const totalSteps = config.steps.length;\n  const completed = completedSteps.length;\n  \n  return Math.round((completed / totalSteps) * 100);\n};\n\nexport const getNextStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\n  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;\n  \n  return config.steps[currentIndex + 1];\n};\n\nexport const getPreviousStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\n  if (currentIndex <= 0) return null;\n  \n  return config.steps[currentIndex - 1];\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;AAqBD,mDAAmD;AACnD,MAAM,aAAyC;IAC7C,eAAe;QACb,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,gBAAgB;QACd,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,sBAAsB;QACpB,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,4BAAmE;IAC9E,oBAAoB;QAClB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,iBAAiB;QACf,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,sBAAsB;QACpB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,cAAc;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,qBAAqB;QACnB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,KAAK;QACH,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA,0CAA0C;AAC1C,MAAM,4BAAoD;IACxD,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,gBAAgB;IAChB,uBAAuB;IACvB,uBAAuB;IACvB,OAAO;IACP,gCAAgC;AAClC;AAGO,MAAM,2BAA2B,CAAC;IACvC,QAAQ,GAAG,CAAC,yCAAyC;IACrD,QAAQ,GAAG,CAAC,sBAAsB,OAAO,IAAI,CAAC;IAC9C,QAAQ,GAAG,CAAC,qBAAqB;IAEjC,0BAA0B;IAC1B,IAAI,SAAS,yBAAyB,CAAC,cAAc;IACrD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,mCAAmC,OAAO,IAAI;QAC1D,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,eAAe,cAAc,WAAW,GAAG,OAAO,CAAC,cAAc;IACvE,SAAS,yBAAyB,CAAC,aAAa;IAChD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,uCAAuC,OAAO,IAAI;QAC9D,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM,YAAY,yBAAyB,CAAC,aAAa;IACzD,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO,yBAAyB,CAAC,UAAU;IAC7C;IAEA,6EAA6E;IAC7E,IAAI,OAAO,gBAAgB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,2BAA2B;QACxC,QAAQ,GAAG,CAAC,uBAAuB;QACnC,IAAI,MAAM;YACR,MAAM,cAAc,yBAAyB,CAAC,KAAK;YACnD,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC,kCAAkC,YAAY,IAAI;gBAC9D,OAAO;YACT;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,qCAAqC;IACjD,OAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,SAAS,CAAC;IACd,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEA,qDAAqD;AACrD,qDAAqD;AACrD,IAAI,2BAAmD,CAAC;AAEjD,MAAM,8BAA8B,CAAC;IAC1C,2BAA2B;AAC7B;AAEA,MAAM,6BAA6B,CAAC;IAClC,OAAO,wBAAwB,CAAC,KAAK,IAAI;AAC3C;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,cAAc;AAChE;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,UAAU,YAAY,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO;IAEzE,OAAO,OAAO,KAAK,CAAC,UAAU;AAChC;AAEO,MAAM,eAAe,CAAC,eAAuB;IAClD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO,CAAC;IAErB,OAAO,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AACvD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,GAAG;AACxC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI,EAAE;AACjE;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,IAAI,EAAE;AAClE;AAEO,MAAM,oBAAoB,CAAC,eAAuB;IACvD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa,OAAO,KAAK,CAAC,MAAM;IACtC,MAAM,YAAY,eAAe,MAAM;IAEvC,OAAO,KAAK,KAAK,CAAC,AAAC,YAAY,aAAc;AAC/C;AAEO,MAAM,cAAc,CAAC,eAAuB;IACjD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,aAAa,eAAe;IACjD,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO;IAE3E,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAEO,MAAM,kBAAkB,CAAC,eAAuB;IACrD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,aAAa,eAAe;IACjD,IAAI,gBAAgB,GAAG,OAAO;IAE9B,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC", "debugId": null}}, {"offset": {"line": 1648, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useLicenseData.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { useState, useEffect, useCallback } from 'react';\r\n\r\nimport { licenseTypeService, LicenseType } from '@/services/licenseTypeService';\r\nimport { licenseCategoryService, LicenseCategory } from '@/services/licenseCategoryService';\r\nimport { setLicenseTypeUUIDToCodeMap } from '@/config/licenseTypeStepConfig';\r\n\r\ninterface UseLicenseTypesReturn {\r\n  licenseTypes: LicenseType[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  refetch: () => void;\r\n}\r\n\r\ninterface UseLicenseCategoriesReturn {\r\n  categories: LicenseCategory[];\r\n  loading: boolean;\r\n  error: string | null;\r\n  refetch: () => void;\r\n  getCategoriesByType: (licenseTypeId: string) => LicenseCategory[];\r\n}\r\n\r\nexport const useLicenseTypes = (): UseLicenseTypesReturn => {\r\n  const [licenseTypes, setLicenseTypes] = useState<LicenseType[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchLicenseTypes = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await licenseTypeService.getAllLicenseTypes();\r\n      setLicenseTypes(data);\r\n\r\n      // Create UUID to code mapping for step configuration\r\n      const uuidToCodeMap: Record<string, string> = {};\r\n      console.log('License types received:', data);\r\n      data.forEach(licenseType => {\r\n        console.log('Processing license type:', licenseType.name, 'code:', licenseType.code, 'id:', licenseType.license_type_id);\r\n        if (licenseType.code) {\r\n          uuidToCodeMap[licenseType.license_type_id] = licenseType.code;\r\n        }\r\n      });\r\n      console.log('Setting UUID to code map:', uuidToCodeMap);\r\n      setLicenseTypeUUIDToCodeMap(uuidToCodeMap);\r\n    } catch (err: any) {\r\n      let errorMessage = 'Failed to fetch license types';\r\n\r\n      // Handle rate limiting specifically\r\n      if (err.response?.status === 429) {\r\n        errorMessage = 'Too many requests. Please wait a moment and try again.';\r\n        console.warn('Rate limit hit for license types, using cached data if available');\r\n\r\n        // Try to use any cached data as fallback\r\n        try {\r\n          const cachedData = await licenseTypeService.getAllLicenseTypes();\r\n          if (cachedData && cachedData.length > 0) {\r\n            setLicenseTypes(cachedData);\r\n            setError(null);\r\n            return;\r\n          }\r\n        } catch (cacheErr) {\r\n          console.error('No cached data available:', cacheErr);\r\n        }\r\n      } else {\r\n        errorMessage = err.response?.data?.message || errorMessage;\r\n      }\r\n\r\n      setError(errorMessage);\r\n      console.error('Error fetching license types:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchLicenseTypes();\r\n  }, []);\r\n\r\n  return {\r\n    licenseTypes,\r\n    loading,\r\n    error,\r\n    refetch: fetchLicenseTypes,\r\n  };\r\n};\r\n\r\nexport const useLicenseCategories = (): UseLicenseCategoriesReturn => {\r\n  const [categories, setCategories] = useState<LicenseCategory[]>([]);\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  const fetchCategories = async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const data = await licenseCategoryService.getAllLicenseCategories();\r\n      setCategories(data);\r\n    } catch (err: any) {\r\n      let errorMessage = 'Failed to fetch license categories';\r\n\r\n      // Handle rate limiting specifically\r\n      if (err.response?.status === 429) {\r\n        errorMessage = 'Too many requests. Please wait a moment and try again.';\r\n        console.warn('Rate limit hit for license categories, using cached data if available');\r\n\r\n        // Try to use any cached data as fallback\r\n        try {\r\n          const cachedData = await licenseCategoryService.getAllLicenseCategories();\r\n          if (cachedData && cachedData.length > 0) {\r\n            setCategories(cachedData);\r\n            setError(null);\r\n            return;\r\n          }\r\n        } catch (cacheErr) {\r\n          console.error('No cached data available:', cacheErr);\r\n        }\r\n      } else {\r\n        errorMessage = err.response?.data?.message || errorMessage;\r\n      }\r\n\r\n      setError(errorMessage);\r\n      console.error('Error fetching license categories:', err);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const getCategoriesByType = useCallback((licenseTypeId: string): LicenseCategory[] => {\r\n    return categories.filter(category => category.license_type_id === licenseTypeId);\r\n  }, [categories]);\r\n\r\n  useEffect(() => {\r\n    fetchCategories();\r\n  }, []);\r\n\r\n  return {\r\n    categories,\r\n    loading,\r\n    error,\r\n    refetch: fetchCategories,\r\n    getCategoriesByType,\r\n  };\r\n};\r\n\r\n// Combined hook for both license types and categories\r\nexport const useLicenseData = () => {\r\n  const licenseTypesData = useLicenseTypes();\r\n  const categoriesData = useLicenseCategories();\r\n\r\n  const loading = licenseTypesData.loading || categoriesData.loading;\r\n  const error = licenseTypesData.error || categoriesData.error;\r\n\r\n  const getLicenseTypeWithCategories = (licenseTypeId: string) => {\r\n    const licenseType = licenseTypesData.licenseTypes.find(lt => lt.license_type_id === licenseTypeId);\r\n    const categories = categoriesData.getCategoriesByType(licenseTypeId);\r\n\r\n    return {\r\n      licenseType,\r\n      categories,\r\n    };\r\n  };\r\n\r\n  const refetch = () => {\r\n    licenseTypesData.refetch();\r\n    categoriesData.refetch();\r\n  };\r\n\r\n  return {\r\n    licenseTypes: licenseTypesData.licenseTypes,\r\n    categories: categoriesData.categories,\r\n    loading,\r\n    error,\r\n    refetch,\r\n    getLicenseTypeWithCategories,\r\n    getCategoriesByType: categoriesData.getCategoriesByType,\r\n  };\r\n};\r\n\r\n// Hook specifically for postal/courier license data\r\nexport const usePostalCourierLicenses = () => {\r\n  const { licenseTypes, categories, loading, error, refetch } = useLicenseData();\r\n\r\n  // Find postal/courier license types\r\n  const postalCourierTypes = licenseTypes.filter(lt =>\r\n    lt.name.toLowerCase().includes('postal') ||\r\n    lt.name.toLowerCase().includes('courier') ||\r\n    lt.name.toLowerCase().includes('mail')\r\n  );\r\n\r\n  // Get categories for postal/courier license types\r\n  const postalCourierCategories = categories.filter(cat =>\r\n    postalCourierTypes.some(lt => lt.license_type_id === cat.license_type_id)\r\n  );\r\n\r\n  return {\r\n    licenseTypes: postalCourierTypes,\r\n    categories: postalCourierCategories,\r\n    loading,\r\n    error,\r\n    refetch,\r\n  };\r\n};\r\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AACA;AACA;;AANA;;;;;AAuBO,MAAM,kBAAkB;;IAC7B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,oBAAoB;QACxB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM,wIAAA,CAAA,qBAAkB,CAAC,kBAAkB;YACxD,gBAAgB;YAEhB,qDAAqD;YACrD,MAAM,gBAAwC,CAAC;YAC/C,QAAQ,GAAG,CAAC,2BAA2B;YACvC,KAAK,OAAO,CAAC,CAAA;gBACX,QAAQ,GAAG,CAAC,4BAA4B,YAAY,IAAI,EAAE,SAAS,YAAY,IAAI,EAAE,OAAO,YAAY,eAAe;gBACvH,IAAI,YAAY,IAAI,EAAE;oBACpB,aAAa,CAAC,YAAY,eAAe,CAAC,GAAG,YAAY,IAAI;gBAC/D;YACF;YACA,QAAQ,GAAG,CAAC,6BAA6B;YACzC,CAAA,GAAA,yIAAA,CAAA,8BAA2B,AAAD,EAAE;QAC9B,EAAE,OAAO,KAAU;YACjB,IAAI,eAAe;YAEnB,oCAAoC;YACpC,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBAChC,eAAe;gBACf,QAAQ,IAAI,CAAC;gBAEb,yCAAyC;gBACzC,IAAI;oBACF,MAAM,aAAa,MAAM,wIAAA,CAAA,qBAAkB,CAAC,kBAAkB;oBAC9D,IAAI,cAAc,WAAW,MAAM,GAAG,GAAG;wBACvC,gBAAgB;wBAChB,SAAS;wBACT;oBACF;gBACF,EAAE,OAAO,UAAU;oBACjB,QAAQ,KAAK,CAAC,6BAA6B;gBAC7C;YACF,OAAO;gBACL,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YAChD;YAEA,SAAS;YACT,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA,SAAS;IACX;AACF;GA/Da;AAiEN,MAAM,uBAAuB;;IAClC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,kBAAkB;QACtB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,OAAO,MAAM,4IAAA,CAAA,yBAAsB,CAAC,uBAAuB;YACjE,cAAc;QAChB,EAAE,OAAO,KAAU;YACjB,IAAI,eAAe;YAEnB,oCAAoC;YACpC,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;gBAChC,eAAe;gBACf,QAAQ,IAAI,CAAC;gBAEb,yCAAyC;gBACzC,IAAI;oBACF,MAAM,aAAa,MAAM,4IAAA,CAAA,yBAAsB,CAAC,uBAAuB;oBACvE,IAAI,cAAc,WAAW,MAAM,GAAG,GAAG;wBACvC,cAAc;wBACd,SAAS;wBACT;oBACF;gBACF,EAAE,OAAO,UAAU;oBACjB,QAAQ,KAAK,CAAC,6BAA6B;gBAC7C;YACF,OAAO;gBACL,eAAe,IAAI,QAAQ,EAAE,MAAM,WAAW;YAChD;YAEA,SAAS;YACT,QAAQ,KAAK,CAAC,sCAAsC;QACtD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;iEAAE,CAAC;YACvC,OAAO,WAAW,MAAM;yEAAC,CAAA,WAAY,SAAS,eAAe,KAAK;;QACpE;gEAAG;QAAC;KAAW;IAEf,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;QACF;yCAAG,EAAE;IAEL,OAAO;QACL;QACA;QACA;QACA,SAAS;QACT;IACF;AACF;IAxDa;AA2DN,MAAM,iBAAiB;;IAC5B,MAAM,mBAAmB;IACzB,MAAM,iBAAiB;IAEvB,MAAM,UAAU,iBAAiB,OAAO,IAAI,eAAe,OAAO;IAClE,MAAM,QAAQ,iBAAiB,KAAK,IAAI,eAAe,KAAK;IAE5D,MAAM,+BAA+B,CAAC;QACpC,MAAM,cAAc,iBAAiB,YAAY,CAAC,IAAI,CAAC,CAAA,KAAM,GAAG,eAAe,KAAK;QACpF,MAAM,aAAa,eAAe,mBAAmB,CAAC;QAEtD,OAAO;YACL;YACA;QACF;IACF;IAEA,MAAM,UAAU;QACd,iBAAiB,OAAO;QACxB,eAAe,OAAO;IACxB;IAEA,OAAO;QACL,cAAc,iBAAiB,YAAY;QAC3C,YAAY,eAAe,UAAU;QACrC;QACA;QACA;QACA;QACA,qBAAqB,eAAe,mBAAmB;IACzD;AACF;IA/Ba;;QACc;QACF;;;AAgClB,MAAM,2BAA2B;;IACtC,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG;IAE9D,oCAAoC;IACpC,MAAM,qBAAqB,aAAa,MAAM,CAAC,CAAA,KAC7C,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,aAC/B,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,cAC/B,GAAG,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;IAGjC,kDAAkD;IAClD,MAAM,0BAA0B,WAAW,MAAM,CAAC,CAAA,MAChD,mBAAmB,IAAI,CAAC,CAAA,KAAM,GAAG,eAAe,KAAK,IAAI,eAAe;IAG1E,OAAO;QACL,cAAc;QACd,YAAY;QACZ;QACA;QACA;IACF;AACF;IAtBa;;QACmD", "debugId": null}}, {"offset": {"line": 1848, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationProgressService.ts"], "sourcesContent": ["/**\n * Application Progress Service\n * Manages step completion tracking and progress calculation for license applications\n */\n\nimport { getLicenseTypeStepConfig, calculateProgress } from '@/config/licenseTypeStepConfig';\n\nexport interface StepProgress {\n  stepId: string;\n  stepName: string;\n  completed: boolean;\n  completedAt?: Date;\n  data?: any;\n}\n\nexport interface ApplicationProgress {\n  applicationId: string;\n  licenseTypeId: string;\n  totalSteps: number;\n  completedSteps: number;\n  progressPercentage: number;\n  steps: StepProgress[];\n  lastUpdated: Date;\n}\n\nclass ApplicationProgressService {\n  private progressCache = new Map<string, ApplicationProgress>();\n\n  /**\n   * Initialize progress tracking for a new application\n   */\n  async initializeProgress(applicationId: string, licenseTypeId: string): Promise<ApplicationProgress> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      throw new Error(`Invalid license type: ${licenseTypeId}`);\n    }\n\n    const steps: StepProgress[] = licenseConfig.steps.map(step => ({\n      stepId: step.id,\n      stepName: step.name,\n      completed: false\n    }));\n\n    const progress: ApplicationProgress = {\n      applicationId,\n      licenseTypeId,\n      totalSteps: steps.length,\n      completedSteps: 0,\n      progressPercentage: 0,\n      steps,\n      lastUpdated: new Date()\n    };\n\n    this.progressCache.set(applicationId, progress);\n    await this.saveProgressToStorage(progress);\n    \n    return progress;\n  }\n\n  /**\n   * Mark a step as completed\n   */\n  async markStepCompleted(applicationId: string, stepId: string, data?: any): Promise<ApplicationProgress> {\n    let progress = await this.getProgress(applicationId);\n    \n    if (!progress) {\n      throw new Error(`No progress found for application: ${applicationId}`);\n    }\n\n    // Update the specific step\n    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);\n    if (stepIndex === -1) {\n      throw new Error(`Step not found: ${stepId}`);\n    }\n\n    if (!progress.steps[stepIndex].completed) {\n      progress.steps[stepIndex].completed = true;\n      progress.steps[stepIndex].completedAt = new Date();\n      progress.steps[stepIndex].data = data;\n\n      // Recalculate progress\n      progress.completedSteps = progress.steps.filter(step => step.completed).length;\n      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);\n      progress.lastUpdated = new Date();\n\n      // Update cache and storage\n      this.progressCache.set(applicationId, progress);\n      await this.saveProgressToStorage(progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Mark a step as incomplete (for editing)\n   */\n  async markStepIncomplete(applicationId: string, stepId: string): Promise<ApplicationProgress> {\n    let progress = await this.getProgress(applicationId);\n    \n    if (!progress) {\n      throw new Error(`No progress found for application: ${applicationId}`);\n    }\n\n    // Update the specific step\n    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);\n    if (stepIndex === -1) {\n      throw new Error(`Step not found: ${stepId}`);\n    }\n\n    if (progress.steps[stepIndex].completed) {\n      progress.steps[stepIndex].completed = false;\n      progress.steps[stepIndex].completedAt = undefined;\n      progress.steps[stepIndex].data = undefined;\n\n      // Recalculate progress\n      progress.completedSteps = progress.steps.filter(step => step.completed).length;\n      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);\n      progress.lastUpdated = new Date();\n\n      // Update cache and storage\n      this.progressCache.set(applicationId, progress);\n      await this.saveProgressToStorage(progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Get current progress for an application\n   */\n  async getProgress(applicationId: string): Promise<ApplicationProgress | null> {\n    // Check cache first\n    if (this.progressCache.has(applicationId)) {\n      return this.progressCache.get(applicationId)!;\n    }\n\n    // Load from storage\n    const progress = await this.loadProgressFromStorage(applicationId);\n    if (progress) {\n      this.progressCache.set(applicationId, progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Get completed step IDs for an application\n   */\n  async getCompletedStepIds(applicationId: string): Promise<string[]> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return [];\n\n    return progress.steps\n      .filter(step => step.completed)\n      .map(step => step.stepId);\n  }\n\n  /**\n   * Check if a specific step is completed\n   */\n  async isStepCompleted(applicationId: string, stepId: string): Promise<boolean> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return false;\n\n    const step = progress.steps.find(s => s.stepId === stepId);\n    return step?.completed || false;\n  }\n\n  /**\n   * Get next incomplete step\n   */\n  async getNextIncompleteStep(applicationId: string): Promise<StepProgress | null> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return null;\n\n    return progress.steps.find(step => !step.completed) || null;\n  }\n\n  /**\n   * Calculate overall application completion status\n   */\n  async getApplicationStatus(applicationId: string): Promise<'not_started' | 'in_progress' | 'completed'> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return 'not_started';\n\n    if (progress.completedSteps === 0) return 'not_started';\n    if (progress.completedSteps === progress.totalSteps) return 'completed';\n    return 'in_progress';\n  }\n\n  /**\n   * Save progress to localStorage (in a real app, this would be an API call)\n   */\n  private async saveProgressToStorage(progress: ApplicationProgress): Promise<void> {\n    try {\n      const key = `application_progress_${progress.applicationId}`;\n      localStorage.setItem(key, JSON.stringify({\n        ...progress,\n        lastUpdated: progress.lastUpdated.toISOString()\n      }));\n    } catch (error) {\n      console.error('Error saving progress to storage:', error);\n    }\n  }\n\n  /**\n   * Load progress from localStorage (in a real app, this would be an API call)\n   */\n  private async loadProgressFromStorage(applicationId: string): Promise<ApplicationProgress | null> {\n    try {\n      const key = `application_progress_${applicationId}`;\n      const stored = localStorage.getItem(key);\n      \n      if (!stored) return null;\n\n      const parsed = JSON.parse(stored);\n      return {\n        ...parsed,\n        lastUpdated: new Date(parsed.lastUpdated),\n        steps: parsed.steps.map((step: any) => ({\n          ...step,\n          completedAt: step.completedAt ? new Date(step.completedAt) : undefined\n        }))\n      };\n    } catch (error) {\n      console.error('Error loading progress from storage:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Clear progress cache (useful for testing or when switching applications)\n   */\n  clearCache(): void {\n    this.progressCache.clear();\n  }\n\n  /**\n   * Delete progress for an application\n   */\n  async deleteProgress(applicationId: string): Promise<void> {\n    this.progressCache.delete(applicationId);\n    \n    try {\n      const key = `application_progress_${applicationId}`;\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Error deleting progress from storage:', error);\n    }\n  }\n}\n\n// Export singleton instance\nexport const applicationProgressService = new ApplicationProgressService();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAoBA,MAAM;IACI,gBAAgB,IAAI,MAAmC;IAE/D;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAE,aAAqB,EAAgC;QACnG,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,eAAe;QAC1D;QAEA,MAAM,QAAwB,cAAc,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC7D,QAAQ,KAAK,EAAE;gBACf,UAAU,KAAK,IAAI;gBACnB,WAAW;YACb,CAAC;QAED,MAAM,WAAgC;YACpC;YACA;YACA,YAAY,MAAM,MAAM;YACxB,gBAAgB;YAChB,oBAAoB;YACpB;YACA,aAAa,IAAI;QACnB;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;QACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAEjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,kBAAkB,aAAqB,EAAE,MAAc,EAAE,IAAU,EAAgC;QACvG,IAAI,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,eAAe;QACvE;QAEA,2BAA2B;QAC3B,MAAM,YAAY,SAAS,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACnE,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;QAC7C;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE;YACxC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG;YACtC,SAAS,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI;YAC5C,SAAS,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG;YAEjC,uBAAuB;YACvB,SAAS,cAAc,GAAG,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC9E,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI;YAC3F,SAAS,WAAW,GAAG,IAAI;YAE3B,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;YACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAE,MAAc,EAAgC;QAC5F,IAAI,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,eAAe;QACvE;QAEA,2BAA2B;QAC3B,MAAM,YAAY,SAAS,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACnE,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;QAC7C;QAEA,IAAI,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE;YACvC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG;YACtC,SAAS,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG;YACxC,SAAS,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG;YAEjC,uBAAuB;YACvB,SAAS,cAAc,GAAG,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC9E,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI;YAC3F,SAAS,WAAW,GAAG,IAAI;YAE3B,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;YACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,YAAY,aAAqB,EAAuC;QAC5E,oBAAoB;QACpB,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB;YACzC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAChC;QAEA,oBAAoB;QACpB,MAAM,WAAW,MAAM,IAAI,CAAC,uBAAuB,CAAC;QACpD,IAAI,UAAU;YACZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;QACxC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,oBAAoB,aAAqB,EAAqB;QAClE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,OAAO,SAAS,KAAK,CAClB,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAC7B,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;IAC5B;IAEA;;GAEC,GACD,MAAM,gBAAgB,aAAqB,EAAE,MAAc,EAAoB;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACnD,OAAO,MAAM,aAAa;IAC5B;IAEA;;GAEC,GACD,MAAM,sBAAsB,aAAqB,EAAgC;QAC/E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,KAAK;IACzD;IAEA;;GAEC,GACD,MAAM,qBAAqB,aAAqB,EAAwD;QACtG,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,IAAI,SAAS,cAAc,KAAK,GAAG,OAAO;QAC1C,IAAI,SAAS,cAAc,KAAK,SAAS,UAAU,EAAE,OAAO;QAC5D,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,sBAAsB,QAA6B,EAAiB;QAChF,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,SAAS,aAAa,EAAE;YAC5D,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;gBACvC,GAAG,QAAQ;gBACX,aAAa,SAAS,WAAW,CAAC,WAAW;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA;;GAEC,GACD,MAAc,wBAAwB,aAAqB,EAAuC;QAChG,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,eAAe;YACnD,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,OAAO;gBACL,GAAG,MAAM;gBACT,aAAa,IAAI,KAAK,OAAO,WAAW;gBACxC,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;wBACtC,GAAG,IAAI;wBACP,aAAa,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,IAAI;oBAC/D,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK;IAC1B;IAEA;;GAEC,GACD,MAAM,eAAe,aAAqB,EAAiB;QACzD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAE1B,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,eAAe;YACnD,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;IACF;AACF;AAGO,MAAM,6BAA6B,IAAI", "debugId": null}}, {"offset": {"line": 2041, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/stepValidationService.ts"], "sourcesContent": ["/**\n * Step Validation Service\n * Handles step completion validation and navigation rules for license applications\n */\n\nimport { getLicenseTypeStepConfig, getStepIndex } from '@/config/licenseTypeStepConfig';\nimport { applicationProgressService } from './applicationProgressService';\n\nexport interface ValidationResult {\n  isValid: boolean;\n  errors: string[];\n  warnings: string[];\n}\n\nexport interface NavigationValidation {\n  canNavigateToStep: boolean;\n  reason?: string;\n  requiredSteps: string[];\n}\n\nclass StepValidationService {\n  /**\n   * Validate if user can navigate to a specific step\n   */\n  async validateStepNavigation(\n    applicationId: string,\n    licenseTypeId: string,\n    targetStepId: string\n  ): Promise<NavigationValidation> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Invalid license type',\n        requiredSteps: []\n      };\n    }\n\n    const targetStepIndex = getStepIndex(licenseTypeId, targetStepId);\n    if (targetStepIndex === -1) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Invalid step',\n        requiredSteps: []\n      };\n    }\n\n    // Get completed steps\n    const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);\n    \n    // Check if all required previous steps are completed\n    const requiredPreviousSteps = licenseConfig.steps\n      .slice(0, targetStepIndex)\n      .filter(step => step.required)\n      .map(step => step.id);\n\n    const missingRequiredSteps = requiredPreviousSteps.filter(\n      stepId => !completedStepIds.includes(stepId)\n    );\n\n    if (missingRequiredSteps.length > 0) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Required previous steps must be completed first',\n        requiredSteps: missingRequiredSteps\n      };\n    }\n\n    return {\n      canNavigateToStep: true,\n      requiredSteps: []\n    };\n  }\n\n  /**\n   * Validate if user can proceed to next step\n   */\n  async validateNextStepNavigation(\n    applicationId: string,\n    licenseTypeId: string,\n    currentStepId: string\n  ): Promise<NavigationValidation> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Invalid license type',\n        requiredSteps: []\n      };\n    }\n\n    const currentStepIndex = getStepIndex(licenseTypeId, currentStepId);\n    if (currentStepIndex === -1) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Invalid current step',\n        requiredSteps: []\n      };\n    }\n\n    // Check if current step is completed (if required)\n    const currentStep = licenseConfig.steps[currentStepIndex];\n    if (currentStep.required) {\n      const isCompleted = await applicationProgressService.isStepCompleted(applicationId, currentStepId);\n      if (!isCompleted) {\n        return {\n          canNavigateToStep: false,\n          reason: 'Current step must be completed before proceeding',\n          requiredSteps: [currentStepId]\n        };\n      }\n    }\n\n    // Check if there is a next step\n    if (currentStepIndex >= licenseConfig.steps.length - 1) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Already at the last step',\n        requiredSteps: []\n      };\n    }\n\n    return {\n      canNavigateToStep: true,\n      requiredSteps: []\n    };\n  }\n\n  /**\n   * Validate if user can go back to previous step\n   */\n  async validatePreviousStepNavigation(\n    applicationId: string,\n    licenseTypeId: string,\n    currentStepId: string\n  ): Promise<NavigationValidation> {\n    const currentStepIndex = getStepIndex(licenseTypeId, currentStepId);\n    \n    if (currentStepIndex <= 0) {\n      return {\n        canNavigateToStep: false,\n        reason: 'Already at the first step',\n        requiredSteps: []\n      };\n    }\n\n    return {\n      canNavigateToStep: true,\n      requiredSteps: []\n    };\n  }\n\n  /**\n   * Get the next available step that the user can navigate to\n   */\n  async getNextAvailableStep(\n    applicationId: string,\n    licenseTypeId: string\n  ): Promise<string | null> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) return null;\n\n    const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);\n    \n    // Find the first incomplete required step\n    for (const step of licenseConfig.steps) {\n      if (step.required && !completedStepIds.includes(step.id)) {\n        return step.id;\n      }\n    }\n\n    // If all required steps are completed, find the first incomplete optional step\n    for (const step of licenseConfig.steps) {\n      if (!step.required && !completedStepIds.includes(step.id)) {\n        return step.id;\n      }\n    }\n\n    // All steps completed\n    return null;\n  }\n\n  /**\n   * Validate application completion\n   */\n  async validateApplicationCompletion(\n    applicationId: string,\n    licenseTypeId: string\n  ): Promise<ValidationResult> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      return {\n        isValid: false,\n        errors: ['Invalid license type'],\n        warnings: []\n      };\n    }\n\n    const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);\n    const requiredSteps = licenseConfig.steps.filter(step => step.required);\n    const missingRequiredSteps = requiredSteps.filter(step => !completedStepIds.includes(step.id));\n\n    const errors: string[] = [];\n    const warnings: string[] = [];\n\n    if (missingRequiredSteps.length > 0) {\n      errors.push(`Missing required steps: ${missingRequiredSteps.map(s => s.name).join(', ')}`);\n    }\n\n    const optionalSteps = licenseConfig.steps.filter(step => !step.required);\n    const missingOptionalSteps = optionalSteps.filter(step => !completedStepIds.includes(step.id));\n\n    if (missingOptionalSteps.length > 0) {\n      warnings.push(`Optional steps not completed: ${missingOptionalSteps.map(s => s.name).join(', ')}`);\n    }\n\n    return {\n      isValid: errors.length === 0,\n      errors,\n      warnings\n    };\n  }\n\n  /**\n   * Get step completion requirements\n   */\n  async getStepRequirements(\n    licenseTypeId: string,\n    stepId: string\n  ): Promise<{\n    isRequired: boolean;\n    dependencies: string[];\n    description: string;\n    estimatedTime: string;\n  } | null> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) return null;\n\n    const step = licenseConfig.steps.find(s => s.id === stepId);\n    if (!step) return null;\n\n    const stepIndex = getStepIndex(licenseTypeId, stepId);\n    const dependencies = licenseConfig.steps\n      .slice(0, stepIndex)\n      .filter(s => s.required)\n      .map(s => s.id);\n\n    return {\n      isRequired: step.required,\n      dependencies,\n      description: step.description,\n      estimatedTime: step.estimatedTime\n    };\n  }\n\n  /**\n   * Check if application is ready for submission\n   */\n  async isReadyForSubmission(\n    applicationId: string,\n    licenseTypeId: string\n  ): Promise<boolean> {\n    const validation = await this.validateApplicationCompletion(applicationId, licenseTypeId);\n    return validation.isValid;\n  }\n}\n\n// Export singleton instance\nexport const stepValidationService = new StepValidationService();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;AACA;;;AAcA,MAAM;IACJ;;GAEC,GACD,MAAM,uBACJ,aAAqB,EACrB,aAAqB,EACrB,YAAoB,EACW;QAC/B,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,MAAM,kBAAkB,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;QACpD,IAAI,oBAAoB,CAAC,GAAG;YAC1B,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,sBAAsB;QACtB,MAAM,mBAAmB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,mBAAmB,CAAC;QAE9E,qDAAqD;QACrD,MAAM,wBAAwB,cAAc,KAAK,CAC9C,KAAK,CAAC,GAAG,iBACT,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAC5B,GAAG,CAAC,CAAA,OAAQ,KAAK,EAAE;QAEtB,MAAM,uBAAuB,sBAAsB,MAAM,CACvD,CAAA,SAAU,CAAC,iBAAiB,QAAQ,CAAC;QAGvC,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACnC,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe;YACjB;QACF;QAEA,OAAO;YACL,mBAAmB;YACnB,eAAe,EAAE;QACnB;IACF;IAEA;;GAEC,GACD,MAAM,2BACJ,aAAqB,EACrB,aAAqB,EACrB,aAAqB,EACU;QAC/B,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,MAAM,mBAAmB,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;QACrD,IAAI,qBAAqB,CAAC,GAAG;YAC3B,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,mDAAmD;QACnD,MAAM,cAAc,cAAc,KAAK,CAAC,iBAAiB;QACzD,IAAI,YAAY,QAAQ,EAAE;YACxB,MAAM,cAAc,MAAM,gJAAA,CAAA,6BAA0B,CAAC,eAAe,CAAC,eAAe;YACpF,IAAI,CAAC,aAAa;gBAChB,OAAO;oBACL,mBAAmB;oBACnB,QAAQ;oBACR,eAAe;wBAAC;qBAAc;gBAChC;YACF;QACF;QAEA,gCAAgC;QAChC,IAAI,oBAAoB,cAAc,KAAK,CAAC,MAAM,GAAG,GAAG;YACtD,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,OAAO;YACL,mBAAmB;YACnB,eAAe,EAAE;QACnB;IACF;IAEA;;GAEC,GACD,MAAM,+BACJ,aAAqB,EACrB,aAAqB,EACrB,aAAqB,EACU;QAC/B,MAAM,mBAAmB,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;QAErD,IAAI,oBAAoB,GAAG;YACzB,OAAO;gBACL,mBAAmB;gBACnB,QAAQ;gBACR,eAAe,EAAE;YACnB;QACF;QAEA,OAAO;YACL,mBAAmB;YACnB,eAAe,EAAE;QACnB;IACF;IAEA;;GAEC,GACD,MAAM,qBACJ,aAAqB,EACrB,aAAqB,EACG;QACxB,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe,OAAO;QAE3B,MAAM,mBAAmB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,mBAAmB,CAAC;QAE9E,0CAA0C;QAC1C,KAAK,MAAM,QAAQ,cAAc,KAAK,CAAE;YACtC,IAAI,KAAK,QAAQ,IAAI,CAAC,iBAAiB,QAAQ,CAAC,KAAK,EAAE,GAAG;gBACxD,OAAO,KAAK,EAAE;YAChB;QACF;QAEA,+EAA+E;QAC/E,KAAK,MAAM,QAAQ,cAAc,KAAK,CAAE;YACtC,IAAI,CAAC,KAAK,QAAQ,IAAI,CAAC,iBAAiB,QAAQ,CAAC,KAAK,EAAE,GAAG;gBACzD,OAAO,KAAK,EAAE;YAChB;QACF;QAEA,sBAAsB;QACtB,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,8BACJ,aAAqB,EACrB,aAAqB,EACM;QAC3B,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,OAAO;gBACL,SAAS;gBACT,QAAQ;oBAAC;iBAAuB;gBAChC,UAAU,EAAE;YACd;QACF;QAEA,MAAM,mBAAmB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,mBAAmB,CAAC;QAC9E,MAAM,gBAAgB,cAAc,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;QACtE,MAAM,uBAAuB,cAAc,MAAM,CAAC,CAAA,OAAQ,CAAC,iBAAiB,QAAQ,CAAC,KAAK,EAAE;QAE5F,MAAM,SAAmB,EAAE;QAC3B,MAAM,WAAqB,EAAE;QAE7B,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACnC,OAAO,IAAI,CAAC,CAAC,wBAAwB,EAAE,qBAAqB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO;QAC3F;QAEA,MAAM,gBAAgB,cAAc,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ;QACvE,MAAM,uBAAuB,cAAc,MAAM,CAAC,CAAA,OAAQ,CAAC,iBAAiB,QAAQ,CAAC,KAAK,EAAE;QAE5F,IAAI,qBAAqB,MAAM,GAAG,GAAG;YACnC,SAAS,IAAI,CAAC,CAAC,8BAA8B,EAAE,qBAAqB,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,EAAE,IAAI,CAAC,OAAO;QACnG;QAEA,OAAO;YACL,SAAS,OAAO,MAAM,KAAK;YAC3B;YACA;QACF;IACF;IAEA;;GAEC,GACD,MAAM,oBACJ,aAAqB,EACrB,MAAc,EAMN;QACR,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe,OAAO;QAE3B,MAAM,OAAO,cAAc,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QACpD,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,YAAY,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;QAC9C,MAAM,eAAe,cAAc,KAAK,CACrC,KAAK,CAAC,GAAG,WACT,MAAM,CAAC,CAAA,IAAK,EAAE,QAAQ,EACtB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAEhB,OAAO;YACL,YAAY,KAAK,QAAQ;YACzB;YACA,aAAa,KAAK,WAAW;YAC7B,eAAe,KAAK,aAAa;QACnC;IACF;IAEA;;GAEC,GACD,MAAM,qBACJ,aAAqB,EACrB,aAAqB,EACH;QAClB,MAAM,aAAa,MAAM,IAAI,CAAC,6BAA6B,CAAC,eAAe;QAC3E,OAAO,WAAW,OAAO;IAC3B;AACF;AAGO,MAAM,wBAAwB,IAAI", "debugId": null}}, {"offset": {"line": 2236, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/TextInput.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  className?: string;\n  containerClassName?: string;\n}\n\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  className = '',\n  containerClassName = '',\n  id,\n  ...props\n}, ref) => {\n  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseInputClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n  `;\n  \n  const inputClasses = error\n    ? `${baseInputClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseInputClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={inputId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <input\n        ref={ref}\n        id={inputId}\n        className={`${inputClasses} ${className}`}\n        {...props}\n      />\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nTextInput.displayName = 'TextInput';\n\nexport default TextInput;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,EAAE,EACF,GAAG,OACJ,EAAE;IACD,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAExE,MAAM,mBAAmB,CAAC;;;;;;EAM1B,CAAC;IAED,MAAM,eAAe,QACjB,GAAG,iBAAiB,2EAA2E,CAAC,GAChG,GAAG,iBAAiB,0GAA0G,CAAC;IAEnI,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,GAAG,aAAa,CAAC,EAAE,WAAW;gBACxC,GAAG,KAAK;;;;;;YAGV,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 2333, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface SelectOption {\n  value: string;\n  label: string;\n  disabled?: boolean;\n}\n\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  options: SelectOption[];\n  placeholder?: string;\n  className?: string;\n  containerClassName?: string;\n  onChange?: (value: string) => void;\n}\n\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  options = [],\n  placeholder = 'Select an option...',\n  className = '',\n  containerClassName = '',\n  onChange,\n  id,\n  value,\n  ...props\n}, ref) => {\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseSelectClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  `;\n  \n  const selectClasses = error\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    if (onChange) {\n      onChange(e.target.value);\n    }\n  };\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={selectId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <select\n          ref={ref}\n          id={selectId}\n          value={value || ''}\n          onChange={handleChange}\n          className={`${selectClasses} ${className}`}\n          {...props}\n        >\n          {placeholder && (\n            <option value=\"\" disabled>\n              {placeholder}\n            </option>\n          )}\n          \n          {options.map((option) => (\n            <option \n              key={option.value} \n              value={option.value}\n              disabled={option.disabled}\n            >\n              {option.label}\n            </option>\n          ))}\n        </select>\n        \n        {/* Custom dropdown arrow */}\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\n        </div>\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nSelect.displayName = 'Select';\n\nexport default Select;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,oBAAoB,CAAC;;;;;;;;;EAS3B,CAAC;IAED,MAAM,gBAAgB,QAClB,GAAG,kBAAkB,2EAA2E,CAAC,GACjG,GAAG,kBAAkB,0GAA0G,CAAC;IAEpI,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW;wBACzC,GAAG,KAAK;;4BAER,6BACC,6LAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 2483, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formValidation.ts"], "sourcesContent": ["// Form validation utilities\r\n\r\nexport interface ValidationRule {\r\n  required?: boolean;\r\n  minLength?: number;\r\n  maxLength?: number;\r\n  pattern?: RegExp;\r\n  custom?: (value: any) => string | null;\r\n}\r\n\r\nexport interface ValidationErrors {\r\n  [key: string]: string;\r\n}\r\n\r\nexport const validateField = (value: any, rules: ValidationRule): string | null => {\r\n  // Required validation\r\n  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\r\n    return 'This field is required';\r\n  }\r\n\r\n  // Skip other validations if field is empty and not required\r\n  if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n    return null;\r\n  }\r\n\r\n  // String validations\r\n  if (typeof value === 'string') {\r\n    // Min length validation\r\n    if (rules.minLength && value.length < rules.minLength) {\r\n      return `Must be at least ${rules.minLength} characters`;\r\n    }\r\n\r\n    // Max length validation\r\n    if (rules.maxLength && value.length > rules.maxLength) {\r\n      return `Must be no more than ${rules.maxLength} characters`;\r\n    }\r\n\r\n    // Pattern validation\r\n    if (rules.pattern && !rules.pattern.test(value)) {\r\n      return 'Invalid format';\r\n    }\r\n  }\r\n\r\n  // Custom validation\r\n  if (rules.custom) {\r\n    return rules.custom(value);\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport const validateForm = (data: any, rules: { [key: string]: ValidationRule }): ValidationErrors => {\r\n  const errors: ValidationErrors = {};\r\n\r\n  Object.keys(rules).forEach(field => {\r\n    const error = validateField(data[field], rules[field]);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n// Common validation patterns\r\nexport const patterns = {\r\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n  phone: /^(\\+265|0)[0-9]{8,9}$/,\r\n  url: /^https?:\\/\\/.+/,\r\n  alphanumeric: /^[a-zA-Z0-9]+$/,\r\n  alphabetic: /^[a-zA-Z\\s]+$/,\r\n  numeric: /^[0-9]+$/,\r\n  percentage: /^(100|[1-9]?[0-9])$/\r\n};\r\n\r\n// Common validation rules\r\nexport const commonRules = {\r\n  required: { required: true },\r\n  email: { \r\n    required: true, \r\n    pattern: patterns.email,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.email.test(value)) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  phone: {\r\n    required: true,\r\n    pattern: patterns.phone,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.phone.test(value)) {\r\n        return 'Please enter a valid Malawi phone number (e.g., +265123456789 or 0123456789)';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  businessRegistration: {\r\n    required: true,\r\n    minLength: 5,\r\n    custom: (value: string) => {\r\n      if (value && value.length < 5) {\r\n        return 'Business registration number must be at least 5 characters';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  percentage: {\r\n    pattern: patterns.percentage,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.percentage.test(value)) {\r\n        return 'Please enter a valid percentage (0-100)';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n// Utility function to check if form has errors\r\nexport const hasErrors = (errors: ValidationErrors): boolean => {\r\n  return Object.keys(errors).length > 0;\r\n};\r\n\r\n// Utility function to get first error message\r\nexport const getFirstError = (errors: ValidationErrors): string | null => {\r\n  const firstKey = Object.keys(errors)[0];\r\n  return firstKey ? errors[firstKey] : null;\r\n};\r\n\r\n// Utility function to validate array fields\r\nexport const validateArrayField = (\r\n  array: any[], \r\n  rules: { [key: string]: ValidationRule },\r\n  minItems?: number,\r\n  maxItems?: number\r\n): { [key: string]: ValidationErrors } => {\r\n  const arrayErrors: { [key: string]: ValidationErrors } = {};\r\n\r\n  // Check array length\r\n  if (minItems && array.length < minItems) {\r\n    arrayErrors._array = { length: `Must have at least ${minItems} items` };\r\n  }\r\n  if (maxItems && array.length > maxItems) {\r\n    arrayErrors._array = { length: `Must have no more than ${maxItems} items` };\r\n  }\r\n\r\n  // Validate each item in array\r\n  array.forEach((item, index) => {\r\n    const itemErrors = validateForm(item, rules);\r\n    if (hasErrors(itemErrors)) {\r\n      arrayErrors[index] = itemErrors;\r\n    }\r\n  });\r\n\r\n  return arrayErrors;\r\n};\r\n\r\n// File validation\r\nexport const validateFile = (\r\n  file: File | null, \r\n  required: boolean = false,\r\n  maxSize: number = 10, // MB\r\n  allowedTypes: string[] = ['.pdf']\r\n): string | null => {\r\n  if (required && !file) {\r\n    return 'File is required';\r\n  }\r\n\r\n  if (!file) {\r\n    return null;\r\n  }\r\n\r\n  // Check file size\r\n  if (file.size > maxSize * 1024 * 1024) {\r\n    return `File size must be less than ${maxSize}MB`;\r\n  }\r\n\r\n  // Check file type\r\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\r\n  if (!allowedTypes.includes(fileExtension)) {\r\n    return `File type must be: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n// Section validation interface\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  errors: Record<string, string>;\r\n}\r\n\r\n// Validate section data for application forms\r\nexport const validateSection = (data: Record<string, any>, sectionName: string): ValidationResult => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  switch (sectionName) {\r\n    case 'applicantInfo':\r\n      // Required fields for applicant info\r\n      const applicantRequiredFields = [\r\n        'applicant_type', 'first_name', 'last_name', 'email', 'phone',\r\n        'national_id', 'date_of_birth', 'nationality', 'gender',\r\n        'postal_address', 'physical_address', 'city', 'district'\r\n      ];\r\n\r\n      applicantRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\r\n        errors.email = 'Please enter a valid email address';\r\n      }\r\n\r\n      // Phone validation\r\n      if (data.phone && !/^(\\+265|0)?[1-9]\\d{7,8}$/.test(data.phone)) {\r\n        errors.phone = 'Please enter a valid phone number';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'companyProfile':\r\n      const companyRequiredFields = [\r\n        'company_name', 'business_registration_number', 'tax_number', 'company_type',\r\n        'incorporation_date', 'incorporation_place', 'company_email', 'company_phone',\r\n        'company_address', 'company_city', 'company_district', 'number_of_employees',\r\n        'annual_revenue', 'business_description'\r\n      ];\r\n\r\n      companyRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.company_email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.company_email)) {\r\n        errors.company_email = 'Please enter a valid email address';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'businessInfo':\r\n      const businessRequiredFields = [\r\n        'business_model', 'operational_structure', 'target_market', 'competitive_advantage',\r\n        'facilities_description', 'equipment_description', 'operational_areas',\r\n        'service_delivery_model', 'quality_assurance', 'customer_support'\r\n      ];\r\n\r\n      businessRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'serviceScope':\r\n      const serviceScopeRequiredFields = [\r\n        'services_offered', 'geographic_coverage', 'service_categories',\r\n        'target_customers', 'service_capacity'\r\n      ];\r\n\r\n      serviceScopeRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'businessPlan':\r\n      const businessPlanRequiredFields = [\r\n        'executive_summary', 'market_analysis', 'financial_projections',\r\n        'revenue_model', 'investment_requirements', 'implementation_timeline',\r\n        'risk_analysis', 'success_metrics'\r\n      ];\r\n\r\n      businessPlanRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'legalHistory':\r\n      // Required fields\r\n      if (!data.compliance_record || data.compliance_record.trim() === '') {\r\n        errors.compliance_record = 'Compliance record is required';\r\n      }\r\n\r\n      // Declaration must be accepted\r\n      if (!data.declaration_accepted) {\r\n        errors.declaration_accepted = 'You must accept the declaration to proceed';\r\n      }\r\n\r\n      // Conditional validations\r\n      if (data.criminal_history && (!data.criminal_details || data.criminal_details.trim() === '')) {\r\n        errors.criminal_details = 'Please provide details of your criminal history';\r\n      }\r\n\r\n      if (data.bankruptcy_history && (!data.bankruptcy_details || data.bankruptcy_details.trim() === '')) {\r\n        errors.bankruptcy_details = 'Please provide details of your bankruptcy history';\r\n      }\r\n\r\n      if (data.regulatory_actions && (!data.regulatory_details || data.regulatory_details.trim() === '')) {\r\n        errors.regulatory_details = 'Please provide details of regulatory actions';\r\n      }\r\n\r\n      if (data.litigation_history && (!data.litigation_details || data.litigation_details.trim() === '')) {\r\n        errors.litigation_details = 'Please provide details of litigation history';\r\n      }\r\n\r\n      break;\r\n\r\n    default:\r\n      // No validation for unknown sections\r\n      break;\r\n  }\r\n\r\n  return {\r\n    isValid: Object.keys(errors).length === 0,\r\n    errors\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;AAcrB,MAAM,gBAAgB,CAAC,OAAY;IACxC,sBAAsB;IACtB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAG,GAAG;QACpF,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;QAChE,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,OAAO,UAAU,UAAU;QAC7B,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,iBAAiB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QACzD;QAEA,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,qBAAqB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QAC7D;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,IAAI,MAAM,MAAM,EAAE;QAChB,OAAO,MAAM,MAAM,CAAC;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,MAAW;IACtC,MAAM,SAA2B,CAAC;IAElC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACzB,MAAM,QAAQ,cAAc,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM;QACrD,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,OAAO;IACP,KAAK;IACL,cAAc;IACd,YAAY;IACZ,SAAS;IACT,YAAY;AACd;AAGO,MAAM,cAAc;IACzB,UAAU;QAAE,UAAU;IAAK;IAC3B,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,sBAAsB;QACpB,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;YACP,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,YAAY;QACV,SAAS,SAAS,UAAU;QAC5B,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,UAAU,CAAC,IAAI,CAAC,QAAQ;gBAC7C,OAAO;YACT;YACA,OAAO;QACT;IACF;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG;AACtC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,WAAW,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACvC,OAAO,WAAW,MAAM,CAAC,SAAS,GAAG;AACvC;AAGO,MAAM,qBAAqB,CAChC,OACA,OACA,UACA;IAEA,MAAM,cAAmD,CAAC;IAE1D,qBAAqB;IACrB,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC;QAAC;IACxE;IACA,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC;QAAC;IAC5E;IAEA,8BAA8B;IAC9B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,UAAU,aAAa;YACzB,WAAW,CAAC,MAAM,GAAG;QACvB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAC1B,MACA,WAAoB,KAAK,EACzB,UAAkB,EAAE,EACpB,eAAyB;IAAC;CAAO;IAEjC,IAAI,YAAY,CAAC,MAAM;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;QACrC,OAAO,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;IACnD;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IACxD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB;QACzC,OAAO,CAAC,mBAAmB,EAAE,aAAa,IAAI,CAAC,OAAO;IACxD;IAEA,OAAO;AACT;AASO,MAAM,kBAAkB,CAAC,MAA2B;IACzD,MAAM,SAAiC,CAAC;IAExC,OAAQ;QACN,KAAK;YACH,qCAAqC;YACrC,MAAM,0BAA0B;gBAC9B;gBAAkB;gBAAc;gBAAa;gBAAS;gBACtD;gBAAe;gBAAiB;gBAAe;gBAC/C;gBAAkB;gBAAoB;gBAAQ;aAC/C;YAED,wBAAwB,OAAO,CAAC,CAAA;gBAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAChE,OAAO,KAAK,GAAG;YACjB;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,2BAA2B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAC9D,OAAO,KAAK,GAAG;YACjB;YAEA;QAEF,KAAK;YACH,MAAM,wBAAwB;gBAC5B;gBAAgB;gBAAgC;gBAAc;gBAC9D;gBAAsB;gBAAuB;gBAAiB;gBAC9D;gBAAmB;gBAAgB;gBAAoB;gBACvD;gBAAkB;aACnB;YAED,sBAAsB,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,aAAa,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,aAAa,GAAG;gBAChF,OAAO,aAAa,GAAG;YACzB;YAEA;QAEF,KAAK;YACH,MAAM,yBAAyB;gBAC7B;gBAAkB;gBAAyB;gBAAiB;gBAC5D;gBAA0B;gBAAyB;gBACnD;gBAA0B;gBAAqB;aAChD;YAED,uBAAuB,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAoB;gBAAuB;gBAC3C;gBAAoB;aACrB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAqB;gBAAmB;gBACxC;gBAAiB;gBAA2B;gBAC5C;gBAAiB;aAClB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,kBAAkB;YAClB,IAAI,CAAC,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,IAAI,OAAO,IAAI;gBACnE,OAAO,iBAAiB,GAAG;YAC7B;YAEA,+BAA+B;YAC/B,IAAI,CAAC,KAAK,oBAAoB,EAAE;gBAC9B,OAAO,oBAAoB,GAAG;YAChC;YAEA,0BAA0B;YAC1B,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAC5F,OAAO,gBAAgB,GAAG;YAC5B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA;QAEF;YAEE;IACJ;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 2788, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationFormDataService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { processApiResponse } from '@/lib/authUtils';\n\nexport interface ApplicationFormData {\n  form_data_id?: string;\n  application_id: string;\n  section_name: string;\n  section_data: Record<string, any>;\n  completed: boolean;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport const applicationFormDataService = {\n  // Save form section data\n  async saveFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to saveFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to saveFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Saving form section ${sectionName} for application ${applicationId}:`, sectionData);\n\n      const response = await apiClient.post('/application-form-data', {\n        application_id: applicationId,\n        section_name: sectionName,\n        section_data: sectionData,\n        completed: true\n      });\n\n      console.log(`Form section ${sectionName} saved successfully:`, response.data);\n      return processApiResponse(response);\n    } catch (error) {\n      console.error(`Error saving form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Update existing form section data\n  async updateFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to updateFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to updateFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Updating form section ${sectionName} for application ${applicationId}:`, sectionData);\n\n      const response = await apiClient.put(`/application-form-data/${applicationId}/${sectionName}`, {\n        section_data: sectionData,\n        completed: true\n      });\n\n      console.log(`Form section ${sectionName} updated successfully:`, response.data);\n      return processApiResponse(response);\n    } catch (error) {\n      console.error(`Error updating form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Get form section data\n  async getFormSection(applicationId: string, sectionName: string): Promise<ApplicationFormData | null> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      console.warn('Invalid applicationId provided to getFormSection:', applicationId);\n      return null;\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      console.warn('Invalid sectionName provided to getFormSection:', sectionName);\n      return null;\n    }\n\n    try {\n      console.log(`Fetching form section ${sectionName} for application ${applicationId}`);\n      const response = await apiClient.get(`/application-form-data/${applicationId}/${sectionName}`);\n      return processApiResponse(response);\n    } catch (error) {\n      if ((error as any)?.response?.status === 404) {\n        console.log(`Form section ${sectionName} not found for application ${applicationId} - this is normal for new applications`);\n        return null; // Section doesn't exist yet\n      }\n      console.error(`Error fetching form section ${sectionName} for application ${applicationId}:`, error);\n      throw error;\n    }\n  },\n\n  // Get all form data for an application\n  async getApplicationFormData(applicationId: string): Promise<Record<string, any>> {\n    try {\n      const response = await apiClient.get(`/application-form-data/${applicationId}`);\n      const processedResponse = processApiResponse(response);\n\n      // Convert array of sections to object\n      const formData: Record<string, any> = {};\n      if (Array.isArray(processedResponse)) {\n        processedResponse.forEach((section: ApplicationFormData) => {\n          formData[section.section_name] = section.section_data;\n        });\n      }\n\n      return formData;\n    } catch (error) {\n      console.error('Error fetching application form data:', error);\n      return {}; // Return empty object if no data found\n    }\n  },\n\n  // Delete form section\n  async deleteFormSection(applicationId: string, sectionName: string): Promise<{ message: string }> {\n    try {\n      const response = await apiClient.delete(`/application-form-data/${applicationId}/${sectionName}`);\n      return response.data;\n    } catch (error) {\n      console.error(`Error deleting form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Save or update form section (upsert)\n  async saveOrUpdateFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before proceeding\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to saveOrUpdateFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to saveOrUpdateFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Saving/updating form section ${sectionName} for application ${applicationId}`);\n\n      // Try to get existing section first\n      const existingSection = await this.getFormSection(applicationId, sectionName);\n\n      if (existingSection) {\n        // Update existing section\n        console.log(`Updating existing section ${sectionName}`);\n        return await this.updateFormSection(applicationId, sectionName, sectionData);\n      } else {\n        // Create new section\n        console.log(`Creating new section ${sectionName}`);\n        return await this.saveFormSection(applicationId, sectionName, sectionData);\n      }\n    } catch (error) {\n      console.error(`Error saving/updating form section ${sectionName}:`, error);\n      throw error;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAYO,MAAM,6BAA6B;IACxC,yBAAyB;IACzB,MAAM,iBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,mDAAmD,EAAE,eAAe;QACvF;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,iDAAiD,EAAE,aAAa;QACnF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAEpF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,0BAA0B;gBAC9D,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,oBAAoB,CAAC,EAAE,SAAS,IAAI;YAC5E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC3D,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,MAAM,mBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,qDAAqD,EAAE,eAAe;QACzF;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,mDAAmD,EAAE,aAAa;QACrF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAEtF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa,EAAE;gBAC7F,cAAc;gBACd,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,sBAAsB,CAAC,EAAE,SAAS,IAAI;YAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC7D,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,gBAAe,aAAqB,EAAE,WAAmB;QAC7D,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,QAAQ,IAAI,CAAC,qDAAqD;YAClE,OAAO;QACT;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,QAAQ,IAAI,CAAC,mDAAmD;YAChE,OAAO;QACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,iBAAiB,EAAE,eAAe;YACnF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,IAAI,AAAC,OAAe,UAAU,WAAW,KAAK;gBAC5C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,2BAA2B,EAAE,cAAc,sCAAsC,CAAC;gBAC1H,OAAO,MAAM,4BAA4B;YAC3C;YACA,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAC9F,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,MAAM,wBAAuB,aAAqB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,eAAe;YAC9E,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,sCAAsC;YACtC,MAAM,WAAgC,CAAC;YACvC,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBACpC,kBAAkB,OAAO,CAAC,CAAC;oBACzB,QAAQ,CAAC,QAAQ,YAAY,CAAC,GAAG,QAAQ,YAAY;gBACvD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO,CAAC,GAAG,uCAAuC;QACpD;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,aAAqB,EAAE,WAAmB;QAChE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa;YAChG,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC7D,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,MAAM,yBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,oCAAoC;QACpC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,2DAA2D,EAAE,eAAe;QAC/F;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,yDAAyD,EAAE,aAAa;QAC3F;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,iBAAiB,EAAE,eAAe;YAE1F,oCAAoC;YACpC,MAAM,kBAAkB,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe;YAEjE,IAAI,iBAAiB;gBACnB,0BAA0B;gBAC1B,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,aAAa;gBACtD,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,aAAa;YAClE,OAAO;gBACL,qBAAqB;gBACrB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,aAAa;gBACjD,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,aAAa;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,YAAY,CAAC,CAAC,EAAE;YACpE,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 2931, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    console.log('ApplicationService.createApplication called with:', data);\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ApplicationService.createApplication error:', error);\r\n      console.error('Error response:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return response.data;\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      console.log('Creating application with data:', data);\r\n\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      console.log('Creating application with:', {\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id,\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft'\r\n      });\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 14 // ~1/7 steps completed\r\n      });\r\n\r\n      console.log('Application created:', application);\r\n\r\n      // Save applicant form data separately if needed\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        await applicationFormDataService.saveFormSection(\r\n          application.application_id,\r\n          'applicantInfo',\r\n          data.applicant_data\r\n        );\r\n        console.log('Applicant form data saved');\r\n      } catch (formDataError) {\r\n        console.warn('Could not save form data separately, continuing...', formDataError);\r\n        // Continue even if form data saving fails\r\n      }\r\n\r\n      console.log('Application created successfully');\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n\r\n        // Save section data using form data service\r\n        await applicationFormDataService.saveOrUpdateFormSection(\r\n          applicationId,\r\n          sectionName,\r\n          sectionData\r\n        );\r\n\r\n        // Get all form data to calculate progress\r\n        const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n        completedSections = Object.keys(allFormData).length;\r\n\r\n        console.log(`Form data saved using form data service`);\r\n      } catch (formDataError) {\r\n        console.warn('Form data service not available, using basic progress tracking:', formDataError);\r\n\r\n        // Fallback: estimate progress based on section name\r\n        const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n        const sectionIndex = sectionOrder.indexOf(sectionName);\r\n        completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n      }\r\n\r\n      // Calculate progress based on completed sections\r\n      const totalSections = 7; // Total number of form sections\r\n      const progressPercentage = Math.round((completedSections / totalSections) * 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Get all applications - the backend should filter by authenticated user\r\n      const response = await apiClient.get('/applications');\r\n\r\n      console.log('Applications API response:', response.data);\r\n\r\n      // Handle different response structures\r\n      let applications = [];\r\n      if (response.data?.data) {\r\n        applications = Array.isArray(response.data.data) ? response.data.data : [];\r\n      } else if (Array.isArray(response.data)) {\r\n        applications = response.data;\r\n      } else if (response.data) {\r\n        // Single application or other structure\r\n        applications = [response.data];\r\n      }\r\n\r\n      console.log('Processed applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get form data from the form data service\r\n      let formData: Record<string, any> = {};\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        formData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n      } catch (formDataError) {\r\n        console.warn('Could not retrieve form data for validation:', formDataError);\r\n        // Continue with empty form data\r\n      }\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,QAAQ,GAAG,CAAC,qDAAqD;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ,KAAK,CAAC,mBAAoB,OAAe,UAAU;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;YACV;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,GAAG,uBAAuB;YACjD;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,gDAAgD;YAChD,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,MAAM,2BAA2B,eAAe,CAC9C,YAAY,cAAc,EAC1B,iBACA,KAAK,cAAc;gBAErB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,sDAAsD;YACnE,0CAA0C;YAC5C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBAEvC,4CAA4C;gBAC5C,MAAM,2BAA2B,uBAAuB,CACtD,eACA,aACA;gBAGF,0CAA0C;gBAC1C,MAAM,cAAc,MAAM,2BAA2B,sBAAsB,CAAC;gBAC5E,oBAAoB,OAAO,IAAI,CAAC,aAAa,MAAM;gBAEnD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,mEAAmE;gBAEhF,oDAAoD;gBACpD,MAAM,eAAe;oBAAC;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAgB;oBAAgB;iBAAe;gBACxI,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAC7D;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,GAAG,gCAAgC;YACzD,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB;YAE5E,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,SAAS,IAAI;YAChE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YAEvD,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,SAAS,IAAI,EAAE,MAAM;gBACvB,eAAe,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,EAAE;YAC5E,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACvC,eAAe,SAAS,IAAI;YAC9B,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,wCAAwC;gBACxC,eAAe;oBAAC,SAAS,IAAI;iBAAC;YAChC;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,2CAA2C;YAC3C,IAAI,WAAgC,CAAC;YACrC,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,WAAW,MAAM,2BAA2B,sBAAsB,CAAC;YACrE,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,gDAAgD;YAC7D,gCAAgC;YAClC;YAEA,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 3230, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicantService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { Applicant } from '../types/license';\n\nexport interface CreateApplicantData {\n  name: string;\n  business_registration_number: string;\n  tpin: string;\n  website: string;\n  email: string;\n  phone: string;\n  fax?: string;\n  level_of_insurance_cover?: string;\n  address_id?: string;\n  contact_id?: string;\n  date_incorporation: string; // Changed from Date to string to match backend DTO\n  place_incorporation: string;\n}\n\nexport const applicantService = {\n  // Create new applicant\n  async createApplicant(data: CreateApplicantData): Promise<Applicant> {\n    try {\n      console.log('Creating applicant with data:', data);\n\n      const response = await apiClient.post('/applicants', data);\n      // Handle different response formats\n      if (response.data) {\n        // Check if it's a standard success response format\n        if (response.data.success !== undefined && response.data.data) {\n          console.log('Standard response format detected');\n          return response.data.data;\n        }\n        // Check if it's direct data format\n        else if (response.data.applicant_id || response.data.id) {\n          console.log('Direct data format detected');\n          return response.data;\n        }\n        // Fallback: assume it's the applicant data\n        else {\n          console.log('Fallback: treating response.data as applicant');\n          return response.data;\n        }\n      }\n\n      throw new Error('Invalid response format from applicant creation');\n    } catch (error) {\n      console.error('Error creating applicant:', error);\n      console.error('Error details:', (error as any)?.response?.data);\n      throw error;\n    }\n  },\n\n  // Get applicant by ID\n  async getApplicant(id: string): Promise<Applicant> {\n    try {\n      const response = await apiClient.get(`/applicants/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching applicant:', error);\n      throw error;\n    }\n  },\n\n  // Update applicant\n  async updateApplicant(id: string, data: Partial<CreateApplicantData>): Promise<Applicant> {\n    try {\n      console.log('Updating applicant:', id, data);\n      \n      const response = await apiClient.put(`/applicants/${id}`, data);\n      \n      console.log('Applicant updated successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating applicant:', error);\n      throw error;\n    }\n  },\n\n  // Get applicants by user (if user can have multiple applicants)\n  async getApplicantsByUser(): Promise<Applicant[]> {\n    try {\n      const response = await apiClient.get('/applicants/by-user');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user applicants:', error);\n      throw error;\n    }\n  },\n\n  // Delete applicant\n  async deleteApplicant(id: string): Promise<{ message: string }> {\n    try {\n      const response = await apiClient.delete(`/applicants/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting applicant:', error);\n      throw error;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AAkBO,MAAM,mBAAmB;IAC9B,uBAAuB;IACvB,MAAM,iBAAgB,IAAyB;QAC7C,IAAI;YACF,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,eAAe;YACrD,oCAAoC;YACpC,IAAI,SAAS,IAAI,EAAE;gBACjB,mDAAmD;gBACnD,IAAI,SAAS,IAAI,CAAC,OAAO,KAAK,aAAa,SAAS,IAAI,CAAC,IAAI,EAAE;oBAC7D,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAEK,IAAI,SAAS,IAAI,CAAC,YAAY,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACvD,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI;gBACtB,OAEK;oBACH,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI;gBACtB;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,QAAQ,KAAK,CAAC,kBAAmB,OAAe,UAAU;YAC1D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YACxD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAkC;QAClE,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB,IAAI;YAEvC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YAE1D,QAAQ,GAAG,CAAC,mCAAmC,SAAS,IAAI;YAC5D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,gEAAgE;IAChE,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 3314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/ApplicantInfo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { useRouter } from 'next/navigation';\nimport TextInput from '@/components/common/TextInput';\nimport Select from '@/components/common/Select';\nimport { validateSection } from '@/utils/formValidation';\nimport { applicationFormDataService } from '@/services/applicationFormDataService';\nimport { applicationService } from '@/services/applicationService';\nimport { applicantService } from '@/services/applicantService';\nimport { applicationProgressService } from '@/services/applicationProgressService';\nimport { IndependentStepProps } from '../types';\n\ninterface ApplicantInfoProps extends IndependentStepProps {}\n\nconst ApplicantInfo: React.FC<ApplicantInfoProps> = ({\n  applicationId,\n  licenseTypeId,\n  licenseCategoryId,\n  isEditMode = false,\n  onStepComplete,\n  onStepError,\n  onNavigate\n}) => {\n  const router = useRouter();\n\n  // Debug logging for props\n  console.log('ApplicantInfo component props:', {\n    applicationId,\n    licenseTypeId,\n    licenseCategoryId,\n    isEditMode\n  });\n\n  const [localData, setLocalData] = useState({\n    applicant_type: '',\n    first_name: '',\n    last_name: '',\n    middle_name: '',\n    email: '',\n    phone: '',\n    national_id: '',\n    date_of_birth: '',\n    nationality: 'Malawian',\n    gender: '',\n    postal_address: '',\n    physical_address: '',\n    city: '',\n    district: '',\n    postal_code: ''\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [applicationCreated, setApplicationCreated] = useState(false);\n  const [createdApplicationId, setCreatedApplicationId] = useState<string | null>(null);\n\n  // Load existing data when component mounts (for edit mode)\n  useEffect(() => {\n    const loadExistingData = async () => {\n      // Add more robust validation for applicationId\n      if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {\n        console.log('Loading existing data for application:', applicationId);\n        setIsLoading(true);\n        try {\n          // Load data from multiple sources since ApplicantInfo saves to different places\n          const dataPromises = [];\n\n          // 1. Load form data (individual person fields)\n          dataPromises.push(\n            applicationFormDataService.getFormSection(applicationId, 'applicantInfo')\n              .then(data => ({ source: 'formData', data }))\n              .catch(error => {\n                console.warn('Could not load form data:', error);\n                return { source: 'formData', data: null };\n              })\n          );\n\n          // 2. Load application data (includes applicant relation)\n          dataPromises.push(\n            applicationService.getApplication(applicationId)\n              .then(data => ({ source: 'application', data }))\n              .catch(error => {\n                console.warn('Could not load application data:', error);\n                return { source: 'application', data: null };\n              })\n          );\n\n          const results = await Promise.all(dataPromises);\n\n          // Merge data from different sources\n          let mergedData = { ...localData };\n\n          for (const result of results) {\n            if (result.data) {\n              if (result.source === 'formData') {\n                // Type guard: check if it's form data with section_data\n                const formData = result.data as any;\n                if (formData && formData.section_data) {\n                  console.log('Loaded form data:', formData.section_data);\n                  mergedData = { ...mergedData, ...formData.section_data };\n                }\n              } else if (result.source === 'application') {\n                // Type guard: check if it's application data with applicant\n                const applicationData = result.data as any;\n                if (applicationData && applicationData.applicant) {\n                  const applicant = applicationData.applicant;\n                  console.log('Loaded application with applicant:', applicant);\n\n                  // Map applicant business data to form fields where applicable\n                  if (applicant.name && !mergedData.first_name && !mergedData.last_name) {\n                    // If no individual names, try to split business name\n                    const nameParts = applicant.name.split(' ');\n                    if (nameParts.length >= 2) {\n                      mergedData.first_name = nameParts[0];\n                      mergedData.last_name = nameParts.slice(1).join(' ');\n                    } else {\n                      mergedData.first_name = applicant.name;\n                    }\n                  }\n\n                  if (applicant.email && !mergedData.email) {\n                    mergedData.email = applicant.email;\n                  }\n\n                  if (applicant.phone && !mergedData.phone) {\n                    mergedData.phone = applicant.phone;\n                  }\n\n                  if (applicant.place_incorporation && !mergedData.city) {\n                    mergedData.city = applicant.place_incorporation;\n                  }\n                }\n              }\n            }\n          }\n\n          console.log('Merged applicant data from all sources:', mergedData);\n          setLocalData(mergedData);\n\n        } catch (error) {\n          console.error('Error loading existing applicant data:', error);\n          // Don't call onStepError as it would hide the form\n          // Just log the error and continue with empty form\n          console.log('Continuing with empty form due to load error');\n        } finally {\n          setIsLoading(false);\n        }\n      } else {\n        console.log('Skipping data load - not in edit mode or invalid applicationId:', { isEditMode, applicationId });\n      }\n    };\n\n    loadExistingData();\n  }, [applicationId, isEditMode]);\n\n  // Handle local changes\n  const handleLocalChange = useCallback((field: string, value: any) => {\n    setLocalData((prev: any) => ({ ...prev, [field]: value }));\n    setHasUnsavedChanges(true);\n\n    // Clear validation error for this field if it exists\n    if (validationErrors && validationErrors[field]) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));\n    }\n\n    // Clear save error when user starts making changes\n    if (validationErrors.save) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, save: '' }));\n    }\n  }, [validationErrors]);\n\n  // Validate form data\n  const validateForm = () => {\n    const validation = validateSection(localData, 'applicantInfo');\n    setValidationErrors(validation.errors);\n    return validation.isValid;\n  };\n\n  // Save data to backend\n  const saveData = async () => {\n    const validation = validateSection(localData, 'applicantInfo');\n    setValidationErrors(validation.errors);\n\n    if (!validation.isValid) {\n      // Don't call onStepError as it hides the form\n      // Instead, let the form show validation errors inline\n      console.log('Validation failed:', validation.errors);\n      return false;\n    }\n\n    setIsSaving(true);\n    try {\n      if (isEditMode && applicationId && applicationId !== 'new') {\n        // Update existing application data\n        await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'applicantInfo', localData);\n\n        // Mark step as completed in progress tracking\n        await applicationProgressService.markStepCompleted(applicationId, 'applicant-info', localData);\n\n        console.log('Applicant info updated for application:', applicationId);\n      } else {\n        // Create new applicant and application\n        // Map individual person data to business applicant structure\n        const applicantPayload = {\n          name: `${localData.first_name} ${localData.last_name}`.trim(),\n          email: localData.email,\n          phone: localData.phone,\n          business_registration_number: `BRN-${Date.now()}`,\n          tpin: `TPIN-${Date.now()}`,\n          website: 'https://example.com',\n          date_incorporation: new Date().toISOString(),\n          place_incorporation: localData.city || 'Malawi'\n        };\n\n        const createdApplicant = await applicantService.createApplicant(applicantPayload);\n\n\n\n        console.log('Created applicant response:', createdApplicant);\n        console.log('Applicant ID from response:', createdApplicant?.applicant_id);\n\n        // Validate that we got a valid applicant ID\n        if (!createdApplicant || !createdApplicant.applicant_id) {\n          throw new Error('Failed to create applicant: No applicant ID returned');\n        }\n\n        const applicationPayload = {\n          application_number: `APP-${Date.now()}`,\n          applicant_id: createdApplicant.applicant_id,\n          license_category_id: licenseCategoryId,\n          status: 'draft' as any\n        };\n\n        console.log('Creating application with payload:', applicationPayload);\n        const createdApplication = await applicationService.createApplication(applicationPayload);\n\n        console.log('Created application response:', createdApplication);\n        console.log('Application ID from response:', createdApplication?.application_id);\n\n        // Validate that we got a valid application ID\n        if (!createdApplication || !createdApplication.application_id) {\n          throw new Error('Failed to create application: No application ID returned');\n        }\n\n        const newApplicationId = createdApplication.application_id;\n        console.log('Using application ID:', newApplicationId);\n\n        // Save form data\n        await applicationFormDataService.saveOrUpdateFormSection(\n          newApplicationId,\n          'applicantInfo',\n          localData\n        );\n\n        console.log('New application created:', newApplicationId);\n\n        // Initialize progress tracking for the new application\n        await applicationProgressService.initializeProgress(newApplicationId, licenseTypeId);\n\n        // Mark this step as completed\n        await applicationProgressService.markStepCompleted(newApplicationId, 'applicant-info', localData);\n\n        console.log('Applicant info step completed, passing application ID to parent:', newApplicationId);\n\n        // Set application created state\n        setApplicationCreated(true);\n        setCreatedApplicationId(newApplicationId);\n\n        // Pass the application ID to the parent - let the parent handle navigation\n        onStepComplete?.('applicant-info', { ...localData, applicationId: newApplicationId });\n\n        // Don't navigate here - show continue button instead\n        return true;\n      }\n\n      setHasUnsavedChanges(false);\n      onStepComplete?.('applicant-info', localData);\n      return true;\n    } catch (error: any) {\n      console.error('Error saving applicant info:', error);\n      console.error('Error details:', {\n        message: error.message,\n        response: error.response?.data,\n        status: error.response?.status\n      });\n\n      // Extract meaningful error message\n      let errorMessage = 'Failed to save applicant information';\n      if (error.message && error.message.includes('Invalid applicationId')) {\n        errorMessage = 'Error creating application record. Please try again.';\n      } else if (error.response?.data?.message) {\n        errorMessage = error.response.data.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      // Don't call onStepError as it hides the form - instead show inline error\n      // onStepError?.('applicant-info', { save: errorMessage });\n\n      // Show error in the form itself\n      setValidationErrors({ save: errorMessage });\n      return false;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Handle save button click\n  const handleSave = async () => {\n    await saveData();\n  };\n\n  // Handle continue to next step\n  const handleContinueToNextStep = () => {\n    console.log('Continue to next step clicked');\n    console.log('Created application ID:', createdApplicationId);\n    console.log('License type ID:', licenseTypeId);\n    console.log('License category ID:', licenseCategoryId);\n\n    if (createdApplicationId) {\n      const nextStepUrl = `/customer/applications/apply/${licenseTypeId}/${licenseCategoryId}/company-profile?app=${createdApplicationId}`;\n      console.log('Navigating to next step URL:', nextStepUrl);\n      router.push(nextStepUrl);\n    } else {\n      console.error('No created application ID available for navigation');\n    }\n  };\n\n\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Applicant Information\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Please provide your personal information. This will create your application record.\n        </p>\n        {!applicationId && (\n          <div className=\"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n            <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n              <i className=\"ri-information-line mr-1\"></i>\n              Your application will be created when you save this step.\n            </p>\n          </div>\n        )}\n        {applicationId && (\n          <div className=\"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n            <p className=\"text-sm text-green-700 dark:text-green-300\">\n              <i className=\"ri-check-line mr-1\"></i>\n              Application created: {applicationId.slice(0, 8)}...\n            </p>\n          </div>\n        )}\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Applicant Type */}\n        <div className=\"md:col-span-2\">\n          <Select\n            label=\"Applicant Type\"\n            value={localData.applicant_type || ''}\n            onChange={(value) => handleLocalChange('applicant_type', value)}\n            options={[\n              { value: 'individual', label: 'Individual' },\n              { value: 'company', label: 'Company' },\n              { value: 'organization', label: 'Organization' }\n            ]}\n            required\n            error={validationErrors.applicant_type}\n          />\n        </div>\n\n        {/* Personal Information */}\n        <TextInput\n          label=\"First Name\"\n          value={localData.first_name || ''}\n          onChange={(e) => handleLocalChange('first_name', e.target.value)}\n          required\n          error={validationErrors.first_name}\n        />\n\n        <TextInput\n          label=\"Last Name\"\n          value={localData.last_name || ''}\n          onChange={(e) => handleLocalChange('last_name', e.target.value)}\n          required\n          error={validationErrors.last_name}\n        />\n\n        <TextInput\n          label=\"Middle Name\"\n          value={localData.middle_name || ''}\n          onChange={(e) => handleLocalChange('middle_name', e.target.value)}\n          error={validationErrors.middle_name}\n        />\n\n        <Select\n          label=\"Gender\"\n          value={localData.gender || ''}\n          onChange={(value) => handleLocalChange('gender', value)}\n          options={[\n            { value: 'male', label: 'Male' },\n            { value: 'female', label: 'Female' },\n            { value: 'other', label: 'Other' }\n          ]}\n          required\n          error={validationErrors.gender}\n        />\n\n        {/* Contact Information */}\n        <TextInput\n          label=\"Email Address\"\n          type=\"email\"\n          value={localData.email || ''}\n          onChange={(e) => handleLocalChange('email', e.target.value)}\n          required\n          error={validationErrors.email}\n        />\n\n        <TextInput\n          label=\"Phone Number\"\n          value={localData.phone || ''}\n          onChange={(e) => handleLocalChange('phone', e.target.value)}\n          placeholder=\"+265 1 234 567\"\n          required\n          error={validationErrors.phone}\n        />\n\n        {/* Identification */}\n        <TextInput\n          label=\"National ID\"\n          value={localData.national_id || ''}\n          onChange={(e) => handleLocalChange('national_id', e.target.value)}\n          placeholder=\"**********\"\n          required\n          error={validationErrors.national_id}\n        />\n\n        <TextInput\n          label=\"Date of Birth\"\n          type=\"date\"\n          value={localData.date_of_birth || ''}\n          onChange={(e) => handleLocalChange('date_of_birth', e.target.value)}\n          required\n          error={validationErrors.date_of_birth}\n        />\n\n        <Select\n          label=\"Nationality\"\n          value={localData.nationality || 'Malawian'}\n          onChange={(value) => handleLocalChange('nationality', value)}\n          options={[\n            { value: 'Malawian', label: 'Malawian' },\n            { value: 'Other', label: 'Other' }\n          ]}\n          required\n          error={validationErrors.nationality}\n        />\n      </div>\n\n      {/* Address Information */}\n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n        <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          Address Information\n        </h4>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"md:col-span-2\">\n            <TextInput\n              label=\"Postal Address\"\n              value={localData.postal_address || ''}\n              onChange={(e) => handleLocalChange('postal_address', e.target.value)}\n              placeholder=\"P.O. Box 123\"\n              required\n              error={validationErrors.postal_address}\n            />\n          </div>\n\n          <div className=\"md:col-span-2\">\n            <TextInput\n              label=\"Physical Address\"\n              value={localData.physical_address || ''}\n              onChange={(e) => handleLocalChange('physical_address', e.target.value)}\n              placeholder=\"Street address\"\n              required\n              error={validationErrors.physical_address}\n            />\n          </div>\n\n          <TextInput\n            label=\"City\"\n            value={localData.city || ''}\n            onChange={(e) => handleLocalChange('city', e.target.value)}\n            required\n            error={validationErrors.city}\n          />\n\n          <Select\n            label=\"District\"\n            value={localData.district || ''}\n            onChange={(value) => handleLocalChange('district', value)}\n            options={[\n              { value: 'Blantyre', label: 'Blantyre' },\n              { value: 'Lilongwe', label: 'Lilongwe' },\n              { value: 'Mzuzu', label: 'Mzuzu' },\n              { value: 'Zomba', label: 'Zomba' },\n              { value: 'Other', label: 'Other' }\n            ]}\n            required\n            error={validationErrors.district}\n          />\n\n          <TextInput\n            label=\"Postal Code\"\n            value={localData.postal_code || ''}\n            onChange={(e) => handleLocalChange('postal_code', e.target.value)}\n            error={validationErrors.postal_code}\n          />\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {validationErrors.save && (\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3\"></i>\n            <div>\n              <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                Error Saving Application\n              </h3>\n              <p className=\"text-red-700 dark:text-red-300 text-sm mt-1\">\n                {validationErrors.save}\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Success Message */}\n      {applicationCreated && !isEditMode && (\n        <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <i className=\"ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3\"></i>\n            <div>\n              <h3 className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                Application Created Successfully!\n              </h3>\n              <p className=\"text-green-700 dark:text-green-300 text-sm mt-1\">\n                Your application has been created. You can now continue to the next step.\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Action Buttons */}\n      <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\n        {applicationCreated && !isEditMode ? (\n          <>\n            {/* Save Changes Button (for edit mode) */}\n            <button\n              onClick={handleSave}\n              disabled={isSaving || isLoading}\n              className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSaving || isLoading ? (\n                <>\n                  <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n                  Saving...\n                </>\n              ) : (\n                <>\n                  <i className=\"ri-save-line mr-2\"></i>\n                  Save Changes\n                </>\n              )}\n            </button>\n\n            {/* Continue to Next Step Button */}\n            <button\n              onClick={handleContinueToNextStep}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              <i className=\"ri-arrow-right-line mr-2\"></i>\n              Continue to Company Profile\n            </button>\n          </>\n        ) : (\n          /* Create/Save Application Button */\n          <button\n            onClick={handleSave}\n            disabled={isSaving || isLoading}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isSaving || isLoading ? (\n              <>\n                <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n                {applicationId ? 'Saving...' : 'Creating Application...'}\n              </>\n            ) : (\n              <>\n                <i className=\"ri-save-line mr-2\"></i>\n                {applicationId ? 'Save Changes' : 'Create Application'}\n              </>\n            )}\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ApplicantInfo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAVA;;;;;;;;;;AAeA,MAAM,gBAA8C,CAAC,EACnD,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,aAAa,KAAK,EAClB,cAAc,EACd,WAAW,EACX,UAAU,EACX;;IACC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IAEvB,0BAA0B;IAC1B,QAAQ,GAAG,CAAC,kCAAkC;QAC5C;QACA;QACA;QACA;IACF;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,gBAAgB;QAChB,YAAY;QACZ,WAAW;QACX,aAAa;QACb,OAAO;QACP,OAAO;QACP,aAAa;QACb,eAAe;QACf,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,kBAAkB;QAClB,MAAM;QACN,UAAU;QACV,aAAa;IACf;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhF,2DAA2D;IAC3D,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;4DAAmB;oBACvB,+CAA+C;oBAC/C,IAAI,cAAc,iBAAiB,kBAAkB,SAAS,kBAAkB,eAAe,cAAc,IAAI,OAAO,IAAI;wBAC1H,QAAQ,GAAG,CAAC,0CAA0C;wBACtD,aAAa;wBACb,IAAI;4BACF,gFAAgF;4BAChF,MAAM,eAAe,EAAE;4BAEvB,+CAA+C;4BAC/C,aAAa,IAAI,CACf,gJAAA,CAAA,6BAA0B,CAAC,cAAc,CAAC,eAAe,iBACtD,IAAI;4EAAC,CAAA,OAAQ,CAAC;wCAAE,QAAQ;wCAAY;oCAAK,CAAC;2EAC1C,KAAK;4EAAC,CAAA;oCACL,QAAQ,IAAI,CAAC,6BAA6B;oCAC1C,OAAO;wCAAE,QAAQ;wCAAY,MAAM;oCAAK;gCAC1C;;4BAGJ,yDAAyD;4BACzD,aAAa,IAAI,CACf,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,eAC/B,IAAI;4EAAC,CAAA,OAAQ,CAAC;wCAAE,QAAQ;wCAAe;oCAAK,CAAC;2EAC7C,KAAK;4EAAC,CAAA;oCACL,QAAQ,IAAI,CAAC,oCAAoC;oCACjD,OAAO;wCAAE,QAAQ;wCAAe,MAAM;oCAAK;gCAC7C;;4BAGJ,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;4BAElC,oCAAoC;4BACpC,IAAI,aAAa;gCAAE,GAAG,SAAS;4BAAC;4BAEhC,KAAK,MAAM,UAAU,QAAS;gCAC5B,IAAI,OAAO,IAAI,EAAE;oCACf,IAAI,OAAO,MAAM,KAAK,YAAY;wCAChC,wDAAwD;wCACxD,MAAM,WAAW,OAAO,IAAI;wCAC5B,IAAI,YAAY,SAAS,YAAY,EAAE;4CACrC,QAAQ,GAAG,CAAC,qBAAqB,SAAS,YAAY;4CACtD,aAAa;gDAAE,GAAG,UAAU;gDAAE,GAAG,SAAS,YAAY;4CAAC;wCACzD;oCACF,OAAO,IAAI,OAAO,MAAM,KAAK,eAAe;wCAC1C,4DAA4D;wCAC5D,MAAM,kBAAkB,OAAO,IAAI;wCACnC,IAAI,mBAAmB,gBAAgB,SAAS,EAAE;4CAChD,MAAM,YAAY,gBAAgB,SAAS;4CAC3C,QAAQ,GAAG,CAAC,sCAAsC;4CAElD,8DAA8D;4CAC9D,IAAI,UAAU,IAAI,IAAI,CAAC,WAAW,UAAU,IAAI,CAAC,WAAW,SAAS,EAAE;gDACrE,qDAAqD;gDACrD,MAAM,YAAY,UAAU,IAAI,CAAC,KAAK,CAAC;gDACvC,IAAI,UAAU,MAAM,IAAI,GAAG;oDACzB,WAAW,UAAU,GAAG,SAAS,CAAC,EAAE;oDACpC,WAAW,SAAS,GAAG,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC;gDACjD,OAAO;oDACL,WAAW,UAAU,GAAG,UAAU,IAAI;gDACxC;4CACF;4CAEA,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,KAAK,EAAE;gDACxC,WAAW,KAAK,GAAG,UAAU,KAAK;4CACpC;4CAEA,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,KAAK,EAAE;gDACxC,WAAW,KAAK,GAAG,UAAU,KAAK;4CACpC;4CAEA,IAAI,UAAU,mBAAmB,IAAI,CAAC,WAAW,IAAI,EAAE;gDACrD,WAAW,IAAI,GAAG,UAAU,mBAAmB;4CACjD;wCACF;oCACF;gCACF;4BACF;4BAEA,QAAQ,GAAG,CAAC,2CAA2C;4BACvD,aAAa;wBAEf,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,0CAA0C;4BACxD,mDAAmD;4BACnD,kDAAkD;4BAClD,QAAQ,GAAG,CAAC;wBACd,SAAU;4BACR,aAAa;wBACf;oBACF,OAAO;wBACL,QAAQ,GAAG,CAAC,mEAAmE;4BAAE;4BAAY;wBAAc;oBAC7G;gBACF;;YAEA;QACF;kCAAG;QAAC;QAAe;KAAW;IAE9B,uBAAuB;IACvB,MAAM,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;wDAAE,CAAC,OAAe;YACpD;gEAAa,CAAC,OAAc,CAAC;wBAAE,GAAG,IAAI;wBAAE,CAAC,MAAM,EAAE;oBAAM,CAAC;;YACxD,qBAAqB;YAErB,qDAAqD;YACrD,IAAI,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;gBAC/C;oEAAoB,CAAC,OAAiC,CAAC;4BAAE,GAAG,IAAI;4BAAE,CAAC,MAAM,EAAE;wBAAG,CAAC;;YACjF;YAEA,mDAAmD;YACnD,IAAI,iBAAiB,IAAI,EAAE;gBACzB;oEAAoB,CAAC,OAAiC,CAAC;4BAAE,GAAG,IAAI;4BAAE,MAAM;wBAAG,CAAC;;YAC9E;QACF;uDAAG;QAAC;KAAiB;IAErB,qBAAqB;IACrB,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,oBAAoB,WAAW,MAAM;QACrC,OAAO,WAAW,OAAO;IAC3B;IAEA,uBAAuB;IACvB,MAAM,WAAW;QACf,MAAM,aAAa,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,oBAAoB,WAAW,MAAM;QAErC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,8CAA8C;YAC9C,sDAAsD;YACtD,QAAQ,GAAG,CAAC,sBAAsB,WAAW,MAAM;YACnD,OAAO;QACT;QAEA,YAAY;QACZ,IAAI;YACF,IAAI,cAAc,iBAAiB,kBAAkB,OAAO;gBAC1D,mCAAmC;gBACnC,MAAM,gJAAA,CAAA,6BAA0B,CAAC,uBAAuB,CAAC,eAAe,iBAAiB;gBAEzF,8CAA8C;gBAC9C,MAAM,gJAAA,CAAA,6BAA0B,CAAC,iBAAiB,CAAC,eAAe,kBAAkB;gBAEpF,QAAQ,GAAG,CAAC,2CAA2C;YACzD,OAAO;gBACL,uCAAuC;gBACvC,6DAA6D;gBAC7D,MAAM,mBAAmB;oBACvB,MAAM,GAAG,UAAU,UAAU,CAAC,CAAC,EAAE,UAAU,SAAS,EAAE,CAAC,IAAI;oBAC3D,OAAO,UAAU,KAAK;oBACtB,OAAO,UAAU,KAAK;oBACtB,8BAA8B,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;oBACjD,MAAM,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;oBAC1B,SAAS;oBACT,oBAAoB,IAAI,OAAO,WAAW;oBAC1C,qBAAqB,UAAU,IAAI,IAAI;gBACzC;gBAEA,MAAM,mBAAmB,MAAM,sIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;gBAIhE,QAAQ,GAAG,CAAC,+BAA+B;gBAC3C,QAAQ,GAAG,CAAC,+BAA+B,kBAAkB;gBAE7D,4CAA4C;gBAC5C,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,YAAY,EAAE;oBACvD,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,qBAAqB;oBACzB,oBAAoB,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;oBACvC,cAAc,iBAAiB,YAAY;oBAC3C,qBAAqB;oBACrB,QAAQ;gBACV;gBAEA,QAAQ,GAAG,CAAC,sCAAsC;gBAClD,MAAM,qBAAqB,MAAM,wIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC;gBAEtE,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,QAAQ,GAAG,CAAC,iCAAiC,oBAAoB;gBAEjE,8CAA8C;gBAC9C,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,cAAc,EAAE;oBAC7D,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,mBAAmB,mBAAmB,cAAc;gBAC1D,QAAQ,GAAG,CAAC,yBAAyB;gBAErC,iBAAiB;gBACjB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,uBAAuB,CACtD,kBACA,iBACA;gBAGF,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,uDAAuD;gBACvD,MAAM,gJAAA,CAAA,6BAA0B,CAAC,kBAAkB,CAAC,kBAAkB;gBAEtE,8BAA8B;gBAC9B,MAAM,gJAAA,CAAA,6BAA0B,CAAC,iBAAiB,CAAC,kBAAkB,kBAAkB;gBAEvF,QAAQ,GAAG,CAAC,oEAAoE;gBAEhF,gCAAgC;gBAChC,sBAAsB;gBACtB,wBAAwB;gBAExB,2EAA2E;gBAC3E,iBAAiB,kBAAkB;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAEnF,qDAAqD;gBACrD,OAAO;YACT;YAEA,qBAAqB;YACrB,iBAAiB,kBAAkB;YACnC,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAS,MAAM,OAAO;gBACtB,UAAU,MAAM,QAAQ,EAAE;gBAC1B,QAAQ,MAAM,QAAQ,EAAE;YAC1B;YAEA,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,0BAA0B;gBACpE,eAAe;YACjB,OAAO,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACxC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC5C,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,eAAe,MAAM,OAAO;YAC9B;YAEA,0EAA0E;YAC1E,2DAA2D;YAE3D,gCAAgC;YAChC,oBAAoB;gBAAE,MAAM;YAAa;YACzC,OAAO;QACT,SAAU;YACR,YAAY;QACd;IACF;IAEA,2BAA2B;IAC3B,MAAM,aAAa;QACjB,MAAM;IACR;IAEA,+BAA+B;IAC/B,MAAM,2BAA2B;QAC/B,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,2BAA2B;QACvC,QAAQ,GAAG,CAAC,oBAAoB;QAChC,QAAQ,GAAG,CAAC,wBAAwB;QAEpC,IAAI,sBAAsB;YACxB,MAAM,cAAc,CAAC,6BAA6B,EAAE,cAAc,CAAC,EAAE,kBAAkB,qBAAqB,EAAE,sBAAsB;YACpI,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,OAAO,IAAI,CAAC;QACd,OAAO;YACL,QAAQ,KAAK,CAAC;QAChB;IACF;IAIA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;oBAG5D,CAAC,+BACA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;8CACX,6LAAC;oCAAE,WAAU;;;;;;gCAA+B;;;;;;;;;;;;oBAKjD,+BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;8CACX,6LAAC;oCAAE,WAAU;;;;;;gCAAyB;gCAChB,cAAc,KAAK,CAAC,GAAG;gCAAG;;;;;;;;;;;;;;;;;;0BAMxD,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,yIAAA,CAAA,UAAM;4BACL,OAAM;4BACN,OAAO,UAAU,cAAc,IAAI;4BACnC,UAAU,CAAC,QAAU,kBAAkB,kBAAkB;4BACzD,SAAS;gCACP;oCAAE,OAAO;oCAAc,OAAO;gCAAa;gCAC3C;oCAAE,OAAO;oCAAW,OAAO;gCAAU;gCACrC;oCAAE,OAAO;oCAAgB,OAAO;gCAAe;6BAChD;4BACD,QAAQ;4BACR,OAAO,iBAAiB,cAAc;;;;;;;;;;;kCAK1C,6LAAC,4IAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,UAAU,IAAI;wBAC/B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC/D,QAAQ;wBACR,OAAO,iBAAiB,UAAU;;;;;;kCAGpC,6LAAC,4IAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,SAAS,IAAI;wBAC9B,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC9D,QAAQ;wBACR,OAAO,iBAAiB,SAAS;;;;;;kCAGnC,6LAAC,4IAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,WAAW,IAAI;wBAChC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wBAChE,OAAO,iBAAiB,WAAW;;;;;;kCAGrC,6LAAC,yIAAA,CAAA,UAAM;wBACL,OAAM;wBACN,OAAO,UAAU,MAAM,IAAI;wBAC3B,UAAU,CAAC,QAAU,kBAAkB,UAAU;wBACjD,SAAS;4BACP;gCAAE,OAAO;gCAAQ,OAAO;4BAAO;4BAC/B;gCAAE,OAAO;gCAAU,OAAO;4BAAS;4BACnC;gCAAE,OAAO;gCAAS,OAAO;4BAAQ;yBAClC;wBACD,QAAQ;wBACR,OAAO,iBAAiB,MAAM;;;;;;kCAIhC,6LAAC,4IAAA,CAAA,UAAS;wBACR,OAAM;wBACN,MAAK;wBACL,OAAO,UAAU,KAAK,IAAI;wBAC1B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wBAC1D,QAAQ;wBACR,OAAO,iBAAiB,KAAK;;;;;;kCAG/B,6LAAC,4IAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,KAAK,IAAI;wBAC1B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wBAC1D,aAAY;wBACZ,QAAQ;wBACR,OAAO,iBAAiB,KAAK;;;;;;kCAI/B,6LAAC,4IAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,WAAW,IAAI;wBAChC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wBAChE,aAAY;wBACZ,QAAQ;wBACR,OAAO,iBAAiB,WAAW;;;;;;kCAGrC,6LAAC,4IAAA,CAAA,UAAS;wBACR,OAAM;wBACN,MAAK;wBACL,OAAO,UAAU,aAAa,IAAI;wBAClC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wBAClE,QAAQ;wBACR,OAAO,iBAAiB,aAAa;;;;;;kCAGvC,6LAAC,yIAAA,CAAA,UAAM;wBACL,OAAM;wBACN,OAAO,UAAU,WAAW,IAAI;wBAChC,UAAU,CAAC,QAAU,kBAAkB,eAAe;wBACtD,SAAS;4BACP;gCAAE,OAAO;gCAAY,OAAO;4BAAW;4BACvC;gCAAE,OAAO;gCAAS,OAAO;4BAAQ;yBAClC;wBACD,QAAQ;wBACR,OAAO,iBAAiB,WAAW;;;;;;;;;;;;0BAKvC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAI1E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,4IAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,UAAU,cAAc,IAAI;oCACnC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACnE,aAAY;oCACZ,QAAQ;oCACR,OAAO,iBAAiB,cAAc;;;;;;;;;;;0CAI1C,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,4IAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,UAAU,gBAAgB,IAAI;oCACrC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;oCACrE,aAAY;oCACZ,QAAQ;oCACR,OAAO,iBAAiB,gBAAgB;;;;;;;;;;;0CAI5C,6LAAC,4IAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,UAAU,IAAI,IAAI;gCACzB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACzD,QAAQ;gCACR,OAAO,iBAAiB,IAAI;;;;;;0CAG9B,6LAAC,yIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,UAAU,QAAQ,IAAI;gCAC7B,UAAU,CAAC,QAAU,kBAAkB,YAAY;gCACnD,SAAS;oCACP;wCAAE,OAAO;wCAAY,OAAO;oCAAW;oCACvC;wCAAE,OAAO;wCAAY,OAAO;oCAAW;oCACvC;wCAAE,OAAO;wCAAS,OAAO;oCAAQ;oCACjC;wCAAE,OAAO;wCAAS,OAAO;oCAAQ;oCACjC;wCAAE,OAAO;wCAAS,OAAO;oCAAQ;iCAClC;gCACD,QAAQ;gCACR,OAAO,iBAAiB,QAAQ;;;;;;0CAGlC,6LAAC,4IAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,UAAU,WAAW,IAAI;gCAChC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gCAChE,OAAO,iBAAiB,WAAW;;;;;;;;;;;;;;;;;;YAMxC,iBAAiB,IAAI,kBACpB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,6LAAC;oCAAE,WAAU;8CACV,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;YAQ/B,sBAAsB,CAAC,4BACtB,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;;;;;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,6LAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;;;;;;;;;;;;0BASvE,6LAAC;gBAAI,WAAU;0BACZ,sBAAsB,CAAC,2BACtB;;sCAEE,6LAAC;4BACC,SAAS;4BACT,UAAU,YAAY;4BACtB,WAAU;sCAET,YAAY,0BACX;;kDACE,6LAAC;wCAAE,WAAU;;;;;;oCAAyC;;6DAIxD;;kDACE,6LAAC;wCAAE,WAAU;;;;;;oCAAwB;;;;;;;;sCAO3C,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC;oCAAE,WAAU;;;;;;gCAA+B;;;;;;;;mCAKhD,kCAAkC,iBAClC,6LAAC;oBACC,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,YAAY,0BACX;;0CACE,6LAAC;gCAAE,WAAU;;;;;;4BACZ,gBAAgB,cAAc;;qDAGjC;;0CACE,6LAAC;gCAAE,WAAU;;;;;;4BACZ,gBAAgB,iBAAiB;;;;;;;;;;;;;;;;;;;AAQlD;GAxlBM;;QASW,qIAAA,CAAA,YAAS;;;KATpB;uCA0lBS", "debugId": null}}, {"offset": {"line": 4218, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/%5BlicenseTypeId%5D/%5BcategoryId%5D/applicant-info/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, useParams, useSearchParams } from 'next/navigation';\nimport CustomerLayout from '@/components/customer/CustomerLayout';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useLicenseData } from '@/hooks/useLicenseData';\nimport {\n  getLicenseTypeStepConfig,\n  getStepByRoute,\n  getStepIndex,\n  getTotalSteps,\n  getNextStep,\n  getPreviousStep,\n  calculateProgress\n} from '@/config/licenseTypeStepConfig';\nimport { applicationProgressService } from '@/services/applicationProgressService';\nimport { stepValidationService } from '@/services/stepValidationService';\n\n// Import the step component\nimport ApplicantInfo from '@/components/customer/application/steps/ApplicantInfo';\n\nconst ApplicantInfoPage: React.FC = () => {\n  const router = useRouter();\n  const params = useParams();\n  const searchParams = useSearchParams();\n  const { isAuthenticated, loading: authLoading } = useAuth();\n\n  // Initialize license data to populate UUID-to-code mapping\n  const { loading: licenseDataLoading } = useLicenseData();\n\n  const licenseTypeId = params.licenseTypeId as string;\n  const licenseCategoryId = params.categoryId as string;\n  const stepName = 'applicant-info';\n  \n  // Get application ID from URL params (for continuing applications)\n  const applicationId = searchParams.get('app') || 'new';\n  const isEditMode = applicationId !== 'new';\n\n  console.log('ApplicantInfoPage loaded with params:', {\n    licenseTypeId,\n    licenseCategoryId,\n    stepName,\n    applicationId,\n    isEditMode\n  });\n\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [completedSteps, setCompletedSteps] = useState<string[]>([]);\n  const [progressPercentage, setProgressPercentage] = useState(0);\n  const [canNavigateNext, setCanNavigateNext] = useState(false);\n  const [canNavigatePrevious, setCanNavigatePrevious] = useState(false);\n  const [navigationError, setNavigationError] = useState<string | null>(null);\n\n  // Get step configuration\n  console.log('ApplicantInfo page - License Type ID:', licenseTypeId);\n  const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n  console.log('ApplicantInfo page - License Config found:', !!licenseConfig, licenseConfig?.name);\n  \n  const currentStep = getStepByRoute(licenseTypeId, stepName);\n  const currentStepIndex = getStepIndex(licenseTypeId, stepName);\n  const totalSteps = getTotalSteps(licenseTypeId);\n  const nextStep = getNextStep(licenseTypeId, stepName);\n  const previousStep = getPreviousStep(licenseTypeId, stepName);\n  \n  console.log('ApplicantInfo page - Current step:', { stepName, currentStep: !!currentStep, currentStepIndex });\n\n  // Redirect if not authenticated\n  useEffect(() => {\n    if (!authLoading && !isAuthenticated) {\n      router.push('/customer/auth/login');\n      return;\n    }\n  }, [isAuthenticated, authLoading, router]);\n\n  // Load progress and validate step configuration\n  useEffect(() => {\n    const initializeStepPage = async () => {\n      // Wait for license data to load before proceeding\n      if (licenseDataLoading) {\n        return;\n      }\n\n      if (!licenseConfig) {\n        console.error('No license config found for:', licenseTypeId);\n        setError(`Invalid license type: ${licenseTypeId}. Please check the license type configuration.`);\n        return;\n      }\n\n      if (!currentStep) {\n        setError(`Invalid step: ${stepName} for license type ${licenseTypeId}`);\n        return;\n      }\n\n      // Load or initialize progress\n      try {\n        console.log('Loading progress for application:', applicationId, 'isEditMode:', isEditMode);\n        let progress = await applicationProgressService.getProgress(applicationId);\n        console.log('Progress loaded:', progress);\n\n        if (!progress && isEditMode) {\n          console.log('No progress found for existing application, initializing...');\n          // Initialize progress for existing application\n          progress = await applicationProgressService.initializeProgress(applicationId, licenseTypeId);\n          console.log('Progress initialized:', progress);\n        }\n\n        if (progress) {\n          const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);\n          console.log('Completed step IDs:', completedStepIds);\n          setCompletedSteps(completedStepIds);\n          setProgressPercentage(progress.progressPercentage);\n\n          // Validate navigation permissions\n          console.log('Validating navigation for step:', currentStep.id);\n          const nextValidation = await stepValidationService.validateNextStepNavigation(\n            applicationId,\n            licenseTypeId,\n            currentStep.id\n          );\n          console.log('Navigation validation result:', nextValidation);\n          setCanNavigateNext(nextValidation.canNavigateToStep);\n\n          const prevValidation = await stepValidationService.validatePreviousStepNavigation(\n            applicationId, \n            licenseTypeId, \n            currentStep.id\n          );\n          setCanNavigatePrevious(prevValidation.canNavigateToStep);\n        }\n      } catch (error) {\n        console.error('Error loading application progress:', error);\n        // Don't fail completely - allow the form to load even if progress fails\n        console.log('Continuing with default progress state due to error');\n        setProgressPercentage(0);\n        setCompletedSteps([]);\n        setCanNavigateNext(false);\n      }\n\n      setIsLoading(false);\n    };\n\n    initializeStepPage();\n  }, [licenseConfig, currentStep, currentStepIndex, stepName, licenseTypeId, licenseCategoryId, applicationId, isEditMode, router, licenseDataLoading]);\n\n  // Handle step completion\n  const handleStepComplete = async (stepId: string, data?: any) => {\n    try {\n      let actualApplicationId = applicationId;\n\n      // If this is the ApplicantInfo step and we got an application ID, update it\n      if (stepId === 'applicant-info' && data?.applicationId) {\n        actualApplicationId = data.applicationId;\n        console.log('New application created, updating URL with application ID:', actualApplicationId);\n\n        // Update URL to include the new application ID\n        router.replace(`/customer/applications/apply/${licenseTypeId}/${licenseCategoryId}/applicant-info?app=${actualApplicationId}`);\n      }\n\n      if (actualApplicationId !== 'new') {\n        const progress = await applicationProgressService.markStepCompleted(actualApplicationId, stepId, data);\n        setCompletedSteps(progress.steps.filter(s => s.completed).map(s => s.stepId));\n        setProgressPercentage(progress.progressPercentage);\n\n        // Refresh navigation permissions after step completion\n        if (currentStep) {\n          const nextValidation = await stepValidationService.validateNextStepNavigation(\n            actualApplicationId,\n            licenseTypeId,\n            currentStep.id\n          );\n          setCanNavigateNext(nextValidation.canNavigateToStep);\n        }\n      }\n    } catch (error) {\n      console.error('Error marking step as completed:', error);\n    }\n  };\n\n  // Handle step error\n  const handleStepError = (stepId: string, error: string) => {\n    console.error(`Error in step ${stepId}:`, error);\n    setError(error);\n  };\n\n  // Handle navigation\n  const handleNavigate = (direction: 'next' | 'previous') => {\n    if (direction === 'next' && nextStep && canNavigateNext) {\n      const appParam = applicationId !== 'new' ? `?app=${applicationId}` : '';\n      router.push(`/customer/applications/apply/${licenseTypeId}/${licenseCategoryId}/${nextStep.route}${appParam}`);\n    } else if (direction === 'previous' && previousStep && canNavigatePrevious) {\n      const appParam = applicationId !== 'new' ? `?app=${applicationId}` : '';\n      router.push(`/customer/applications/apply/${licenseTypeId}/${licenseCategoryId}/${previousStep.route}${appParam}`);\n    }\n  };\n\n  if (authLoading || isLoading) {\n    return (\n      <CustomerLayout>\n        <div className=\"flex items-center justify-center min-h-96\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading application step...</p>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  if (error) {\n    return (\n      <CustomerLayout>\n        <div className=\"max-w-4xl mx-auto p-6\">\n          <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4\"></i>\n              <div>\n                <h3 className=\"text-lg font-medium text-red-800 dark:text-red-200\">\n                  Error Loading Step\n                </h3>\n                <p className=\"text-red-700 dark:text-red-300 mt-1\">{error}</p>\n                <button\n                  onClick={() => router.back()}\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40\"\n                >\n                  <i className=\"ri-arrow-left-line mr-2\"></i>\n                  Go Back\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  // Use tracked progress instead of calculating it\n  const displayProgress = progressPercentage;\n\n  // Step props for the component\n  const stepProps = {\n    applicationId: applicationId || 'new',\n    licenseTypeId,\n    licenseCategoryId,\n    isEditMode,\n    onStepComplete: handleStepComplete,\n    onStepError: handleStepError,\n    onNavigate: handleNavigate\n  };\n\n  return (\n    <CustomerLayout>\n      <div className=\"max-w-4xl mx-auto p-6\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\n            {licenseConfig?.name} License Application\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Step {currentStepIndex + 1} of {totalSteps}: {currentStep?.name}\n          </p>\n          {isEditMode && (\n            <div className=\"mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n              <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n                <i className=\"ri-edit-line mr-1\"></i>\n                Editing existing application\n              </p>\n            </div>\n          )}\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between mb-2\">\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              Step {currentStepIndex + 1} of {totalSteps}\n            </span>\n            <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {displayProgress}% Complete\n            </span>\n          </div>\n          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n            <div\n              className=\"bg-primary h-2 rounded-full transition-all duration-300\"\n              style={{ width: `${displayProgress}%` }}\n            ></div>\n          </div>\n        </div>\n\n        {/* Step Navigation Breadcrumb */}\n        <div className=\"mb-8\">\n          <div className=\"flex flex-wrap gap-2\">\n            {licenseConfig?.steps.map((step, index) => {\n              const isCompleted = completedSteps.includes(step.id);\n              const isCurrent = index === currentStepIndex;\n              \n              return (\n                <div\n                  key={step.id}\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\n                    isCurrent\n                      ? 'bg-primary text-white'\n                      : isCompleted\n                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'\n                  }`}\n                >\n                  {isCompleted && !isCurrent && (\n                    <i className=\"ri-check-line mr-1\"></i>\n                  )}\n                  {index + 1}. {step.name}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Navigation Error */}\n        {navigationError && (\n          <div className=\"mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n            <div className=\"flex items-center\">\n              <i className=\"ri-warning-line text-yellow-600 dark:text-yellow-400 text-lg mr-3\"></i>\n              <div>\n                <h3 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">\n                  Navigation Restriction\n                </h3>\n                <p className=\"text-yellow-700 dark:text-yellow-300 text-sm mt-1\">\n                  {navigationError}\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Step Content */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6\">\n          <ApplicantInfo {...stepProps} />\n        </div>\n      </div>\n    </CustomerLayout>\n  );\n};\n\nexport default ApplicantInfoPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AASA;AACA;AAEA,4BAA4B;AAC5B;;;AApBA;;;;;;;;;;AAsBA,MAAM,oBAA8B;;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAExD,2DAA2D;IAC3D,MAAM,EAAE,SAAS,kBAAkB,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAErD,MAAM,gBAAgB,OAAO,aAAa;IAC1C,MAAM,oBAAoB,OAAO,UAAU;IAC3C,MAAM,WAAW;IAEjB,mEAAmE;IACnE,MAAM,gBAAgB,aAAa,GAAG,CAAC,UAAU;IACjD,MAAM,aAAa,kBAAkB;IAErC,QAAQ,GAAG,CAAC,yCAAyC;QACnD;QACA;QACA;QACA;QACA;IACF;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtE,yBAAyB;IACzB,QAAQ,GAAG,CAAC,yCAAyC;IACrD,MAAM,gBAAgB,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE;IAC/C,QAAQ,GAAG,CAAC,8CAA8C,CAAC,CAAC,eAAe,eAAe;IAE1F,MAAM,cAAc,CAAA,GAAA,yIAAA,CAAA,iBAAc,AAAD,EAAE,eAAe;IAClD,MAAM,mBAAmB,CAAA,GAAA,yIAAA,CAAA,eAAY,AAAD,EAAE,eAAe;IACrD,MAAM,aAAa,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;IACjC,MAAM,WAAW,CAAA,GAAA,yIAAA,CAAA,cAAW,AAAD,EAAE,eAAe;IAC5C,MAAM,eAAe,CAAA,GAAA,yIAAA,CAAA,kBAAe,AAAD,EAAE,eAAe;IAEpD,QAAQ,GAAG,CAAC,sCAAsC;QAAE;QAAU,aAAa,CAAC,CAAC;QAAa;IAAiB;IAE3G,gCAAgC;IAChC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;sCAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,gDAAgD;IAChD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;kEAAqB;oBACzB,kDAAkD;oBAClD,IAAI,oBAAoB;wBACtB;oBACF;oBAEA,IAAI,CAAC,eAAe;wBAClB,QAAQ,KAAK,CAAC,gCAAgC;wBAC9C,SAAS,CAAC,sBAAsB,EAAE,cAAc,8CAA8C,CAAC;wBAC/F;oBACF;oBAEA,IAAI,CAAC,aAAa;wBAChB,SAAS,CAAC,cAAc,EAAE,SAAS,kBAAkB,EAAE,eAAe;wBACtE;oBACF;oBAEA,8BAA8B;oBAC9B,IAAI;wBACF,QAAQ,GAAG,CAAC,qCAAqC,eAAe,eAAe;wBAC/E,IAAI,WAAW,MAAM,gJAAA,CAAA,6BAA0B,CAAC,WAAW,CAAC;wBAC5D,QAAQ,GAAG,CAAC,oBAAoB;wBAEhC,IAAI,CAAC,YAAY,YAAY;4BAC3B,QAAQ,GAAG,CAAC;4BACZ,+CAA+C;4BAC/C,WAAW,MAAM,gJAAA,CAAA,6BAA0B,CAAC,kBAAkB,CAAC,eAAe;4BAC9E,QAAQ,GAAG,CAAC,yBAAyB;wBACvC;wBAEA,IAAI,UAAU;4BACZ,MAAM,mBAAmB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,mBAAmB,CAAC;4BAC9E,QAAQ,GAAG,CAAC,uBAAuB;4BACnC,kBAAkB;4BAClB,sBAAsB,SAAS,kBAAkB;4BAEjD,kCAAkC;4BAClC,QAAQ,GAAG,CAAC,mCAAmC,YAAY,EAAE;4BAC7D,MAAM,iBAAiB,MAAM,2IAAA,CAAA,wBAAqB,CAAC,0BAA0B,CAC3E,eACA,eACA,YAAY,EAAE;4BAEhB,QAAQ,GAAG,CAAC,iCAAiC;4BAC7C,mBAAmB,eAAe,iBAAiB;4BAEnD,MAAM,iBAAiB,MAAM,2IAAA,CAAA,wBAAqB,CAAC,8BAA8B,CAC/E,eACA,eACA,YAAY,EAAE;4BAEhB,uBAAuB,eAAe,iBAAiB;wBACzD;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,uCAAuC;wBACrD,wEAAwE;wBACxE,QAAQ,GAAG,CAAC;wBACZ,sBAAsB;wBACtB,kBAAkB,EAAE;wBACpB,mBAAmB;oBACrB;oBAEA,aAAa;gBACf;;YAEA;QACF;sCAAG;QAAC;QAAe;QAAa;QAAkB;QAAU;QAAe;QAAmB;QAAe;QAAY;QAAQ;KAAmB;IAEpJ,yBAAyB;IACzB,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,IAAI,sBAAsB;YAE1B,4EAA4E;YAC5E,IAAI,WAAW,oBAAoB,MAAM,eAAe;gBACtD,sBAAsB,KAAK,aAAa;gBACxC,QAAQ,GAAG,CAAC,8DAA8D;gBAE1E,+CAA+C;gBAC/C,OAAO,OAAO,CAAC,CAAC,6BAA6B,EAAE,cAAc,CAAC,EAAE,kBAAkB,oBAAoB,EAAE,qBAAqB;YAC/H;YAEA,IAAI,wBAAwB,OAAO;gBACjC,MAAM,WAAW,MAAM,gJAAA,CAAA,6BAA0B,CAAC,iBAAiB,CAAC,qBAAqB,QAAQ;gBACjG,kBAAkB,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;gBAC3E,sBAAsB,SAAS,kBAAkB;gBAEjD,uDAAuD;gBACvD,IAAI,aAAa;oBACf,MAAM,iBAAiB,MAAM,2IAAA,CAAA,wBAAqB,CAAC,0BAA0B,CAC3E,qBACA,eACA,YAAY,EAAE;oBAEhB,mBAAmB,eAAe,iBAAiB;gBACrD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,QAAgB;QACvC,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,EAAE;QAC1C,SAAS;IACX;IAEA,oBAAoB;IACpB,MAAM,iBAAiB,CAAC;QACtB,IAAI,cAAc,UAAU,YAAY,iBAAiB;YACvD,MAAM,WAAW,kBAAkB,QAAQ,CAAC,KAAK,EAAE,eAAe,GAAG;YACrE,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,cAAc,CAAC,EAAE,kBAAkB,CAAC,EAAE,SAAS,KAAK,GAAG,UAAU;QAC/G,OAAO,IAAI,cAAc,cAAc,gBAAgB,qBAAqB;YAC1E,MAAM,WAAW,kBAAkB,QAAQ,CAAC,KAAK,EAAE,eAAe,GAAG;YACrE,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,cAAc,CAAC,EAAE,kBAAkB,CAAC,EAAE,aAAa,KAAK,GAAG,UAAU;QACnH;IACF;IAEA,IAAI,eAAe,WAAW;QAC5B,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,6LAAC;wCAAE,WAAU;kDAAuC;;;;;;kDACpD,6LAAC;wCACC,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;;0DAEV,6LAAC;gDAAE,WAAU;;;;;;4CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS3D;IAEA,iDAAiD;IACjD,MAAM,kBAAkB;IAExB,+BAA+B;IAC/B,MAAM,YAAY;QAChB,eAAe,iBAAiB;QAChC;QACA;QACA;QACA,gBAAgB;QAChB,aAAa;QACb,YAAY;IACd;IAEA,qBACE,6LAAC,mJAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;;gCACX,eAAe;gCAAK;;;;;;;sCAEvB,6LAAC;4BAAE,WAAU;;gCAAmC;gCACxC,mBAAmB;gCAAE;gCAAK;gCAAW;gCAAG,aAAa;;;;;;;wBAE5D,4BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;kDACX,6LAAC;wCAAE,WAAU;;;;;;oCAAwB;;;;;;;;;;;;;;;;;;8BAQ7C,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAU;;wCAAuD;wCAC/D,mBAAmB;wCAAE;wCAAK;;;;;;;8CAElC,6LAAC;oCAAK,WAAU;;wCACb;wCAAgB;;;;;;;;;;;;;sCAGrB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;8BAM5C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ,eAAe,MAAM,IAAI,CAAC,MAAM;4BAC/B,MAAM,cAAc,eAAe,QAAQ,CAAC,KAAK,EAAE;4BACnD,MAAM,YAAY,UAAU;4BAE5B,qBACE,6LAAC;gCAEC,WAAW,CAAC,2CAA2C,EACrD,YACI,0BACA,cACA,sEACA,iEACJ;;oCAED,eAAe,CAAC,2BACf,6LAAC;wCAAE,WAAU;;;;;;oCAEd,QAAQ;oCAAE;oCAAG,KAAK,IAAI;;+BAZlB,KAAK,EAAE;;;;;wBAelB;;;;;;;;;;;gBAKH,iCACC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;;;;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;;;;;;8BAQX,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC,0KAAA,CAAA,UAAa;wBAAE,GAAG,SAAS;;;;;;;;;;;;;;;;;;;;;;AAKtC;GAhUM;;QACW,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QACc,kIAAA,CAAA,UAAO;QAGjB,iIAAA,CAAA,iBAAc;;;KAPlD;uCAkUS", "debugId": null}}]}