import { <PERSON>In, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "class-validator";
import { ApiProperty, ApiPropertyOptional } from "@nestjs/swagger";

export class CreateAddressDto {
  @ApiProperty({
    description: 'Address type',
    enum: ['physical', 'postal'],
    example: 'physical'
  })
  @IsString({ message: "Address type invalid!" })
  @IsIn(['physical', 'postal'], { message: "Address type must be one of the following: physical, postal" })
  @IsNotEmpty({ message: "Address type is required!"})
  address_type: string;

  @ApiProperty({
    description: 'Address origin',
    enum: ['applicant', 'stakeholder', 'contact_person', 'user'],
    example: 'applicant'
  })
  @IsString({ message: "Address origin invalid!" })
  @IsIn(['applicant', 'stakeholder', 'contact_person', 'user'], { message: "Address origin must be one of the following: applicant, stakeholder, contact_person, user" })
  @IsNotEmpty({ message: "Address origin is required!"})
  address_origin: string;

  @ApiProperty({
    description: 'Address line 1',
    example: '123 Main Street'
  })
  @IsString({ message: "Address line 1 invalid!" })
  @IsNotEmpty({ message: "Address line 1 is required!"})
  address_line_1: string;

  @ApiPropertyOptional({
    description: 'Address line 2',
    example: 'Apartment 4B'
  })
  @IsOptional()
  @IsString({ message: "Address line 2 invalid!" })
  address_line_2?: string;

  @ApiPropertyOptional({
    description: 'Address line 3',
    example: 'Building Complex'
  })
  @IsOptional()
  @IsString({ message: "Address line 3 invalid!" })
  address_line_3?: string;

  @ApiProperty({
    description: 'Postal code',
    example: '101010',
    minLength: 6,
    maxLength: 9
  })
  @IsNotEmpty({ message: "Postal code is required!"})
  @IsString({ message: "Postal code invalid!" })
  @MinLength(6, { message: "Postal code must be at least 6 characters long!" })
  @MaxLength(9, { message: "Postal code must not exceed 9 characters!" })
  postal_code: string;

  @ApiProperty({
    description: 'Country',
    example: 'Malawi',
    minLength: 3,
    maxLength: 50
  })
  @IsNotEmpty({ message: "Country is required!"})
  @IsString({ message: "Country invalid!" })
  @MinLength(3, { message: "Country must be at least 3 characters long!" })
  @MaxLength(50, { message: "Country must not exceed 50 characters!" })
  country: string;

  @ApiProperty({
    description: 'City',
    example: 'Lilongwe',
    minLength: 3,
    maxLength: 50
  })
  @IsNotEmpty({ message: "City is required!"})
  @IsString({ message: "City invalid!" })
  @MinLength(3, { message: "City must be at least 3 characters long!" })
  @MaxLength(50, { message: "City must not exceed 50 characters!" })
  city: string;
}
