'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useLicenseData } from '@/hooks/useLicenseData';
import { LicenseType } from '@/services/licenseTypeService';
import { LicenseCategory } from '@/services/licenseCategoryService';
import { getConfigByLicenseTypeName } from '@/config/applicationConfig';

const CategoryDetailPage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const { licenseTypes, loading: licenseLoading, getCategoriesByType } = useLicenseData();

  const [selectedLicenseType, setSelectedLicenseType] = useState<LicenseType | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<LicenseCategory | null>(null);
  const [isApplying, setIsApplying] = useState(false);

  const licenseTypeId = params.licenseTypeId as string;
  const categoryId = params.categoryId as string;



  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }

    if (licenseTypes && licenseTypeId) {
      const licenseType = licenseTypes.find(lt => lt.license_type_id === licenseTypeId);
      if (licenseType) {
        setSelectedLicenseType(licenseType);

        // Find the selected category
        const typeCategories = getCategoriesByType(licenseTypeId);
        const category = typeCategories.find(cat => cat.license_category_id === categoryId);
        if (category) {
          setSelectedCategory(category);
        } else {
          // Category not found, redirect back
          router.push(`/customer/applications/${licenseTypeId}`);
        }
      } else {
        // License type not found, redirect back
        router.push('/customer/applications');
      }
    }
  }, [isAuthenticated, authLoading, router, licenseTypes, licenseTypeId, categoryId, getCategoriesByType]);

  const handleApplyNow = () => {
    setIsApplying(true);
    // Navigate to the application form using the new apply route
    router.push(`/customer/applications/apply/${licenseTypeId}/${categoryId}/applicant-info`);
  };

  const handleBackToCategory = () => {
    router.push(`/customer/applications/${licenseTypeId}`);
  };

  const handleBackToApplications = () => {
    router.push('/customer/applications');
  };

  const getCategoryIcon = (categoryName: string) => {
    const nameLower = categoryName.toLowerCase();

    if (nameLower.includes('postal') || nameLower.includes('mail')) {
      return {
        icon: 'ri-mail-line',
        iconBg: 'bg-blue-100 dark:bg-blue-900/20',
        iconColor: 'text-blue-600 dark:text-blue-400'
      };
    } else if (nameLower.includes('international')) {
      return {
        icon: 'ri-global-line',
        iconBg: 'bg-purple-100 dark:bg-purple-900/20',
        iconColor: 'text-purple-600 dark:text-purple-400'
      };
    } else if (nameLower.includes('domestic')) {
      return {
        icon: 'ri-truck-line',
        iconBg: 'bg-green-100 dark:bg-green-900/20',
        iconColor: 'text-green-600 dark:text-green-400'
      };
    } else if (nameLower.includes('telecom') || nameLower.includes('spectrum')) {
      return {
        icon: 'ri-signal-tower-line',
        iconBg: 'bg-indigo-100 dark:bg-indigo-900/20',
        iconColor: 'text-indigo-600 dark:text-indigo-400'
      };
    } else {
      return {
        icon: 'ri-file-text-line',
        iconBg: 'bg-gray-100 dark:bg-gray-700',
        iconColor: 'text-gray-600 dark:text-gray-400'
      };
    }
  };

  if (authLoading || licenseLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </CustomerLayout>
    );
  }

  if (!selectedLicenseType || !selectedCategory) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <i className="ri-error-warning-line text-4xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Category Not Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              The requested license category could not be found.
            </p>
            <div className="space-x-4">
              <button
                onClick={handleBackToCategory}
                className="bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
              >
                Back to Categories
              </button>
              <button
                onClick={handleBackToApplications}
                className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary-dark transition-colors"
              >
                Back to License Types
              </button>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  const config = getConfigByLicenseTypeName(selectedLicenseType.name);
  const iconData = getCategoryIcon(selectedCategory.name);

  return (
    <CustomerLayout>
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center mb-4">
            <button
              onClick={handleBackToCategory}
              className="mr-4 p-2 text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              title="Back to Categories"
            >
              <i className="ri-arrow-left-line text-xl"></i>
            </button>
            <div className="flex items-center">
              <div className={`w-12 h-12 ${iconData.iconBg} rounded-full flex items-center justify-center mr-4`}>
                <i className={`${iconData.icon} text-xl ${iconData.iconColor}`}></i>
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                  {selectedCategory.name}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-1">
                  {selectedLicenseType.name}
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Category Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                About This License Category
              </h2>
              <p className="text-gray-600 dark:text-gray-400 mb-6 leading-relaxed">
                {selectedCategory.description || 'This license category allows you to operate within the specified regulatory framework. Please review all requirements before proceeding with your application.'}
              </p>

              {/* Requirements */}
              {config && config.requirements.length > 0 && (
                <div className="mb-6">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                    <i className="ri-file-list-3-line mr-2 text-primary"></i>
                    Required Documents
                  </h3>
                  <div className="grid grid-cols-1 gap-2">
                    {config.requirements.map((requirement, index) => (
                      <div key={index} className="flex items-start">
                        <i className="ri-checkbox-line text-primary mr-2 mt-0.5 flex-shrink-0"></i>
                        <span className="text-sm text-gray-700 dark:text-gray-300">{requirement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Application Process */}
              {config && config.steps.length > 0 && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-3">
                    <i className="ri-route-line mr-2 text-primary"></i>
                    Application Process
                  </h3>
                  <div className="space-y-3">
                    {config.steps.map((step, index) => (
                      <div key={step.id} className="flex items-start">
                        <div className="w-6 h-6 bg-primary text-white rounded-full flex items-center justify-center text-xs font-medium mr-3 mt-0.5 flex-shrink-0">
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-gray-100">{step.name}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">{step.description}</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 sticky top-8">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
                Application Details
              </h3>

              {/* Fee Information */}
              <div className="mb-6">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Application Fee:</span>
                  <span className="font-semibold text-primary">
                    {selectedCategory.fee ? `MWK ${selectedCategory.fee}` : 'Contact MACRA'}
                  </span>
                </div>
                {config && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">Estimated Time:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                      {config.estimatedTime}
                    </span>
                  </div>
                )}
              </div>

              {/* Apply Button */}
              <button
                onClick={handleApplyNow}
                disabled={isApplying}
                className="w-full bg-primary text-white py-3 px-4 rounded-lg font-medium hover:bg-primary-dark transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center mb-4"
              >
                {isApplying ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Loading Application...
                  </>
                ) : (
                  <>
                    <i className="ri-file-add-line mr-2"></i>
                    Start Application
                  </>
                )}
              </button>

              {/* Back Button */}
              <button
                onClick={handleBackToCategory}
                className="w-full bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 py-2 px-4 rounded-lg font-medium hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors flex items-center justify-center"
              >
                <i className="ri-arrow-left-line mr-2"></i>
                Back to Categories
              </button>
            </div>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default CategoryDetailPage;
