// Script to create missing applicantInfo section for application
// Usage: node create-missing-applicant-info.js

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001'; // Adjust to your backend URL
const APPLICATION_ID = 'a6a29647-ba0f-482e-8e10-8dabf3902d43';

// You'll need to get a valid JWT token from your authentication system
const JWT_TOKEN = 'your-jwt-token-here';

const createApplicantInfoSection = async () => {
  try {
    // Sample applicant info data - adjust as needed
    const applicantInfoData = {
      application_id: APPLICATION_ID,
      section_name: 'applicantInfo',
      section_data: {
        first_name: '',
        last_name: '',
        email: '',
        phone: '',
        address: '',
        // Add other required fields as needed
      },
      completed: false
    };

    const response = await axios.post(
      `${API_BASE_URL}/application-form-data`,
      applicantInfoData,
      {
        headers: {
          'Authorization': `Bearer ${JWT_TOKEN}`,
          'Content-Type': 'application/json'
        }
      }
    );

    console.log('Successfully created applicantInfo section:', response.data);
  } catch (error) {
    console.error('Error creating applicantInfo section:', error.response?.data || error.message);
  }
};

createApplicantInfoSection();
