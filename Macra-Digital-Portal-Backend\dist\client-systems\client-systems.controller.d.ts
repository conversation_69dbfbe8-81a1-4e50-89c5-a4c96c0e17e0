import { ClientSystemsService } from './client-systems.service';
import { CreateClientSystemDto } from '../dto/client-system/create-client-system.dto';
import { UpdateClientSystemDto } from '../dto/client-system/update-client-system.dto';
import { ClientSystems } from '../entities/client-systems.entity';
import { PaginateQuery } from 'nestjs-paginate';
import { PaginatedResult } from '../common/interfaces/pagination.interface';
export declare class ClientSystemsController {
    private readonly clientSystemsService;
    constructor(clientSystemsService: ClientSystemsService);
    create(createClientSystemDto: CreateClientSystemDto, req: any): Promise<ClientSystems>;
    findAll(query: PaginateQuery): Promise<PaginatedResult<ClientSystems>>;
    getStats(): Promise<{
        total: number;
        active: number;
        inactive: number;
        maintenance: number;
        deprecated: number;
        byType: Record<string, number>;
    }>;
    findOne(id: string): Promise<ClientSystems>;
    findBySystemCode(systemCode: string): Promise<ClientSystems>;
    update(id: string, updateClientSystemDto: UpdateClientSystemDto, req: any): Promise<ClientSystems>;
    updateLastAccessed(id: string): Promise<{
        message: string;
    }>;
    remove(id: string): Promise<{
        message: string;
    }>;
}
