"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UpdateApplicationFormDataDto = void 0;
const class_validator_1 = require("class-validator");
const swagger_1 = require("@nestjs/swagger");
class UpdateApplicationFormDataDto {
    section_data;
    completed;
}
exports.UpdateApplicationFormDataDto = UpdateApplicationFormDataDto;
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Section data as JSON object',
        example: {
            first_name: 'John',
            last_name: 'Doe',
            email: '<EMAIL>'
        }
    }),
    (0, class_validator_1.IsObject)(),
    __metadata("design:type", Object)
], UpdateApplicationFormDataDto.prototype, "section_data", void 0);
__decorate([
    (0, swagger_1.ApiProperty)({
        description: 'Whether the section is completed',
        example: true,
        default: false
    }),
    (0, class_validator_1.IsOptional)(),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], UpdateApplicationFormDataDto.prototype, "completed", void 0);
//# sourceMappingURL=update-application-form-data.dto.js.map