"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationFormDataService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const application_form_data_entity_1 = require("../entities/application-form-data.entity");
let ApplicationFormDataService = class ApplicationFormDataService {
    applicationFormDataRepository;
    constructor(applicationFormDataRepository) {
        this.applicationFormDataRepository = applicationFormDataRepository;
    }
    async create(createApplicationFormDataDto, userId) {
        const existingSection = await this.applicationFormDataRepository.findOne({
            where: {
                application_id: createApplicationFormDataDto.application_id,
                section_name: createApplicationFormDataDto.section_name,
            },
        });
        if (existingSection) {
            throw new common_1.ConflictException(`Section '${createApplicationFormDataDto.section_name}' already exists for application '${createApplicationFormDataDto.application_id}'`);
        }
        const formData = this.applicationFormDataRepository.create({
            ...createApplicationFormDataDto,
            completed: createApplicationFormDataDto.completed ?? false,
            created_by: userId,
        });
        return this.applicationFormDataRepository.save(formData);
    }
    async findByApplicationId(applicationId) {
        return this.applicationFormDataRepository.find({
            where: { application_id: applicationId },
            order: { created_at: 'ASC' },
        });
    }
    async findByApplicationAndSection(applicationId, sectionName) {
        const formData = await this.applicationFormDataRepository.findOne({
            where: {
                application_id: applicationId,
                section_name: sectionName,
            },
        });
        if (!formData) {
            throw new common_1.NotFoundException(`Form section '${sectionName}' not found for application '${applicationId}'`);
        }
        return formData;
    }
    async findByApplicationAndSectionOptional(applicationId, sectionName) {
        const formData = await this.applicationFormDataRepository.findOne({
            where: {
                application_id: applicationId,
                section_name: sectionName,
            },
        });
        return formData || null;
    }
    async update(applicationId, sectionName, updateApplicationFormDataDto, userId) {
        const formData = await this.findByApplicationAndSection(applicationId, sectionName);
        Object.assign(formData, {
            ...updateApplicationFormDataDto,
            updated_by: userId,
        });
        return this.applicationFormDataRepository.save(formData);
    }
    async upsert(createApplicationFormDataDto, userId) {
        try {
            const existingSection = await this.findByApplicationAndSection(createApplicationFormDataDto.application_id, createApplicationFormDataDto.section_name);
            return this.update(createApplicationFormDataDto.application_id, createApplicationFormDataDto.section_name, {
                section_data: createApplicationFormDataDto.section_data,
                completed: createApplicationFormDataDto.completed,
            }, userId);
        }
        catch (error) {
            if (error instanceof common_1.NotFoundException) {
                return this.create(createApplicationFormDataDto, userId);
            }
            throw error;
        }
    }
    async remove(applicationId, sectionName) {
        const formData = await this.findByApplicationAndSection(applicationId, sectionName);
        await this.applicationFormDataRepository.softDelete(formData.form_data_id);
    }
    async removeByApplicationId(applicationId) {
        await this.applicationFormDataRepository.softDelete({ application_id: applicationId });
    }
};
exports.ApplicationFormDataService = ApplicationFormDataService;
exports.ApplicationFormDataService = ApplicationFormDataService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(application_form_data_entity_1.ApplicationFormData)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ApplicationFormDataService);
//# sourceMappingURL=application-form-data.service.js.map