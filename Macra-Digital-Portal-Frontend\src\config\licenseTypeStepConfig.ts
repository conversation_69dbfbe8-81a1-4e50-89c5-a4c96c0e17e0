/**
 * License Type Step Configuration System
 * Defines which form steps are required for each license type
 */

export interface StepConfig {
  id: string;
  name: string;
  component: string;
  route: string;
  required: boolean;
  description: string;
  estimatedTime: string; // in minutes
}

export interface LicenseTypeStepConfig {
  licenseTypeId: string;
  name: string;
  description: string;
  steps: StepConfig[];
  estimatedTotalTime: string;
  requirements: string[];
}

// Base steps that can be used across license types
const BASE_STEPS: Record<string, StepConfig> = {
  applicantInfo: {
    id: 'applicant-info',
    name: 'Applicant Information',
    component: 'ApplicantInfo',
    route: 'applicant-info',
    required: true,
    description: 'Personal or company information of the applicant',
    estimatedTime: '5'
  },
  companyProfile: {
    id: 'company-profile',
    name: 'Company Profile',
    component: 'CompanyProfile',
    route: 'company-profile',
    required: true,
    description: 'Company structure, shareholders, and directors',
    estimatedTime: '10'
  },
  management: {
    id: 'management',
    name: 'Management Structure',
    component: 'Management',
    route: 'management',
    required: false,
    description: 'Management team and organizational structure',
    estimatedTime: '8'
  },
  professionalServices: {
    id: 'professional-services',
    name: 'Professional Services',
    component: 'ProfessionalServices',
    route: 'professional-services',
    required: false,
    description: 'External consultants and service providers',
    estimatedTime: '6'
  },
  businessInfo: {
    id: 'business-info',
    name: 'Business Information',
    component: 'BusinessInfo',
    route: 'business-info',
    required: true,
    description: 'Business description and operational plan',
    estimatedTime: '7'
  },
  serviceScope: {
    id: 'service-scope',
    name: 'Service Scope',
    component: 'ServiceScope',
    route: 'service-scope',
    required: true,
    description: 'Services offered and geographic coverage',
    estimatedTime: '8'
  },
  businessPlan: {
    id: 'business-plan',
    name: 'Business Plan',
    component: 'BusinessPlan',
    route: 'business-plan',
    required: true,
    description: 'Market analysis and financial projections',
    estimatedTime: '15'
  },
  legalHistory: {
    id: 'legal-history',
    name: 'Legal History',
    component: 'LegalHistory',
    route: 'legal-history',
    required: true,
    description: 'Legal compliance and regulatory history',
    estimatedTime: '5'
  },
  reviewSubmit: {
    id: 'review-submit',
    name: 'Review & Submit',
    component: 'ReviewSubmit',
    route: 'review-submit',
    required: true,
    description: 'Review all information and submit application',
    estimatedTime: '10'
  }
};

// License type specific configurations
export const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {
  telecommunications: {
    licenseTypeId: 'telecommunications',
    name: 'Telecommunications License',
    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.companyProfile,
      BASE_STEPS.management,
      BASE_STEPS.professionalServices,
      BASE_STEPS.businessInfo,
      BASE_STEPS.serviceScope,
      BASE_STEPS.businessPlan,
      BASE_STEPS.legalHistory,
      BASE_STEPS.reviewSubmit
    ],
    estimatedTotalTime: '74 minutes',
    requirements: [
      'Business registration certificate',
      'Tax compliance certificate',
      'Technical specifications',
      'Financial statements',
      'Management CVs',
      'Network coverage plans'
    ]
  },

  postal_services: {
    licenseTypeId: 'postal_services',
    name: 'Postal Services License',
    description: 'License for postal and courier service providers',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.companyProfile,
      BASE_STEPS.businessInfo,
      BASE_STEPS.businessPlan,
      BASE_STEPS.legalHistory,
      BASE_STEPS.reviewSubmit
    ],
    estimatedTotalTime: '42 minutes',
    requirements: [
      'Business registration certificate',
      'Fleet inventory',
      'Service coverage map',
      'Insurance certificates',
      'Premises documentation'
    ]
  },

  standards_compliance: {
    licenseTypeId: 'standards_compliance',
    name: 'Standards Compliance License',
    description: 'License for standards compliance and certification services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.companyProfile,
      BASE_STEPS.management,
      BASE_STEPS.professionalServices,
      BASE_STEPS.businessInfo,
      BASE_STEPS.serviceScope,
      BASE_STEPS.legalHistory,
      BASE_STEPS.reviewSubmit
    ],
    estimatedTotalTime: '59 minutes',
    requirements: [
      'Accreditation certificates',
      'Technical competency proof',
      'Quality management system',
      'Laboratory facilities documentation',
      'Staff qualifications'
    ]
  },

  broadcasting: {
    licenseTypeId: 'broadcasting',
    name: 'Broadcasting License',
    description: 'License for radio and television broadcasting services',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.companyProfile,
      BASE_STEPS.management,
      BASE_STEPS.businessInfo,
      BASE_STEPS.serviceScope,
      BASE_STEPS.businessPlan,
      BASE_STEPS.legalHistory,
      BASE_STEPS.reviewSubmit
    ],
    estimatedTotalTime: '63 minutes',
    requirements: [
      'Broadcasting equipment specifications',
      'Content programming plan',
      'Studio facility documentation',
      'Transmission coverage maps',
      'Local content compliance plan'
    ]
  },

  spectrum_management: {
    licenseTypeId: 'spectrum_management',
    name: 'Spectrum Management License',
    description: 'License for radio frequency spectrum management and allocation',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.companyProfile,
      BASE_STEPS.management,
      BASE_STEPS.professionalServices,
      BASE_STEPS.businessInfo,
      BASE_STEPS.serviceScope,
      BASE_STEPS.businessPlan,
      BASE_STEPS.legalHistory,
      BASE_STEPS.reviewSubmit
    ],
    estimatedTotalTime: '74 minutes',
    requirements: [
      'Spectrum usage plan',
      'Technical interference analysis',
      'Equipment type approval',
      'Frequency coordination agreements',
      'Monitoring capabilities documentation'
    ]
  },

  clf: {
    licenseTypeId: 'clf',
    name: 'CLF License',
    description: 'Consumer Lending and Finance license',
    steps: [
      BASE_STEPS.applicantInfo,
      BASE_STEPS.companyProfile,
      BASE_STEPS.management,
      BASE_STEPS.businessInfo,
      BASE_STEPS.businessPlan,
      BASE_STEPS.legalHistory,
      BASE_STEPS.reviewSubmit
    ],
    estimatedTotalTime: '50 minutes',
    requirements: [
      'Financial institution license',
      'Capital adequacy documentation',
      'Risk management framework',
      'Consumer protection policies',
      'Anti-money laundering procedures'
    ]
  }
};

// License type name to config key mapping
const LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {
  'telecommunications': 'telecommunications',
  'postal services': 'postal_services',
  'postal_services': 'postal_services',
  'standards compliance': 'standards_compliance',
  'standards_compliance': 'standards_compliance',
  'broadcasting': 'broadcasting',
  'spectrum management': 'spectrum_management',
  'spectrum_management': 'spectrum_management',
  'clf': 'clf',
  'consumer lending and finance': 'clf'
};

// Helper functions
export const getLicenseTypeStepConfig = (licenseTypeId: string): LicenseTypeStepConfig | null => {

  // Check if licenseTypeId is valid
  if (!licenseTypeId || typeof licenseTypeId !== 'string') {
    console.log('Invalid licenseTypeId provided:', licenseTypeId);
    return null;
  }

  // First try direct lookup
  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];
  if (config) {
    console.log('Found config via direct lookup:', config.name);
    return config;
  }

  // Try normalized lookup
  const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');
  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];
  if (config) {
    console.log('Found config via normalized lookup:', config.name);
    return config;
  }

  // Try name mapping
  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];
  if (mappedKey) {
    console.log('Found config via name mapping:', mappedKey);
    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];
  }

  // If licenseTypeId looks like a UUID, try to get the code from license types
  if (isUUID(licenseTypeId)) {
    console.log('Detected UUID, trying to get code...');
    const code = getLicenseTypeCodeFromUUID(licenseTypeId);
    console.log('Got code from UUID:', code);
    if (code) {
      const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];
      if (foundConfig) {
        console.log('Found config via UUID mapping:', foundConfig.name);
        return foundConfig;
      }
    }
  }

  console.log('No config found for license type:', licenseTypeId);
  return null;
};

// Helper function to check if a string is a UUID
const isUUID = (str: string): boolean => {
  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
  return uuidRegex.test(str);
};

// Helper function to get license type code from UUID
// This will be populated by the license type service
let licenseTypeUUIDToCodeMap: Record<string, string> = {};

export const setLicenseTypeUUIDToCodeMap = (map: Record<string, string>) => {
  licenseTypeUUIDToCodeMap = map;
};

const getLicenseTypeCodeFromUUID = (uuid: string): string | null => {
  return licenseTypeUUIDToCodeMap[uuid] || null;
};

export const getStepByRoute = (licenseTypeId: string, stepRoute: string): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  if (!config) return null;
  
  return config.steps.find(step => step.route === stepRoute) || null;
};

export const getStepByIndex = (licenseTypeId: string, stepIndex: number): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  if (!config || stepIndex < 0 || stepIndex >= config.steps.length) return null;
  
  return config.steps[stepIndex];
};

export const getStepIndex = (licenseTypeId: string, stepRoute: string): number => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  if (!config) return -1;
  
  return config.steps.findIndex(step => step.route === stepRoute);
};

export const getTotalSteps = (licenseTypeId: string): number => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  return config ? config.steps.length : 0;
};

export const getRequiredSteps = (licenseTypeId: string): StepConfig[] => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  return config ? config.steps.filter(step => step.required) : [];
};

export const getOptionalSteps = (licenseTypeId: string): StepConfig[] => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  return config ? config.steps.filter(step => !step.required) : [];
};

export const calculateProgress = (licenseTypeId: string, completedSteps: string[]): number => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  if (!config) return 0;
  
  const totalSteps = config.steps.length;
  const completed = completedSteps.length;
  
  return Math.round((completed / totalSteps) * 100);
};

export const getNextStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  if (!config) return null;
  
  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);
  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;
  
  return config.steps[currentIndex + 1];
};

export const getPreviousStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {
  const config = getLicenseTypeStepConfig(licenseTypeId);
  if (!config) return null;
  
  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);
  if (currentIndex <= 0) return null;
  
  return config.steps[currentIndex - 1];
};
