(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/services/applicationFormDataService.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_services_applicationFormDataService_ts_485e25d6._.js",
  "static/chunks/src_services_applicationFormDataService_ts_ae48ac97._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/services/applicationFormDataService.ts [app-client] (ecmascript)");
    });
});
}}),
}]);