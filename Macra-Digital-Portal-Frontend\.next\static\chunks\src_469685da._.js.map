{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Consumer Affairs',\r\n      href: '/customer/consumer-affairs',\r\n      icon: 'ri-shield-user-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/consumer-affairs',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/standards': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/consumer-affairs': 'Loading Consumer Affairs...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM;gBACpC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;aACD;kDAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM;gBACjC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;+CAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,MAAM,gBAAgB;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,cAAc,OAAO;kEAAC,CAAA;4BACpB,OAAO,QAAQ,CAAC;wBAClB;;gBACF;;YAEA,4DAA4D;YAC5D,MAAM,QAAQ,WAAW,eAAe;YACxC;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,uBAAuB,CAAC;QAC1B;0DAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAChD,MAAM,eAA0C;gBAC9C,aAAa;gBACb,sBAAsB;gBACtB,0BAA0B;gBAC1B,oCAAoC;gBACpC,sBAAsB;gBACtB,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,8BAA8B;gBAC9B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;YAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;YAC1D,WAAW;YACX,uBAAuB;QACzB;qDAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,2CAA2C;YAC3C,OAAO,QAAQ,CAAC;QAClB;qDAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,sBAAsB,CAAC;QACzB;yDAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,qCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,6LAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,6LAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,6LAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,6LAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,6LAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,6LAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,6LAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,gIAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,6LAAC,qIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlVM;;QAGa,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACC,kIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,aAAU;;;KAN7B;uCAoVS", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/TextInput.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  className?: string;\n  containerClassName?: string;\n}\n\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  className = '',\n  containerClassName = '',\n  id,\n  ...props\n}, ref) => {\n  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseInputClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n  `;\n  \n  const inputClasses = error\n    ? `${baseInputClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseInputClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={inputId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <input\n        ref={ref}\n        id={inputId}\n        className={`${inputClasses} ${className}`}\n        {...props}\n      />\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nTextInput.displayName = 'TextInput';\n\nexport default TextInput;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,0BAAY,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,EAAE,EACF,GAAG,OACJ,EAAE;IACD,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAExE,MAAM,mBAAmB,CAAC;;;;;;EAM1B,CAAC;IAED,MAAM,eAAe,QACjB,GAAG,iBAAiB,2EAA2E,CAAC,GAChG,GAAG,iBAAiB,0GAA0G,CAAC;IAEnI,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,GAAG,aAAa,CAAC,EAAE,WAAW;gBACxC,GAAG,KAAK;;;;;;YAGV,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 1021, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface SelectOption {\n  value: string;\n  label: string;\n  disabled?: boolean;\n}\n\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  options: SelectOption[];\n  placeholder?: string;\n  className?: string;\n  containerClassName?: string;\n  onChange?: (value: string) => void;\n}\n\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  options = [],\n  placeholder = 'Select an option...',\n  className = '',\n  containerClassName = '',\n  onChange,\n  id,\n  value,\n  ...props\n}, ref) => {\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseSelectClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  `;\n  \n  const selectClasses = error\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    if (onChange) {\n      onChange(e.target.value);\n    }\n  };\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={selectId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <select\n          ref={ref}\n          id={selectId}\n          value={value || ''}\n          onChange={handleChange}\n          className={`${selectClasses} ${className}`}\n          {...props}\n        >\n          {placeholder && (\n            <option value=\"\" disabled>\n              {placeholder}\n            </option>\n          )}\n          \n          {options.map((option) => (\n            <option \n              key={option.value} \n              value={option.value}\n              disabled={option.disabled}\n            >\n              {option.label}\n            </option>\n          ))}\n        </select>\n        \n        {/* Custom dropdown arrow */}\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\n        </div>\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nSelect.displayName = 'Select';\n\nexport default Select;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,OAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,oBAAoB,CAAC;;;;;;;;;EAS3B,CAAC;IAED,MAAM,gBAAgB,QAClB,GAAG,kBAAkB,2EAA2E,CAAC,GACjG,GAAG,kBAAkB,0GAA0G,CAAC;IAEpI,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,6LAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,6LAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW;wBACzC,GAAG,KAAK;;4BAER,6BACC,6LAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,6LAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,6LAAC;gBAAE,WAAU;;kCACX,6LAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,6LAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationFormDataService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { processApiResponse } from '@/lib/authUtils';\n\nexport interface ApplicationFormData {\n  form_data_id?: string;\n  application_id: string;\n  section_name: string;\n  section_data: Record<string, any>;\n  completed: boolean;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport const applicationFormDataService = {\n  // Save form section data\n  async saveFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to saveFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to saveFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Saving form section ${sectionName} for application ${applicationId}:`, sectionData);\n\n      const response = await apiClient.post('/application-form-data', {\n        application_id: applicationId,\n        section_name: sectionName,\n        section_data: sectionData,\n        completed: true\n      });\n\n      console.log(`Form section ${sectionName} saved successfully:`, response.data);\n      return processApiResponse(response);\n    } catch (error) {\n      console.error(`Error saving form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Update existing form section data\n  async updateFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to updateFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to updateFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Updating form section ${sectionName} for application ${applicationId}:`, sectionData);\n\n      const response = await apiClient.put(`/application-form-data/${applicationId}/${sectionName}`, {\n        section_data: sectionData,\n        completed: true\n      });\n\n      console.log(`Form section ${sectionName} updated successfully:`, response.data);\n      return processApiResponse(response);\n    } catch (error) {\n      console.error(`Error updating form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Get form section data\n  async getFormSection(applicationId: string, sectionName: string): Promise<ApplicationFormData | null> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      console.warn('Invalid applicationId provided to getFormSection:', applicationId);\n      return null;\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      console.warn('Invalid sectionName provided to getFormSection:', sectionName);\n      return null;\n    }\n\n    try {\n      console.log(`Fetching form section ${sectionName} for application ${applicationId}`);\n      const response = await apiClient.get(`/application-form-data/${applicationId}/${sectionName}`);\n      return processApiResponse(response);\n    } catch (error) {\n      if ((error as any)?.response?.status === 404) {\n        console.log(`Form section ${sectionName} not found for application ${applicationId} - this is normal for new applications`);\n        return null; // Section doesn't exist yet\n      }\n      console.error(`Error fetching form section ${sectionName} for application ${applicationId}:`, error);\n      throw error;\n    }\n  },\n\n  // Get all form data for an application\n  async getApplicationFormData(applicationId: string): Promise<Record<string, any>> {\n    try {\n      const response = await apiClient.get(`/application-form-data/${applicationId}`);\n      const processedResponse = processApiResponse(response);\n\n      // Convert array of sections to object\n      const formData: Record<string, any> = {};\n      if (Array.isArray(processedResponse)) {\n        processedResponse.forEach((section: ApplicationFormData) => {\n          formData[section.section_name] = section.section_data;\n        });\n      }\n\n      return formData;\n    } catch (error) {\n      console.error('Error fetching application form data:', error);\n      return {}; // Return empty object if no data found\n    }\n  },\n\n  // Delete form section\n  async deleteFormSection(applicationId: string, sectionName: string): Promise<{ message: string }> {\n    try {\n      const response = await apiClient.delete(`/application-form-data/${applicationId}/${sectionName}`);\n      return response.data;\n    } catch (error) {\n      console.error(`Error deleting form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Save or update form section (upsert)\n  async saveOrUpdateFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before proceeding\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to saveOrUpdateFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to saveOrUpdateFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Saving/updating form section ${sectionName} for application ${applicationId}`);\n\n      // Try to get existing section first\n      const existingSection = await this.getFormSection(applicationId, sectionName);\n\n      if (existingSection) {\n        // Update existing section\n        console.log(`Updating existing section ${sectionName}`);\n        return await this.updateFormSection(applicationId, sectionName, sectionData);\n      } else {\n        // Create new section\n        console.log(`Creating new section ${sectionName}`);\n        return await this.saveFormSection(applicationId, sectionName, sectionData);\n      }\n    } catch (error) {\n      console.error(`Error saving/updating form section ${sectionName}:`, error);\n      throw error;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAYO,MAAM,6BAA6B;IACxC,yBAAyB;IACzB,MAAM,iBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,mDAAmD,EAAE,eAAe;QACvF;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,iDAAiD,EAAE,aAAa;QACnF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAEpF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,0BAA0B;gBAC9D,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,oBAAoB,CAAC,EAAE,SAAS,IAAI;YAC5E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC3D,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,MAAM,mBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,qDAAqD,EAAE,eAAe;QACzF;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,mDAAmD,EAAE,aAAa;QACrF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAEtF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa,EAAE;gBAC7F,cAAc;gBACd,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,sBAAsB,CAAC,EAAE,SAAS,IAAI;YAC9E,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC7D,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,gBAAe,aAAqB,EAAE,WAAmB;QAC7D,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,QAAQ,IAAI,CAAC,qDAAqD;YAClE,OAAO;QACT;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,QAAQ,IAAI,CAAC,mDAAmD;YAChE,OAAO;QACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,iBAAiB,EAAE,eAAe;YACnF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,IAAI,AAAC,OAAe,UAAU,WAAW,KAAK;gBAC5C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,2BAA2B,EAAE,cAAc,sCAAsC,CAAC;gBAC1H,OAAO,MAAM,4BAA4B;YAC3C;YACA,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAC9F,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,MAAM,wBAAuB,aAAqB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,eAAe;YAC9E,MAAM,oBAAoB,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,sCAAsC;YACtC,MAAM,WAAgC,CAAC;YACvC,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBACpC,kBAAkB,OAAO,CAAC,CAAC;oBACzB,QAAQ,CAAC,QAAQ,YAAY,CAAC,GAAG,QAAQ,YAAY;gBACvD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO,CAAC,GAAG,uCAAuC;QACpD;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,aAAqB,EAAE,WAAmB;QAChE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa;YAChG,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC7D,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,MAAM,yBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,oCAAoC;QACpC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,2DAA2D,EAAE,eAAe;QAC/F;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,yDAAyD,EAAE,aAAa;QAC3F;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,iBAAiB,EAAE,eAAe;YAE1F,oCAAoC;YACpC,MAAM,kBAAkB,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe;YAEjE,IAAI,iBAAiB;gBACnB,0BAA0B;gBAC1B,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,aAAa;gBACtD,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,aAAa;YAClE,OAAO;gBACL,qBAAqB;gBACrB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,aAAa;gBACjD,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,aAAa;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,YAAY,CAAC,CAAC,EAAE;YACpE,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1314, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    console.log('ApplicationService.createApplication called with:', data);\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ApplicationService.createApplication error:', error);\r\n      console.error('Error response:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return response.data;\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      console.log('Creating application with data:', data);\r\n\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      console.log('Creating application with:', {\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id,\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft'\r\n      });\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 14 // ~1/7 steps completed\r\n      });\r\n\r\n      console.log('Application created:', application);\r\n\r\n      // Save applicant form data separately if needed\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        await applicationFormDataService.saveFormSection(\r\n          application.application_id,\r\n          'applicantInfo',\r\n          data.applicant_data\r\n        );\r\n        console.log('Applicant form data saved');\r\n      } catch (formDataError) {\r\n        console.warn('Could not save form data separately, continuing...', formDataError);\r\n        // Continue even if form data saving fails\r\n      }\r\n\r\n      console.log('Application created successfully');\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n\r\n        // Save section data using form data service\r\n        await applicationFormDataService.saveOrUpdateFormSection(\r\n          applicationId,\r\n          sectionName,\r\n          sectionData\r\n        );\r\n\r\n        // Get all form data to calculate progress\r\n        const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n        completedSections = Object.keys(allFormData).length;\r\n\r\n        console.log(`Form data saved using form data service`);\r\n      } catch (formDataError) {\r\n        console.warn('Form data service not available, using basic progress tracking:', formDataError);\r\n\r\n        // Fallback: estimate progress based on section name\r\n        const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n        const sectionIndex = sectionOrder.indexOf(sectionName);\r\n        completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n      }\r\n\r\n      // Calculate progress based on completed sections\r\n      const totalSections = 7; // Total number of form sections\r\n      const progressPercentage = Math.round((completedSections / totalSections) * 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Get all applications - the backend should filter by authenticated user\r\n      const response = await apiClient.get('/applications');\r\n\r\n      console.log('Applications API response:', response.data);\r\n\r\n      // Handle different response structures\r\n      let applications = [];\r\n      if (response.data?.data) {\r\n        applications = Array.isArray(response.data.data) ? response.data.data : [];\r\n      } else if (Array.isArray(response.data)) {\r\n        applications = response.data;\r\n      } else if (response.data) {\r\n        // Single application or other structure\r\n        applications = [response.data];\r\n      }\r\n\r\n      console.log('Processed applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get form data from the form data service\r\n      let formData: Record<string, any> = {};\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        formData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n      } catch (formDataError) {\r\n        console.warn('Could not retrieve form data for validation:', formDataError);\r\n        // Continue with empty form data\r\n      }\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,QAAQ,GAAG,CAAC,qDAAqD;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ,KAAK,CAAC,mBAAoB,OAAe,UAAU;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;YACV;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,GAAG,uBAAuB;YACjD;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,gDAAgD;YAChD,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,MAAM,2BAA2B,eAAe,CAC9C,YAAY,cAAc,EAC1B,iBACA,KAAK,cAAc;gBAErB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,sDAAsD;YACnE,0CAA0C;YAC5C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBAEvC,4CAA4C;gBAC5C,MAAM,2BAA2B,uBAAuB,CACtD,eACA,aACA;gBAGF,0CAA0C;gBAC1C,MAAM,cAAc,MAAM,2BAA2B,sBAAsB,CAAC;gBAC5E,oBAAoB,OAAO,IAAI,CAAC,aAAa,MAAM;gBAEnD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,mEAAmE;gBAEhF,oDAAoD;gBACpD,MAAM,eAAe;oBAAC;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAgB;oBAAgB;iBAAe;gBACxI,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAC7D;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,GAAG,gCAAgC;YACzD,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB;YAE5E,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,SAAS,IAAI;YAChE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YAEvD,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,SAAS,IAAI,EAAE,MAAM;gBACvB,eAAe,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,EAAE;YAC5E,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACvC,eAAe,SAAS,IAAI;YAC9B,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,wCAAwC;gBACxC,eAAe;oBAAC,SAAS,IAAI;iBAAC;YAChC;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,2CAA2C;YAC3C,IAAI,WAAgC,CAAC;YACrC,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,WAAW,MAAM,2BAA2B,sBAAsB,CAAC;YACrE,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,gDAAgD;YAC7D,gCAAgC;YAClC;YAEA,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1613, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicantService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { Applicant } from '../types/license';\n\nexport interface CreateApplicantData {\n  name: string;\n  business_registration_number: string;\n  tpin: string;\n  website: string;\n  email: string;\n  phone: string;\n  fax?: string;\n  level_of_insurance_cover?: string;\n  address_id?: string;\n  contact_id?: string;\n  date_incorporation: string; // Changed from Date to string to match backend DTO\n  place_incorporation: string;\n}\n\nexport const applicantService = {\n  // Create new applicant\n  async createApplicant(data: CreateApplicantData): Promise<Applicant> {\n    try {\n      console.log('Creating applicant with data:', data);\n\n      const response = await apiClient.post('/applicants', data);\n      // Handle different response formats\n      if (response.data) {\n        // Check if it's a standard success response format\n        if (response.data.success !== undefined && response.data.data) {\n          console.log('Standard response format detected');\n          return response.data.data;\n        }\n        // Check if it's direct data format\n        else if (response.data.applicant_id || response.data.id) {\n          console.log('Direct data format detected');\n          return response.data;\n        }\n        // Fallback: assume it's the applicant data\n        else {\n          console.log('Fallback: treating response.data as applicant');\n          return response.data;\n        }\n      }\n\n      throw new Error('Invalid response format from applicant creation');\n    } catch (error) {\n      console.error('Error creating applicant:', error);\n      console.error('Error details:', (error as any)?.response?.data);\n      throw error;\n    }\n  },\n\n  // Get applicant by ID\n  async getApplicant(id: string): Promise<Applicant> {\n    try {\n      const response = await apiClient.get(`/applicants/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching applicant:', error);\n      throw error;\n    }\n  },\n\n  // Update applicant\n  async updateApplicant(id: string, data: Partial<CreateApplicantData>): Promise<Applicant> {\n    try {\n      console.log('Updating applicant:', id, data);\n      \n      const response = await apiClient.put(`/applicants/${id}`, data);\n      \n      console.log('Applicant updated successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating applicant:', error);\n      throw error;\n    }\n  },\n\n  // Get applicants by user (if user can have multiple applicants)\n  async getApplicantsByUser(): Promise<Applicant[]> {\n    try {\n      const response = await apiClient.get('/applicants/by-user');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user applicants:', error);\n      throw error;\n    }\n  },\n\n  // Delete applicant\n  async deleteApplicant(id: string): Promise<{ message: string }> {\n    try {\n      const response = await apiClient.delete(`/applicants/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting applicant:', error);\n      throw error;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AAkBO,MAAM,mBAAmB;IAC9B,uBAAuB;IACvB,MAAM,iBAAgB,IAAyB;QAC7C,IAAI;YACF,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,eAAe;YACrD,oCAAoC;YACpC,IAAI,SAAS,IAAI,EAAE;gBACjB,mDAAmD;gBACnD,IAAI,SAAS,IAAI,CAAC,OAAO,KAAK,aAAa,SAAS,IAAI,CAAC,IAAI,EAAE;oBAC7D,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAEK,IAAI,SAAS,IAAI,CAAC,YAAY,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACvD,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI;gBACtB,OAEK;oBACH,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI;gBACtB;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,QAAQ,KAAK,CAAC,kBAAmB,OAAe,UAAU;YAC1D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YACxD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAkC;QAClE,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB,IAAI;YAEvC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YAE1D,QAAQ,GAAG,CAAC,mCAAmC,SAAS,IAAI;YAC5D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,gEAAgE;IAChE,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formValidation.ts"], "sourcesContent": ["// Form validation utilities\r\n\r\nexport interface ValidationRule {\r\n  required?: boolean;\r\n  minLength?: number;\r\n  maxLength?: number;\r\n  pattern?: RegExp;\r\n  custom?: (value: any) => string | null;\r\n}\r\n\r\nexport interface ValidationErrors {\r\n  [key: string]: string;\r\n}\r\n\r\nexport const validateField = (value: any, rules: ValidationRule): string | null => {\r\n  // Required validation\r\n  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\r\n    return 'This field is required';\r\n  }\r\n\r\n  // Skip other validations if field is empty and not required\r\n  if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n    return null;\r\n  }\r\n\r\n  // String validations\r\n  if (typeof value === 'string') {\r\n    // Min length validation\r\n    if (rules.minLength && value.length < rules.minLength) {\r\n      return `Must be at least ${rules.minLength} characters`;\r\n    }\r\n\r\n    // Max length validation\r\n    if (rules.maxLength && value.length > rules.maxLength) {\r\n      return `Must be no more than ${rules.maxLength} characters`;\r\n    }\r\n\r\n    // Pattern validation\r\n    if (rules.pattern && !rules.pattern.test(value)) {\r\n      return 'Invalid format';\r\n    }\r\n  }\r\n\r\n  // Custom validation\r\n  if (rules.custom) {\r\n    return rules.custom(value);\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport const validateForm = (data: any, rules: { [key: string]: ValidationRule }): ValidationErrors => {\r\n  const errors: ValidationErrors = {};\r\n\r\n  Object.keys(rules).forEach(field => {\r\n    const error = validateField(data[field], rules[field]);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n// Common validation patterns\r\nexport const patterns = {\r\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n  phone: /^(\\+265|0)[0-9]{8,9}$/,\r\n  url: /^https?:\\/\\/.+/,\r\n  alphanumeric: /^[a-zA-Z0-9]+$/,\r\n  alphabetic: /^[a-zA-Z\\s]+$/,\r\n  numeric: /^[0-9]+$/,\r\n  percentage: /^(100|[1-9]?[0-9])$/\r\n};\r\n\r\n// Common validation rules\r\nexport const commonRules = {\r\n  required: { required: true },\r\n  email: { \r\n    required: true, \r\n    pattern: patterns.email,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.email.test(value)) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  phone: {\r\n    required: true,\r\n    pattern: patterns.phone,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.phone.test(value)) {\r\n        return 'Please enter a valid Malawi phone number (e.g., +265123456789 or 0123456789)';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  businessRegistration: {\r\n    required: true,\r\n    minLength: 5,\r\n    custom: (value: string) => {\r\n      if (value && value.length < 5) {\r\n        return 'Business registration number must be at least 5 characters';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  percentage: {\r\n    pattern: patterns.percentage,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.percentage.test(value)) {\r\n        return 'Please enter a valid percentage (0-100)';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n// Utility function to check if form has errors\r\nexport const hasErrors = (errors: ValidationErrors): boolean => {\r\n  return Object.keys(errors).length > 0;\r\n};\r\n\r\n// Utility function to get first error message\r\nexport const getFirstError = (errors: ValidationErrors): string | null => {\r\n  const firstKey = Object.keys(errors)[0];\r\n  return firstKey ? errors[firstKey] : null;\r\n};\r\n\r\n// Utility function to validate array fields\r\nexport const validateArrayField = (\r\n  array: any[], \r\n  rules: { [key: string]: ValidationRule },\r\n  minItems?: number,\r\n  maxItems?: number\r\n): { [key: string]: ValidationErrors } => {\r\n  const arrayErrors: { [key: string]: ValidationErrors } = {};\r\n\r\n  // Check array length\r\n  if (minItems && array.length < minItems) {\r\n    arrayErrors._array = { length: `Must have at least ${minItems} items` };\r\n  }\r\n  if (maxItems && array.length > maxItems) {\r\n    arrayErrors._array = { length: `Must have no more than ${maxItems} items` };\r\n  }\r\n\r\n  // Validate each item in array\r\n  array.forEach((item, index) => {\r\n    const itemErrors = validateForm(item, rules);\r\n    if (hasErrors(itemErrors)) {\r\n      arrayErrors[index] = itemErrors;\r\n    }\r\n  });\r\n\r\n  return arrayErrors;\r\n};\r\n\r\n// File validation\r\nexport const validateFile = (\r\n  file: File | null, \r\n  required: boolean = false,\r\n  maxSize: number = 10, // MB\r\n  allowedTypes: string[] = ['.pdf']\r\n): string | null => {\r\n  if (required && !file) {\r\n    return 'File is required';\r\n  }\r\n\r\n  if (!file) {\r\n    return null;\r\n  }\r\n\r\n  // Check file size\r\n  if (file.size > maxSize * 1024 * 1024) {\r\n    return `File size must be less than ${maxSize}MB`;\r\n  }\r\n\r\n  // Check file type\r\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\r\n  if (!allowedTypes.includes(fileExtension)) {\r\n    return `File type must be: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n// Section validation interface\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  errors: Record<string, string>;\r\n}\r\n\r\n// Validate section data for application forms\r\nexport const validateSection = (data: Record<string, any>, sectionName: string): ValidationResult => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  switch (sectionName) {\r\n    case 'applicantInfo':\r\n      // Required fields for applicant info\r\n      const applicantRequiredFields = [\r\n        'applicant_type', 'first_name', 'last_name', 'email', 'phone',\r\n        'national_id', 'date_of_birth', 'nationality', 'gender',\r\n        'postal_address', 'physical_address', 'city', 'district'\r\n      ];\r\n\r\n      applicantRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\r\n        errors.email = 'Please enter a valid email address';\r\n      }\r\n\r\n      // Phone validation\r\n      if (data.phone && !/^(\\+265|0)?[1-9]\\d{7,8}$/.test(data.phone)) {\r\n        errors.phone = 'Please enter a valid phone number';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'companyProfile':\r\n      const companyRequiredFields = [\r\n        'company_name', 'business_registration_number', 'tax_number', 'company_type',\r\n        'incorporation_date', 'incorporation_place', 'company_email', 'company_phone',\r\n        'company_address', 'company_city', 'company_district', 'number_of_employees',\r\n        'annual_revenue', 'business_description'\r\n      ];\r\n\r\n      companyRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.company_email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.company_email)) {\r\n        errors.company_email = 'Please enter a valid email address';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'businessInfo':\r\n      const businessRequiredFields = [\r\n        'business_model', 'operational_structure', 'target_market', 'competitive_advantage',\r\n        'facilities_description', 'equipment_description', 'operational_areas',\r\n        'service_delivery_model', 'quality_assurance', 'customer_support'\r\n      ];\r\n\r\n      businessRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'serviceScope':\r\n      const serviceScopeRequiredFields = [\r\n        'services_offered', 'geographic_coverage', 'service_categories',\r\n        'target_customers', 'service_capacity'\r\n      ];\r\n\r\n      serviceScopeRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'businessPlan':\r\n      const businessPlanRequiredFields = [\r\n        'executive_summary', 'market_analysis', 'financial_projections',\r\n        'revenue_model', 'investment_requirements', 'implementation_timeline',\r\n        'risk_analysis', 'success_metrics'\r\n      ];\r\n\r\n      businessPlanRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'legalHistory':\r\n      // Required fields\r\n      if (!data.compliance_record || data.compliance_record.trim() === '') {\r\n        errors.compliance_record = 'Compliance record is required';\r\n      }\r\n\r\n      // Declaration must be accepted\r\n      if (!data.declaration_accepted) {\r\n        errors.declaration_accepted = 'You must accept the declaration to proceed';\r\n      }\r\n\r\n      // Conditional validations\r\n      if (data.criminal_history && (!data.criminal_details || data.criminal_details.trim() === '')) {\r\n        errors.criminal_details = 'Please provide details of your criminal history';\r\n      }\r\n\r\n      if (data.bankruptcy_history && (!data.bankruptcy_details || data.bankruptcy_details.trim() === '')) {\r\n        errors.bankruptcy_details = 'Please provide details of your bankruptcy history';\r\n      }\r\n\r\n      if (data.regulatory_actions && (!data.regulatory_details || data.regulatory_details.trim() === '')) {\r\n        errors.regulatory_details = 'Please provide details of regulatory actions';\r\n      }\r\n\r\n      if (data.litigation_history && (!data.litigation_details || data.litigation_details.trim() === '')) {\r\n        errors.litigation_details = 'Please provide details of litigation history';\r\n      }\r\n\r\n      break;\r\n\r\n    default:\r\n      // No validation for unknown sections\r\n      break;\r\n  }\r\n\r\n  return {\r\n    isValid: Object.keys(errors).length === 0,\r\n    errors\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;AAcrB,MAAM,gBAAgB,CAAC,OAAY;IACxC,sBAAsB;IACtB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAG,GAAG;QACpF,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;QAChE,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,OAAO,UAAU,UAAU;QAC7B,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,iBAAiB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QACzD;QAEA,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,qBAAqB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QAC7D;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,IAAI,MAAM,MAAM,EAAE;QAChB,OAAO,MAAM,MAAM,CAAC;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,MAAW;IACtC,MAAM,SAA2B,CAAC;IAElC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACzB,MAAM,QAAQ,cAAc,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM;QACrD,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,OAAO;IACP,KAAK;IACL,cAAc;IACd,YAAY;IACZ,SAAS;IACT,YAAY;AACd;AAGO,MAAM,cAAc;IACzB,UAAU;QAAE,UAAU;IAAK;IAC3B,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,sBAAsB;QACpB,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;YACP,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,YAAY;QACV,SAAS,SAAS,UAAU;QAC5B,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,UAAU,CAAC,IAAI,CAAC,QAAQ;gBAC7C,OAAO;YACT;YACA,OAAO;QACT;IACF;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG;AACtC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,WAAW,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACvC,OAAO,WAAW,MAAM,CAAC,SAAS,GAAG;AACvC;AAGO,MAAM,qBAAqB,CAChC,OACA,OACA,UACA;IAEA,MAAM,cAAmD,CAAC;IAE1D,qBAAqB;IACrB,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC;QAAC;IACxE;IACA,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC;QAAC;IAC5E;IAEA,8BAA8B;IAC9B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,UAAU,aAAa;YACzB,WAAW,CAAC,MAAM,GAAG;QACvB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAC1B,MACA,WAAoB,KAAK,EACzB,UAAkB,EAAE,EACpB,eAAyB;IAAC;CAAO;IAEjC,IAAI,YAAY,CAAC,MAAM;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;QACrC,OAAO,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;IACnD;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IACxD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB;QACzC,OAAO,CAAC,mBAAmB,EAAE,aAAa,IAAI,CAAC,OAAO;IACxD;IAEA,OAAO;AACT;AASO,MAAM,kBAAkB,CAAC,MAA2B;IACzD,MAAM,SAAiC,CAAC;IAExC,OAAQ;QACN,KAAK;YACH,qCAAqC;YACrC,MAAM,0BAA0B;gBAC9B;gBAAkB;gBAAc;gBAAa;gBAAS;gBACtD;gBAAe;gBAAiB;gBAAe;gBAC/C;gBAAkB;gBAAoB;gBAAQ;aAC/C;YAED,wBAAwB,OAAO,CAAC,CAAA;gBAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAChE,OAAO,KAAK,GAAG;YACjB;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,2BAA2B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAC9D,OAAO,KAAK,GAAG;YACjB;YAEA;QAEF,KAAK;YACH,MAAM,wBAAwB;gBAC5B;gBAAgB;gBAAgC;gBAAc;gBAC9D;gBAAsB;gBAAuB;gBAAiB;gBAC9D;gBAAmB;gBAAgB;gBAAoB;gBACvD;gBAAkB;aACnB;YAED,sBAAsB,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,aAAa,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,aAAa,GAAG;gBAChF,OAAO,aAAa,GAAG;YACzB;YAEA;QAEF,KAAK;YACH,MAAM,yBAAyB;gBAC7B;gBAAkB;gBAAyB;gBAAiB;gBAC5D;gBAA0B;gBAAyB;gBACnD;gBAA0B;gBAAqB;aAChD;YAED,uBAAuB,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAoB;gBAAuB;gBAC3C;gBAAoB;aACrB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAqB;gBAAmB;gBACxC;gBAAiB;gBAA2B;gBAC5C;gBAAiB;aAClB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,kBAAkB;YAClB,IAAI,CAAC,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,IAAI,OAAO,IAAI;gBACnE,OAAO,iBAAiB,GAAG;YAC7B;YAEA,+BAA+B;YAC/B,IAAI,CAAC,KAAK,oBAAoB,EAAE;gBAC9B,OAAO,oBAAoB,GAAG;YAChC;YAEA,0BAA0B;YAC1B,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAC5F,OAAO,gBAAgB,GAAG;YAC5B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA;QAEF;YAEE;IACJ;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 2002, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/config/licenseTypeStepConfig.ts"], "sourcesContent": ["/**\n * License Type Step Configuration System\n * Defines which form steps are required for each license type\n */\n\nexport interface StepConfig {\n  id: string;\n  name: string;\n  component: string;\n  route: string;\n  required: boolean;\n  description: string;\n  estimatedTime: string; // in minutes\n}\n\nexport interface LicenseTypeStepConfig {\n  licenseTypeId: string;\n  name: string;\n  description: string;\n  steps: StepConfig[];\n  estimatedTotalTime: string;\n  requirements: string[];\n}\n\n// Base steps that can be used across license types\nconst BASE_STEPS: Record<string, StepConfig> = {\n  applicantInfo: {\n    id: 'applicant-info',\n    name: 'Applicant Information',\n    component: 'ApplicantInfo',\n    route: 'applicant-info',\n    required: true,\n    description: 'Personal or company information of the applicant',\n    estimatedTime: '5'\n  },\n  companyProfile: {\n    id: 'company-profile',\n    name: 'Company Profile',\n    component: 'CompanyProfile',\n    route: 'company-profile',\n    required: true,\n    description: 'Company structure, shareholders, and directors',\n    estimatedTime: '10'\n  },\n  management: {\n    id: 'management',\n    name: 'Management Structure',\n    component: 'Management',\n    route: 'management',\n    required: false,\n    description: 'Management team and organizational structure',\n    estimatedTime: '8'\n  },\n  professionalServices: {\n    id: 'professional-services',\n    name: 'Professional Services',\n    component: 'ProfessionalServices',\n    route: 'professional-services',\n    required: false,\n    description: 'External consultants and service providers',\n    estimatedTime: '6'\n  },\n  businessInfo: {\n    id: 'business-info',\n    name: 'Business Information',\n    component: 'BusinessInfo',\n    route: 'business-info',\n    required: true,\n    description: 'Business description and operational plan',\n    estimatedTime: '7'\n  },\n  serviceScope: {\n    id: 'service-scope',\n    name: 'Service Scope',\n    component: 'ServiceScope',\n    route: 'service-scope',\n    required: true,\n    description: 'Services offered and geographic coverage',\n    estimatedTime: '8'\n  },\n  businessPlan: {\n    id: 'business-plan',\n    name: 'Business Plan',\n    component: 'BusinessPlan',\n    route: 'business-plan',\n    required: true,\n    description: 'Market analysis and financial projections',\n    estimatedTime: '15'\n  },\n  legalHistory: {\n    id: 'legal-history',\n    name: 'Legal History',\n    component: 'LegalHistory',\n    route: 'legal-history',\n    required: true,\n    description: 'Legal compliance and regulatory history',\n    estimatedTime: '5'\n  },\n  reviewSubmit: {\n    id: 'review-submit',\n    name: 'Review & Submit',\n    component: 'ReviewSubmit',\n    route: 'review-submit',\n    required: true,\n    description: 'Review all information and submit application',\n    estimatedTime: '10'\n  }\n};\n\n// License type specific configurations\nexport const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {\n  telecommunications: {\n    licenseTypeId: 'telecommunications',\n    name: 'Telecommunications License',\n    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '74 minutes',\n    requirements: [\n      'Business registration certificate',\n      'Tax compliance certificate',\n      'Technical specifications',\n      'Financial statements',\n      'Management CVs',\n      'Network coverage plans'\n    ]\n  },\n\n  postal_services: {\n    licenseTypeId: 'postal_services',\n    name: 'Postal Services License',\n    description: 'License for postal and courier service providers',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '42 minutes',\n    requirements: [\n      'Business registration certificate',\n      'Fleet inventory',\n      'Service coverage map',\n      'Insurance certificates',\n      'Premises documentation'\n    ]\n  },\n\n  standards_compliance: {\n    licenseTypeId: 'standards_compliance',\n    name: 'Standards Compliance License',\n    description: 'License for standards compliance and certification services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '59 minutes',\n    requirements: [\n      'Accreditation certificates',\n      'Technical competency proof',\n      'Quality management system',\n      'Laboratory facilities documentation',\n      'Staff qualifications'\n    ]\n  },\n\n  broadcasting: {\n    licenseTypeId: 'broadcasting',\n    name: 'Broadcasting License',\n    description: 'License for radio and television broadcasting services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '63 minutes',\n    requirements: [\n      'Broadcasting equipment specifications',\n      'Content programming plan',\n      'Studio facility documentation',\n      'Transmission coverage maps',\n      'Local content compliance plan'\n    ]\n  },\n\n  spectrum_management: {\n    licenseTypeId: 'spectrum_management',\n    name: 'Spectrum Management License',\n    description: 'License for radio frequency spectrum management and allocation',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '74 minutes',\n    requirements: [\n      'Spectrum usage plan',\n      'Technical interference analysis',\n      'Equipment type approval',\n      'Frequency coordination agreements',\n      'Monitoring capabilities documentation'\n    ]\n  },\n\n  clf: {\n    licenseTypeId: 'clf',\n    name: 'CLF License',\n    description: 'Consumer Lending and Finance license',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '50 minutes',\n    requirements: [\n      'Financial institution license',\n      'Capital adequacy documentation',\n      'Risk management framework',\n      'Consumer protection policies',\n      'Anti-money laundering procedures'\n    ]\n  }\n};\n\n// License type name to config key mapping\nconst LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {\n  'telecommunications': 'telecommunications',\n  'postal services': 'postal_services',\n  'postal_services': 'postal_services',\n  'standards compliance': 'standards_compliance',\n  'standards_compliance': 'standards_compliance',\n  'broadcasting': 'broadcasting',\n  'spectrum management': 'spectrum_management',\n  'spectrum_management': 'spectrum_management',\n  'clf': 'clf',\n  'consumer lending and finance': 'clf'\n};\n\n// Helper functions\nexport const getLicenseTypeStepConfig = (licenseTypeId: string): LicenseTypeStepConfig | null => {\n  console.log('getLicenseTypeStepConfig called with:', licenseTypeId);\n  console.log('Available configs:', Object.keys(LICENSE_TYPE_STEP_CONFIGS));\n  console.log('UUID to code map:', licenseTypeUUIDToCodeMap);\n\n  // Check if licenseTypeId is valid\n  if (!licenseTypeId || typeof licenseTypeId !== 'string') {\n    console.log('Invalid licenseTypeId provided:', licenseTypeId);\n    return null;\n  }\n\n  // First try direct lookup\n  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];\n  if (config) {\n    console.log('Found config via direct lookup:', config.name);\n    return config;\n  }\n\n  // Try normalized lookup\n  const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');\n  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];\n  if (config) {\n    console.log('Found config via normalized lookup:', config.name);\n    return config;\n  }\n\n  // Try name mapping\n  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];\n  if (mappedKey) {\n    console.log('Found config via name mapping:', mappedKey);\n    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];\n  }\n\n  // If licenseTypeId looks like a UUID, try to get the code from license types\n  if (isUUID(licenseTypeId)) {\n    console.log('Detected UUID, trying to get code...');\n    const code = getLicenseTypeCodeFromUUID(licenseTypeId);\n    console.log('Got code from UUID:', code);\n    if (code) {\n      const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];\n      if (foundConfig) {\n        console.log('Found config via UUID mapping:', foundConfig.name);\n        return foundConfig;\n      }\n    }\n  }\n\n  console.log('No config found for license type:', licenseTypeId);\n  return null;\n};\n\n// Helper function to check if a string is a UUID\nconst isUUID = (str: string): boolean => {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(str);\n};\n\n// Helper function to get license type code from UUID\n// This will be populated by the license type service\nlet licenseTypeUUIDToCodeMap: Record<string, string> = {};\n\nexport const setLicenseTypeUUIDToCodeMap = (map: Record<string, string>) => {\n  licenseTypeUUIDToCodeMap = map;\n};\n\nconst getLicenseTypeCodeFromUUID = (uuid: string): string | null => {\n  return licenseTypeUUIDToCodeMap[uuid] || null;\n};\n\nexport const getStepByRoute = (licenseTypeId: string, stepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  return config.steps.find(step => step.route === stepRoute) || null;\n};\n\nexport const getStepByIndex = (licenseTypeId: string, stepIndex: number): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config || stepIndex < 0 || stepIndex >= config.steps.length) return null;\n  \n  return config.steps[stepIndex];\n};\n\nexport const getStepIndex = (licenseTypeId: string, stepRoute: string): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return -1;\n  \n  return config.steps.findIndex(step => step.route === stepRoute);\n};\n\nexport const getTotalSteps = (licenseTypeId: string): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.length : 0;\n};\n\nexport const getRequiredSteps = (licenseTypeId: string): StepConfig[] => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.filter(step => step.required) : [];\n};\n\nexport const getOptionalSteps = (licenseTypeId: string): StepConfig[] => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.filter(step => !step.required) : [];\n};\n\nexport const calculateProgress = (licenseTypeId: string, completedSteps: string[]): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return 0;\n  \n  const totalSteps = config.steps.length;\n  const completed = completedSteps.length;\n  \n  return Math.round((completed / totalSteps) * 100);\n};\n\nexport const getNextStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\n  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;\n  \n  return config.steps[currentIndex + 1];\n};\n\nexport const getPreviousStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\n  if (currentIndex <= 0) return null;\n  \n  return config.steps[currentIndex - 1];\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;AAqBD,mDAAmD;AACnD,MAAM,aAAyC;IAC7C,eAAe;QACb,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,gBAAgB;QACd,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,sBAAsB;QACpB,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,4BAAmE;IAC9E,oBAAoB;QAClB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,iBAAiB;QACf,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,sBAAsB;QACpB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,cAAc;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,qBAAqB;QACnB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,KAAK;QACH,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA,0CAA0C;AAC1C,MAAM,4BAAoD;IACxD,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,gBAAgB;IAChB,uBAAuB;IACvB,uBAAuB;IACvB,OAAO;IACP,gCAAgC;AAClC;AAGO,MAAM,2BAA2B,CAAC;IACvC,QAAQ,GAAG,CAAC,yCAAyC;IACrD,QAAQ,GAAG,CAAC,sBAAsB,OAAO,IAAI,CAAC;IAC9C,QAAQ,GAAG,CAAC,qBAAqB;IAEjC,kCAAkC;IAClC,IAAI,CAAC,iBAAiB,OAAO,kBAAkB,UAAU;QACvD,QAAQ,GAAG,CAAC,mCAAmC;QAC/C,OAAO;IACT;IAEA,0BAA0B;IAC1B,IAAI,SAAS,yBAAyB,CAAC,cAAc;IACrD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,mCAAmC,OAAO,IAAI;QAC1D,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,eAAe,cAAc,WAAW,GAAG,OAAO,CAAC,cAAc;IACvE,SAAS,yBAAyB,CAAC,aAAa;IAChD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,uCAAuC,OAAO,IAAI;QAC9D,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM,YAAY,yBAAyB,CAAC,aAAa;IACzD,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO,yBAAyB,CAAC,UAAU;IAC7C;IAEA,6EAA6E;IAC7E,IAAI,OAAO,gBAAgB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,2BAA2B;QACxC,QAAQ,GAAG,CAAC,uBAAuB;QACnC,IAAI,MAAM;YACR,MAAM,cAAc,yBAAyB,CAAC,KAAK;YACnD,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC,kCAAkC,YAAY,IAAI;gBAC9D,OAAO;YACT;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,qCAAqC;IACjD,OAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,SAAS,CAAC;IACd,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEA,qDAAqD;AACrD,qDAAqD;AACrD,IAAI,2BAAmD,CAAC;AAEjD,MAAM,8BAA8B,CAAC;IAC1C,2BAA2B;AAC7B;AAEA,MAAM,6BAA6B,CAAC;IAClC,OAAO,wBAAwB,CAAC,KAAK,IAAI;AAC3C;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,cAAc;AAChE;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,UAAU,YAAY,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO;IAEzE,OAAO,OAAO,KAAK,CAAC,UAAU;AAChC;AAEO,MAAM,eAAe,CAAC,eAAuB;IAClD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO,CAAC;IAErB,OAAO,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AACvD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,GAAG;AACxC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI,EAAE;AACjE;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,IAAI,EAAE;AAClE;AAEO,MAAM,oBAAoB,CAAC,eAAuB;IACvD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa,OAAO,KAAK,CAAC,MAAM;IACtC,MAAM,YAAY,eAAe,MAAM;IAEvC,OAAO,KAAK,KAAK,CAAC,AAAC,YAAY,aAAc;AAC/C;AAEO,MAAM,cAAc,CAAC,eAAuB;IACjD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,aAAa,eAAe;IACjD,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO;IAE3E,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAEO,MAAM,kBAAkB,CAAC,eAAuB;IACrD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,aAAa,eAAe;IACjD,IAAI,gBAAgB,GAAG,OAAO;IAE9B,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC", "debugId": null}}, {"offset": {"line": 2371, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/applicant-info/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport CustomerLayout from '@/components/customer/CustomerLayout';\nimport { useAuth } from '@/contexts/AuthContext';\nimport TextInput from '@/components/common/TextInput';\nimport Select from '@/components/common/Select';\nimport { LicenseCategory } from '@/services/licenseCategoryService';\nimport { applicationFormDataService } from '@/services/applicationFormDataService';\nimport { applicationService } from '@/services/applicationService';\nimport { applicantService } from '@/services/applicantService';\nimport { applicationProgressService } from '@/services/applicationProgressService';\nimport { validateSection } from '@/utils/formValidation';\nimport { getLicenseTypeStepConfig, StepConfig, LicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';\n\nconst ApplicantInfoPage: React.FC = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { isAuthenticated, loading: authLoading } = useAuth();\n\n  // Get query parameters\n  const licenseCategoryId = searchParams.get('license_category_id');\n  const applicationId = searchParams.get('application_id');\n\n  // State\n  const [licenseCategory, setLicenseCategory] = useState<LicenseCategory | null>(null);\n  const [licenseTypeConfig, setLicenseTypeConfig] = useState<LicenseTypeStepConfig | null>(null);\n  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Form data state\n  const [formData, setFormData] = useState({\n    applicant_type: '',\n    first_name: '',\n    last_name: '',\n    middle_name: '',\n    email: '',\n    phone: '',\n    national_id: '',\n    date_of_birth: '',\n    nationality: 'Malawian',\n    gender: '',\n    postal_address: '',\n    physical_address: '',\n    city: '',\n    district: '',\n    postal_code: ''\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [applicationCreated, setApplicationCreated] = useState(false);\n  const [createdApplicationId, setCreatedApplicationId] = useState<string | null>(null);\n\n  const currentStepIndex = applicationSteps.findIndex(step => step.id === 'applicant-info');\n\n  // Form handling functions\n  const handleFormChange = (field: string, value: string) => {\n    setFormData(prev => ({ ...prev, [field]: value }));\n    setHasUnsavedChanges(true);\n\n    // Clear validation error for this field\n    if (validationErrors[field]) {\n      setValidationErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[field];\n        return newErrors;\n      });\n    }\n  };\n\n  // Save function\n  const handleSave = async () => {\n    setIsSaving(true);\n    setValidationErrors({});\n\n    try {\n      // Validate form data\n      const validation = validateSection('applicantInfo', formData as Record<string, any>);\n      if (!validation.isValid) {\n        setValidationErrors(validation.errors);\n        setIsSaving(false);\n        return;\n      }\n\n      let currentApplicationId = applicationId;\n\n      // If no application exists, create one\n      if (!currentApplicationId) {\n        console.log('Creating new application...');\n\n        // First create applicant\n        const applicantData = {\n          name: `${formData.first_name} ${formData.last_name}`,\n          first_name: formData.first_name,\n          last_name: formData.last_name,\n          middle_name: formData.middle_name,\n          email: formData.email,\n          phone: formData.phone,\n          national_id: formData.national_id,\n          date_of_birth: formData.date_of_birth,\n          nationality: formData.nationality,\n          gender: formData.gender,\n          postal_address: formData.postal_address,\n          physical_address: formData.physical_address,\n          city: formData.city,\n          district: formData.district,\n          postal_code: formData.postal_code,\n          business_registration_number: '',\n          tpin: '',\n          website: '',\n          business_type: formData.applicant_type || 'individual',\n          date_incorporation: '',\n          place_incorporation: ''\n        };\n\n        const applicant = await applicantService.createApplicant(applicantData);\n        console.log('Applicant created:', applicant);\n\n        // Then create application\n        const applicationNumber = `APP-${Date.now()}-${Math.random().toString(36).substring(2, 11).toUpperCase()}`;\n        const applicationData = {\n          application_number: applicationNumber,\n          license_category_id: licenseCategoryId!,\n          applicant_id: applicant.applicant_id,\n          status: 'draft' as any\n        };\n\n        const application = await applicationService.createApplication(applicationData);\n        console.log('Application created:', application);\n\n        currentApplicationId = application.application_id;\n        setCreatedApplicationId(currentApplicationId);\n        setApplicationCreated(true);\n      } else {\n        // Update existing applicant data\n        console.log('Updating existing application...');\n\n        const applicantData = {\n          first_name: formData.first_name,\n          last_name: formData.last_name,\n          middle_name: formData.middle_name,\n          email: formData.email,\n          phone: formData.phone,\n          national_id: formData.national_id,\n          date_of_birth: formData.date_of_birth,\n          nationality: formData.nationality,\n          gender: formData.gender,\n          postal_address: formData.postal_address,\n          physical_address: formData.physical_address,\n          city: formData.city,\n          district: formData.district,\n          postal_code: formData.postal_code\n        };\n\n        // Get application to find applicant_id\n        const application = await applicationService.getApplication(currentApplicationId);\n        if (application.applicant_id) {\n          await applicantService.updateApplicant(application.applicant_id, applicantData);\n        }\n      }\n\n      // Save form data\n      await applicationFormDataService.saveFormSection(currentApplicationId, 'applicantInfo', formData);\n\n      setHasUnsavedChanges(false);\n      console.log('Application saved successfully');\n\n    } catch (error) {\n      console.error('Error saving application:', error);\n      setValidationErrors({ save: 'Failed to save application. Please try again.' });\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Authentication check\n  useEffect(() => {\n    console.log('Auth state:', { isAuthenticated, authLoading });\n    if (!authLoading && !isAuthenticated) {\n      console.log('User not authenticated, redirecting to login');\n      router.push('/customer/auth/login');\n      return;\n    }\n  }, [isAuthenticated, authLoading, router]);\n\n  // Load data\n  useEffect(() => {\n    const loadData = async () => {\n      try {\n        if (!licenseCategoryId) {\n          setError('License category ID is required');\n          setLoading(false);\n          return;\n        }\n\n        console.log('Loading license category:', licenseCategoryId);\n\n        // Load license category\n        const category = await licenseCategoryService.getLicenseCategory(licenseCategoryId);\n        console.log('License category response:', category);\n\n        if (!category) {\n          setError('License category not found');\n          setLoading(false);\n          return;\n        }\n\n        setLicenseCategory(category);\n\n        // Load license type configuration to get the steps\n        console.log('Loading license type config for:', category.license_type_id);\n        console.log('Full category object:', category);\n\n        if (!category.license_type_id) {\n          setError('License category is missing license type information');\n          setLoading(false);\n          return;\n        }\n\n        const config = getLicenseTypeStepConfig(category.license_type_id);\n        console.log('License type config:', config);\n\n        if (!config) {\n          setError(`No configuration found for license type: ${category.license_type_id}`);\n          setLoading(false);\n          return;\n        }\n        setLicenseTypeConfig(config);\n        setApplicationSteps(config.steps);\n\n        console.log('Data loading completed successfully');\n        setLoading(false);\n      } catch (err: any) {\n        console.error('Error loading data:', err);\n        console.error('Error details:', {\n          message: err.message,\n          response: err.response?.data,\n          status: err.response?.status\n        });\n\n        // Provide more specific error message\n        let errorMessage = 'Failed to load application data';\n        if (err.response?.status === 404) {\n          errorMessage = `License category not found (ID: ${licenseCategoryId}). Please go back to the applications page and select a valid license category.`;\n        } else if (err.response?.status === 401) {\n          errorMessage = 'You are not authorized to access this license category. Please log in again.';\n        } else if (err.response?.status === 500) {\n          errorMessage = 'Server error occurred. Please try again later or contact support.';\n        } else if (err.message) {\n          errorMessage = `Error: ${err.message}`;\n        }\n\n        setError(errorMessage);\n        setLoading(false);\n      }\n    };\n\n    if (isAuthenticated && !authLoading) {\n      loadData();\n    }\n  }, [licenseCategoryId, applicationId, isAuthenticated, authLoading]);\n\n  // Navigation handlers for the ApplicantInfo component\n  const handleNext = () => {\n    const nextStep = applicationSteps[currentStepIndex + 1];\n    if (nextStep) {\n      const params = new URLSearchParams();\n      params.set('license_category_id', licenseCategoryId!);\n      if (applicationId) {\n        params.set('application_id', applicationId);\n      }\n      router.push(`/customer/applications/apply/${nextStep.id}?${params.toString()}`);\n    }\n  };\n\n  const handlePrevious = () => {\n    router.push('/customer/applications');\n  };\n\n  const handleStepComplete = (stepId: string, data?: any) => {\n    console.log('Step completed:', stepId, data);\n    // If data contains applicationId, update the URL\n    if (data?.applicationId) {\n      const params = new URLSearchParams(window.location.search);\n      params.set('application_id', data.applicationId);\n      const newUrl = `${window.location.pathname}?${params.toString()}`;\n      window.history.replaceState({}, '', newUrl);\n    }\n  };\n\n  const handleStepError = (stepId: string, errors: any) => {\n    console.error('Step error:', stepId, errors);\n    setError(typeof errors === 'string' ? errors : 'An error occurred while processing the step');\n  };\n\n  const handleStepClick = (stepIndex: number) => {\n    // Prevent navigation to future steps if not editing an existing application\n    if (!applicationId && stepIndex > currentStepIndex) {\n      setError('Please complete the current step before proceeding to the next step');\n      return;\n    }\n\n    const step = applicationSteps[stepIndex];\n    const params = new URLSearchParams();\n    params.set('license_category_id', licenseCategoryId!);\n    if (applicationId) {\n      params.set('application_id', applicationId);\n    }\n    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);\n  };\n\n  const handleBackToApplications = () => {\n    router.push('/customer/applications');\n  };\n  // Loading state\n  if (authLoading || loading) {\n    return (\n      <CustomerLayout>\n        <div className=\"flex items-center justify-center min-h-96\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading application form...</p>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <CustomerLayout>\n        <div className=\"flex items-center justify-center min-h-96\">\n          <div className=\"text-center\">\n            <div className=\"mb-4\">\n              <i className=\"ri-error-warning-line text-4xl text-red-500\"></i>\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\n              Error Loading Application\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n              {error}\n            </p>\n            <button\n              onClick={() => router.push('/customer/applications')}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              <i className=\"ri-arrow-left-line mr-2\"></i>\n              Back to Applications\n            </button>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  const currentStep = applicationSteps[currentStepIndex];\n\n  return (\n    <CustomerLayout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <button\n            onClick={handleBackToApplications}\n            className=\"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mb-4\"\n          >\n            <i className=\"ri-arrow-left-line mr-1\"></i>\n            Back to Applications\n          </button>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">\n            {licenseCategory?.name} Application\n          </h1>\n          <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n            {applicationId ? 'Edit your application' : 'Complete your license application'}\n          </p>\n        </div>\n\n        {/* Progress Steps - Vertical Layout for Scalability */}\n        <div className=\"mb-8\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4\">\n            <h3 className=\"text-sm font-medium text-gray-900 dark:text-gray-100 mb-4\">\n              Application Progress ({currentStepIndex + 1} of {applicationSteps.length})\n            </h3>\n            <div className=\"space-y-2\">\n              {applicationSteps.map((step, index) => {\n                const isAccessible = applicationId || index <= currentStepIndex;\n                return (\n                  <div\n                    key={step.id}\n                    className={`flex items-center p-2 rounded-md transition-colors ${\n                      isAccessible ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'\n                    } ${\n                      index === currentStepIndex\n                        ? 'bg-primary/10 border border-primary/20'\n                        : index < currentStepIndex\n                        ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'\n                        : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'\n                    }`}\n                    onClick={() => isAccessible && handleStepClick(index)}\n                  >\n                  <div\n                    className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${\n                      index === currentStepIndex\n                        ? 'bg-primary text-white'\n                        : index < currentStepIndex\n                        ? 'bg-green-500 text-white'\n                        : 'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300'\n                    }`}\n                  >\n                    {index < currentStepIndex ? (\n                      <i className=\"ri-check-line\"></i>\n                    ) : (\n                      index + 1\n                    )}\n                  </div>\n                  <div className=\"flex-1\">\n                    <div className={`text-sm font-medium ${\n                      index === currentStepIndex\n                        ? 'text-primary'\n                        : index < currentStepIndex\n                        ? 'text-green-700 dark:text-green-300'\n                        : 'text-gray-600 dark:text-gray-400'\n                    }`}>\n                      {step.name}\n                    </div>\n                    {step.description && (\n                      <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n                        {step.description}\n                      </div>\n                    )}\n                  </div>\n                  {step.required && (\n                    <span className=\"text-xs text-red-500 ml-2\">*</span>\n                  )}\n                </div>\n                );\n              })}\n            </div>\n          </div>\n        </div>\n\n        {/* Current Step Content */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h2 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n              {currentStep.name}\n            </h2>\n          </div>\n\n          <div className=\"p-6\">\n            {/* Header */}\n            <div className=\"mb-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\n                Applicant Information\n              </h3>\n              <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n                Please provide your personal information. This will create your application record.\n              </p>\n              {!applicationId && (\n                <div className=\"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n                  <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n                    <i className=\"ri-information-line mr-1\"></i>\n                    Your application will be created when you save this step.\n                  </p>\n                </div>\n              )}\n              {applicationCreated && (\n                <div className=\"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n                  <p className=\"text-sm text-green-700 dark:text-green-300\">\n                    <i className=\"ri-check-line mr-1\"></i>\n                    Application created: {createdApplicationId?.slice(0, 8)}...\n                  </p>\n                </div>\n              )}\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n              {/* Applicant Type */}\n              <div className=\"md:col-span-2\">\n                <Select\n                  label=\"Applicant Type\"\n                  value={formData.applicant_type || ''}\n                  onChange={(value) => handleFormChange('applicant_type', value)}\n                  options={[\n                    { value: 'individual', label: 'Individual' },\n                    { value: 'company', label: 'Company' },\n                    { value: 'organization', label: 'Organization' }\n                  ]}\n                  required\n                  error={validationErrors.applicant_type}\n                />\n              </div>\n\n              {/* Personal Information */}\n              <TextInput\n                label=\"First Name\"\n                value={formData.first_name || ''}\n                onChange={(e) => handleFormChange('first_name', e.target.value)}\n                required\n                error={validationErrors.first_name}\n              />\n\n              <TextInput\n                label=\"Last Name\"\n                value={formData.last_name || ''}\n                onChange={(e) => handleFormChange('last_name', e.target.value)}\n                required\n                error={validationErrors.last_name}\n              />\n\n              <TextInput\n                label=\"Middle Name\"\n                value={formData.middle_name || ''}\n                onChange={(e) => handleFormChange('middle_name', e.target.value)}\n                error={validationErrors.middle_name}\n              />\n\n              <Select\n                label=\"Gender\"\n                value={formData.gender || ''}\n                onChange={(value) => handleFormChange('gender', value)}\n                options={[\n                  { value: 'male', label: 'Male' },\n                  { value: 'female', label: 'Female' },\n                  { value: 'other', label: 'Other' }\n                ]}\n                required\n                error={validationErrors.gender}\n              />\n\n              {/* Contact Information */}\n              <TextInput\n                label=\"Email Address\"\n                type=\"email\"\n                value={formData.email || ''}\n                onChange={(e) => handleFormChange('email', e.target.value)}\n                required\n                error={validationErrors.email}\n              />\n\n              <TextInput\n                label=\"Phone Number\"\n                value={formData.phone || ''}\n                onChange={(e) => handleFormChange('phone', e.target.value)}\n                placeholder=\"+265 1 234 567\"\n                required\n                error={validationErrors.phone}\n              />\n\n              {/* Identification */}\n              <TextInput\n                label=\"National ID\"\n                value={formData.national_id || ''}\n                onChange={(e) => handleFormChange('national_id', e.target.value)}\n                placeholder=\"**********\"\n                required\n                error={validationErrors.national_id}\n              />\n\n              <TextInput\n                label=\"Date of Birth\"\n                type=\"date\"\n                value={formData.date_of_birth || ''}\n                onChange={(e) => handleFormChange('date_of_birth', e.target.value)}\n                required\n                error={validationErrors.date_of_birth}\n              />\n\n              <Select\n                label=\"Nationality\"\n                value={formData.nationality || 'Malawian'}\n                onChange={(value) => handleFormChange('nationality', value)}\n                options={[\n                  { value: 'Malawian', label: 'Malawian' },\n                  { value: 'Other', label: 'Other' }\n                ]}\n                required\n                error={validationErrors.nationality}\n              />\n            </div>\n\n            {/* Address Information */}\n            <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n              <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\n                Address Information\n              </h4>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div className=\"md:col-span-2\">\n                  <TextInput\n                    label=\"Postal Address\"\n                    value={formData.postal_address || ''}\n                    onChange={(e) => handleFormChange('postal_address', e.target.value)}\n                    placeholder=\"P.O. Box 123\"\n                    required\n                    error={validationErrors.postal_address}\n                  />\n                </div>\n\n                <div className=\"md:col-span-2\">\n                  <TextInput\n                    label=\"Physical Address\"\n                    value={formData.physical_address || ''}\n                    onChange={(e) => handleFormChange('physical_address', e.target.value)}\n                    placeholder=\"Street address\"\n                    required\n                    error={validationErrors.physical_address}\n                  />\n                </div>\n\n                <TextInput\n                  label=\"City\"\n                  value={formData.city || ''}\n                  onChange={(e) => handleFormChange('city', e.target.value)}\n                  required\n                  error={validationErrors.city}\n                />\n\n                <Select\n                  label=\"District\"\n                  value={formData.district || ''}\n                  onChange={(value) => handleFormChange('district', value)}\n                  options={[\n                    { value: 'Blantyre', label: 'Blantyre' },\n                    { value: 'Lilongwe', label: 'Lilongwe' },\n                    { value: 'Mzuzu', label: 'Mzuzu' },\n                    { value: 'Zomba', label: 'Zomba' },\n                    { value: 'Other', label: 'Other' }\n                  ]}\n                  required\n                  error={validationErrors.district}\n                />\n\n                <TextInput\n                  label=\"Postal Code\"\n                  value={formData.postal_code || ''}\n                  onChange={(e) => handleFormChange('postal_code', e.target.value)}\n                  error={validationErrors.postal_code}\n                />\n              </div>\n            </div>\n\n            {/* Error Display */}\n            {validationErrors.save && (\n              <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n                <div className=\"flex items-center\">\n                  <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3\"></i>\n                  <div>\n                    <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                      Error Saving Application\n                    </h3>\n                    <p className=\"text-red-700 dark:text-red-300 text-sm mt-1\">\n                      {validationErrors.save}\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Success Message */}\n            {applicationCreated && !applicationId && (\n              <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n                <div className=\"flex items-center\">\n                  <i className=\"ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3\"></i>\n                  <div>\n                    <h3 className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                      Application Created Successfully!\n                    </h3>\n                    <p className=\"text-green-700 dark:text-green-300 text-sm mt-1\">\n                      Your application has been created. You can now continue to the next step.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {/* Action Buttons */}\n            <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\n              {applicationCreated && !applicationId ? (\n                <>\n                  {/* Save Changes Button (for edit mode) */}\n                  <button\n                    onClick={handleSave}\n                    disabled={isSaving}\n                    className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n                  >\n                    {isSaving ? (\n                      <>\n                        <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n                        Saving...\n                      </>\n                    ) : (\n                      <>\n                        <i className=\"ri-save-line mr-2\"></i>\n                        Save Changes\n                      </>\n                    )}\n                  </button>\n\n                  {/* Continue to Next Step Button */}\n                  <button\n                    onClick={() => {\n                      const nextStep = applicationSteps[currentStepIndex + 1];\n                      if (nextStep) {\n                        const params = new URLSearchParams();\n                        params.set('license_category_id', licenseCategoryId!);\n                        if (createdApplicationId) {\n                          params.set('application_id', createdApplicationId);\n                        }\n                        router.push(`/customer/applications/apply/${nextStep.id}?${params.toString()}`);\n                      }\n                    }}\n                    className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n                  >\n                    <i className=\"ri-arrow-right-line mr-2\"></i>\n                    Continue to Company Profile\n                  </button>\n                </>\n              ) : (\n                /* Create/Save Application Button */\n                <button\n                  onClick={handleSave}\n                  disabled={isSaving}\n                  className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {isSaving ? (\n                    <>\n                      <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n                      {applicationId ? 'Saving...' : 'Creating Application...'}\n                    </>\n                  ) : (\n                    <>\n                      <i className=\"ri-save-line mr-2\"></i>\n                      {applicationId ? 'Save Changes' : 'Create Application'}\n                    </>\n                  )}\n                </button>\n              )}\n            </div>\n          </div>\n\n\n        </div>\n      </div>\n    </CustomerLayout>\n  );\n};\n\nexport default ApplicantInfoPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;;;AAdA;;;;;;;;;;;;AAgBA,MAAM,oBAA8B;;IAClC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAExD,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IACR,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgC;IACzF,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IACzE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,kBAAkB;IAClB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,gBAAgB;QAChB,YAAY;QACZ,WAAW;QACX,aAAa;QACb,OAAO;QACP,OAAO;QACP,aAAa;QACb,eAAe;QACf,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,kBAAkB;QAClB,MAAM;QACN,UAAU;QACV,aAAa;IACf;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhF,MAAM,mBAAmB,iBAAiB,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAExE,0BAA0B;IAC1B,MAAM,mBAAmB,CAAC,OAAe;QACvC,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAChD,qBAAqB;QAErB,wCAAwC;QACxC,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA;gBAClB,MAAM,YAAY;oBAAE,GAAG,IAAI;gBAAC;gBAC5B,OAAO,SAAS,CAAC,MAAM;gBACvB,OAAO;YACT;QACF;IACF;IAEA,gBAAgB;IAChB,MAAM,aAAa;QACjB,YAAY;QACZ,oBAAoB,CAAC;QAErB,IAAI;YACF,qBAAqB;YACrB,MAAM,aAAa,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD,EAAE,iBAAiB;YACpD,IAAI,CAAC,WAAW,OAAO,EAAE;gBACvB,oBAAoB,WAAW,MAAM;gBACrC,YAAY;gBACZ;YACF;YAEA,IAAI,uBAAuB;YAE3B,uCAAuC;YACvC,IAAI,CAAC,sBAAsB;gBACzB,QAAQ,GAAG,CAAC;gBAEZ,yBAAyB;gBACzB,MAAM,gBAAgB;oBACpB,MAAM,GAAG,SAAS,UAAU,CAAC,CAAC,EAAE,SAAS,SAAS,EAAE;oBACpD,YAAY,SAAS,UAAU;oBAC/B,WAAW,SAAS,SAAS;oBAC7B,aAAa,SAAS,WAAW;oBACjC,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,eAAe,SAAS,aAAa;oBACrC,aAAa,SAAS,WAAW;oBACjC,QAAQ,SAAS,MAAM;oBACvB,gBAAgB,SAAS,cAAc;oBACvC,kBAAkB,SAAS,gBAAgB;oBAC3C,MAAM,SAAS,IAAI;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,aAAa,SAAS,WAAW;oBACjC,8BAA8B;oBAC9B,MAAM;oBACN,SAAS;oBACT,eAAe,SAAS,cAAc,IAAI;oBAC1C,oBAAoB;oBACpB,qBAAqB;gBACvB;gBAEA,MAAM,YAAY,MAAM,sIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;gBACzD,QAAQ,GAAG,CAAC,sBAAsB;gBAElC,0BAA0B;gBAC1B,MAAM,oBAAoB,CAAC,IAAI,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,IAAI,WAAW,IAAI;gBAC1G,MAAM,kBAAkB;oBACtB,oBAAoB;oBACpB,qBAAqB;oBACrB,cAAc,UAAU,YAAY;oBACpC,QAAQ;gBACV;gBAEA,MAAM,cAAc,MAAM,wIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC;gBAC/D,QAAQ,GAAG,CAAC,wBAAwB;gBAEpC,uBAAuB,YAAY,cAAc;gBACjD,wBAAwB;gBACxB,sBAAsB;YACxB,OAAO;gBACL,iCAAiC;gBACjC,QAAQ,GAAG,CAAC;gBAEZ,MAAM,gBAAgB;oBACpB,YAAY,SAAS,UAAU;oBAC/B,WAAW,SAAS,SAAS;oBAC7B,aAAa,SAAS,WAAW;oBACjC,OAAO,SAAS,KAAK;oBACrB,OAAO,SAAS,KAAK;oBACrB,aAAa,SAAS,WAAW;oBACjC,eAAe,SAAS,aAAa;oBACrC,aAAa,SAAS,WAAW;oBACjC,QAAQ,SAAS,MAAM;oBACvB,gBAAgB,SAAS,cAAc;oBACvC,kBAAkB,SAAS,gBAAgB;oBAC3C,MAAM,SAAS,IAAI;oBACnB,UAAU,SAAS,QAAQ;oBAC3B,aAAa,SAAS,WAAW;gBACnC;gBAEA,uCAAuC;gBACvC,MAAM,cAAc,MAAM,wIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC;gBAC5D,IAAI,YAAY,YAAY,EAAE;oBAC5B,MAAM,sIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC,YAAY,YAAY,EAAE;gBACnE;YACF;YAEA,iBAAiB;YACjB,MAAM,gJAAA,CAAA,6BAA0B,CAAC,eAAe,CAAC,sBAAsB,iBAAiB;YAExF,qBAAqB;YACrB,QAAQ,GAAG,CAAC;QAEd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,oBAAoB;gBAAE,MAAM;YAAgD;QAC9E,SAAU;YACR,YAAY;QACd;IACF;IAEA,uBAAuB;IACvB,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,QAAQ,GAAG,CAAC,eAAe;gBAAE;gBAAiB;YAAY;YAC1D,IAAI,CAAC,eAAe,CAAC,iBAAiB;gBACpC,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;QACF;sCAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,YAAY;IACZ,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;wDAAW;oBACf,IAAI;wBACF,IAAI,CAAC,mBAAmB;4BACtB,SAAS;4BACT,WAAW;4BACX;wBACF;wBAEA,QAAQ,GAAG,CAAC,6BAA6B;wBAEzC,wBAAwB;wBACxB,MAAM,WAAW,MAAM,uBAAuB,kBAAkB,CAAC;wBACjE,QAAQ,GAAG,CAAC,8BAA8B;wBAE1C,IAAI,CAAC,UAAU;4BACb,SAAS;4BACT,WAAW;4BACX;wBACF;wBAEA,mBAAmB;wBAEnB,mDAAmD;wBACnD,QAAQ,GAAG,CAAC,oCAAoC,SAAS,eAAe;wBACxE,QAAQ,GAAG,CAAC,yBAAyB;wBAErC,IAAI,CAAC,SAAS,eAAe,EAAE;4BAC7B,SAAS;4BACT,WAAW;4BACX;wBACF;wBAEA,MAAM,SAAS,CAAA,GAAA,yIAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS,eAAe;wBAChE,QAAQ,GAAG,CAAC,wBAAwB;wBAEpC,IAAI,CAAC,QAAQ;4BACX,SAAS,CAAC,yCAAyC,EAAE,SAAS,eAAe,EAAE;4BAC/E,WAAW;4BACX;wBACF;wBACA,qBAAqB;wBACrB,oBAAoB,OAAO,KAAK;wBAEhC,QAAQ,GAAG,CAAC;wBACZ,WAAW;oBACb,EAAE,OAAO,KAAU;wBACjB,QAAQ,KAAK,CAAC,uBAAuB;wBACrC,QAAQ,KAAK,CAAC,kBAAkB;4BAC9B,SAAS,IAAI,OAAO;4BACpB,UAAU,IAAI,QAAQ,EAAE;4BACxB,QAAQ,IAAI,QAAQ,EAAE;wBACxB;wBAEA,sCAAsC;wBACtC,IAAI,eAAe;wBACnB,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;4BAChC,eAAe,CAAC,gCAAgC,EAAE,kBAAkB,+EAA+E,CAAC;wBACtJ,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;4BACvC,eAAe;wBACjB,OAAO,IAAI,IAAI,QAAQ,EAAE,WAAW,KAAK;4BACvC,eAAe;wBACjB,OAAO,IAAI,IAAI,OAAO,EAAE;4BACtB,eAAe,CAAC,OAAO,EAAE,IAAI,OAAO,EAAE;wBACxC;wBAEA,SAAS;wBACT,WAAW;oBACb;gBACF;;YAEA,IAAI,mBAAmB,CAAC,aAAa;gBACnC;YACF;QACF;sCAAG;QAAC;QAAmB;QAAe;QAAiB;KAAY;IAEnE,sDAAsD;IACtD,MAAM,aAAa;QACjB,MAAM,WAAW,gBAAgB,CAAC,mBAAmB,EAAE;QACvD,IAAI,UAAU;YACZ,MAAM,SAAS,IAAI;YACnB,OAAO,GAAG,CAAC,uBAAuB;YAClC,IAAI,eAAe;gBACjB,OAAO,GAAG,CAAC,kBAAkB;YAC/B;YACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;QAChF;IACF;IAEA,MAAM,iBAAiB;QACrB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB,CAAC,QAAgB;QAC1C,QAAQ,GAAG,CAAC,mBAAmB,QAAQ;QACvC,iDAAiD;QACjD,IAAI,MAAM,eAAe;YACvB,MAAM,SAAS,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;YACzD,OAAO,GAAG,CAAC,kBAAkB,KAAK,aAAa;YAC/C,MAAM,SAAS,GAAG,OAAO,QAAQ,CAAC,QAAQ,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;YACjE,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;QACtC;IACF;IAEA,MAAM,kBAAkB,CAAC,QAAgB;QACvC,QAAQ,KAAK,CAAC,eAAe,QAAQ;QACrC,SAAS,OAAO,WAAW,WAAW,SAAS;IACjD;IAEA,MAAM,kBAAkB,CAAC;QACvB,4EAA4E;QAC5E,IAAI,CAAC,iBAAiB,YAAY,kBAAkB;YAClD,SAAS;YACT;QACF;QAEA,MAAM,OAAO,gBAAgB,CAAC,UAAU;QACxC,MAAM,SAAS,IAAI;QACnB,OAAO,GAAG,CAAC,uBAAuB;QAClC,IAAI,eAAe;YACjB,OAAO,GAAG,CAAC,kBAAkB;QAC/B;QACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,KAAK,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;IAC5E;IAEA,MAAM,2BAA2B;QAC/B,OAAO,IAAI,CAAC;IACd;IACA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,6LAAC,mJAAA,CAAA,UAAc;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,6LAAC;4BAAE,WAAU;sCACV;;;;;;sCAEH,6LAAC;4BACC,SAAS,IAAM,OAAO,IAAI,CAAC;4BAC3B,WAAU;;8CAEV,6LAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;;;;;;;;;;;;;;;;;;IAOvD;IAEA,MAAM,cAAc,gBAAgB,CAAC,iBAAiB;IAEtD,qBACE,6LAAC,mJAAA,CAAA,UAAc;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,6LAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;;sCAG7C,6LAAC;4BAAG,WAAU;;gCACX,iBAAiB;gCAAK;;;;;;;sCAEzB,6LAAC;4BAAE,WAAU;sCACV,gBAAgB,0BAA0B;;;;;;;;;;;;8BAK/C,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;;oCAA4D;oCACjD,mBAAmB;oCAAE;oCAAK,iBAAiB,MAAM;oCAAC;;;;;;;0CAE3E,6LAAC;gCAAI,WAAU;0CACZ,iBAAiB,GAAG,CAAC,CAAC,MAAM;oCAC3B,MAAM,eAAe,iBAAiB,SAAS;oCAC/C,qBACE,6LAAC;wCAEC,WAAW,CAAC,mDAAmD,EAC7D,eAAe,mBAAmB,gCACnC,CAAC,EACA,UAAU,mBACN,2CACA,QAAQ,mBACR,mFACA,8EACJ;wCACF,SAAS,IAAM,gBAAgB,gBAAgB;;0DAEjD,6LAAC;gDACC,WAAW,CAAC,+EAA+E,EACzF,UAAU,mBACN,0BACA,QAAQ,mBACR,4BACA,iEACJ;0DAED,QAAQ,iCACP,6LAAC;oDAAE,WAAU;;;;;2DAEb,QAAQ;;;;;;0DAGZ,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,CAAC,oBAAoB,EACnC,UAAU,mBACN,iBACA,QAAQ,mBACR,uCACA,oCACJ;kEACC,KAAK,IAAI;;;;;;oDAEX,KAAK,WAAW,kBACf,6LAAC;wDAAI,WAAU;kEACZ,KAAK,WAAW;;;;;;;;;;;;4CAItB,KAAK,QAAQ,kBACZ,6LAAC;gDAAK,WAAU;0DAA4B;;;;;;;uCA5CvC,KAAK,EAAE;;;;;gCAgDlB;;;;;;;;;;;;;;;;;8BAMN,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAG,WAAU;0CACX,YAAY,IAAI;;;;;;;;;;;sCAIrB,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAG1E,6LAAC;4CAAE,WAAU;sDAAgD;;;;;;wCAG5D,CAAC,+BACA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDAAE,WAAU;;;;;;oDAA+B;;;;;;;;;;;;wCAKjD,oCACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;wDAAE,WAAU;;;;;;oDAAyB;oDAChB,sBAAsB,MAAM,GAAG;oDAAG;;;;;;;;;;;;;;;;;;8CAMhE,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,yIAAA,CAAA,UAAM;gDACL,OAAM;gDACN,OAAO,SAAS,cAAc,IAAI;gDAClC,UAAU,CAAC,QAAU,iBAAiB,kBAAkB;gDACxD,SAAS;oDACP;wDAAE,OAAO;wDAAc,OAAO;oDAAa;oDAC3C;wDAAE,OAAO;wDAAW,OAAO;oDAAU;oDACrC;wDAAE,OAAO;wDAAgB,OAAO;oDAAe;iDAChD;gDACD,QAAQ;gDACR,OAAO,iBAAiB,cAAc;;;;;;;;;;;sDAK1C,6LAAC,4IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,UAAU,IAAI;4CAC9B,UAAU,CAAC,IAAM,iBAAiB,cAAc,EAAE,MAAM,CAAC,KAAK;4CAC9D,QAAQ;4CACR,OAAO,iBAAiB,UAAU;;;;;;sDAGpC,6LAAC,4IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,SAAS,IAAI;4CAC7B,UAAU,CAAC,IAAM,iBAAiB,aAAa,EAAE,MAAM,CAAC,KAAK;4CAC7D,QAAQ;4CACR,OAAO,iBAAiB,SAAS;;;;;;sDAGnC,6LAAC,4IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,WAAW,IAAI;4CAC/B,UAAU,CAAC,IAAM,iBAAiB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC/D,OAAO,iBAAiB,WAAW;;;;;;sDAGrC,6LAAC,yIAAA,CAAA,UAAM;4CACL,OAAM;4CACN,OAAO,SAAS,MAAM,IAAI;4CAC1B,UAAU,CAAC,QAAU,iBAAiB,UAAU;4CAChD,SAAS;gDACP;oDAAE,OAAO;oDAAQ,OAAO;gDAAO;gDAC/B;oDAAE,OAAO;oDAAU,OAAO;gDAAS;gDACnC;oDAAE,OAAO;oDAAS,OAAO;gDAAQ;6CAClC;4CACD,QAAQ;4CACR,OAAO,iBAAiB,MAAM;;;;;;sDAIhC,6LAAC,4IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,KAAK,IAAI;4CACzB,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;4CACzD,QAAQ;4CACR,OAAO,iBAAiB,KAAK;;;;;;sDAG/B,6LAAC,4IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,KAAK,IAAI;4CACzB,UAAU,CAAC,IAAM,iBAAiB,SAAS,EAAE,MAAM,CAAC,KAAK;4CACzD,aAAY;4CACZ,QAAQ;4CACR,OAAO,iBAAiB,KAAK;;;;;;sDAI/B,6LAAC,4IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,SAAS,WAAW,IAAI;4CAC/B,UAAU,CAAC,IAAM,iBAAiB,eAAe,EAAE,MAAM,CAAC,KAAK;4CAC/D,aAAY;4CACZ,QAAQ;4CACR,OAAO,iBAAiB,WAAW;;;;;;sDAGrC,6LAAC,4IAAA,CAAA,UAAS;4CACR,OAAM;4CACN,MAAK;4CACL,OAAO,SAAS,aAAa,IAAI;4CACjC,UAAU,CAAC,IAAM,iBAAiB,iBAAiB,EAAE,MAAM,CAAC,KAAK;4CACjE,QAAQ;4CACR,OAAO,iBAAiB,aAAa;;;;;;sDAGvC,6LAAC,yIAAA,CAAA,UAAM;4CACL,OAAM;4CACN,OAAO,SAAS,WAAW,IAAI;4CAC/B,UAAU,CAAC,QAAU,iBAAiB,eAAe;4CACrD,SAAS;gDACP;oDAAE,OAAO;oDAAY,OAAO;gDAAW;gDACvC;oDAAE,OAAO;oDAAS,OAAO;gDAAQ;6CAClC;4CACD,QAAQ;4CACR,OAAO,iBAAiB,WAAW;;;;;;;;;;;;8CAKvC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA4D;;;;;;sDAI1E,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,4IAAA,CAAA,UAAS;wDACR,OAAM;wDACN,OAAO,SAAS,cAAc,IAAI;wDAClC,UAAU,CAAC,IAAM,iBAAiB,kBAAkB,EAAE,MAAM,CAAC,KAAK;wDAClE,aAAY;wDACZ,QAAQ;wDACR,OAAO,iBAAiB,cAAc;;;;;;;;;;;8DAI1C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,4IAAA,CAAA,UAAS;wDACR,OAAM;wDACN,OAAO,SAAS,gBAAgB,IAAI;wDACpC,UAAU,CAAC,IAAM,iBAAiB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wDACpE,aAAY;wDACZ,QAAQ;wDACR,OAAO,iBAAiB,gBAAgB;;;;;;;;;;;8DAI5C,6LAAC,4IAAA,CAAA,UAAS;oDACR,OAAM;oDACN,OAAO,SAAS,IAAI,IAAI;oDACxB,UAAU,CAAC,IAAM,iBAAiB,QAAQ,EAAE,MAAM,CAAC,KAAK;oDACxD,QAAQ;oDACR,OAAO,iBAAiB,IAAI;;;;;;8DAG9B,6LAAC,yIAAA,CAAA,UAAM;oDACL,OAAM;oDACN,OAAO,SAAS,QAAQ,IAAI;oDAC5B,UAAU,CAAC,QAAU,iBAAiB,YAAY;oDAClD,SAAS;wDACP;4DAAE,OAAO;4DAAY,OAAO;wDAAW;wDACvC;4DAAE,OAAO;4DAAY,OAAO;wDAAW;wDACvC;4DAAE,OAAO;4DAAS,OAAO;wDAAQ;wDACjC;4DAAE,OAAO;4DAAS,OAAO;wDAAQ;wDACjC;4DAAE,OAAO;4DAAS,OAAO;wDAAQ;qDAClC;oDACD,QAAQ;oDACR,OAAO,iBAAiB,QAAQ;;;;;;8DAGlC,6LAAC,4IAAA,CAAA,UAAS;oDACR,OAAM;oDACN,OAAO,SAAS,WAAW,IAAI;oDAC/B,UAAU,CAAC,IAAM,iBAAiB,eAAe,EAAE,MAAM,CAAC,KAAK;oDAC/D,OAAO,iBAAiB,WAAW;;;;;;;;;;;;;;;;;;gCAMxC,iBAAiB,IAAI,kBACpB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAqD;;;;;;kEAGnE,6LAAC;wDAAE,WAAU;kEACV,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;gCAQ/B,sBAAsB,CAAC,+BACtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAE,WAAU;;;;;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAyD;;;;;;kEAGvE,6LAAC;wDAAE,WAAU;kEAAkD;;;;;;;;;;;;;;;;;;;;;;;8CASvE,6LAAC;oCAAI,WAAU;8CACZ,sBAAsB,CAAC,8BACtB;;0DAEE,6LAAC;gDACC,SAAS;gDACT,UAAU;gDACV,WAAU;0DAET,yBACC;;sEACE,6LAAC;4DAAE,WAAU;;;;;;wDAAyC;;iFAIxD;;sEACE,6LAAC;4DAAE,WAAU;;;;;;wDAAwB;;;;;;;;0DAO3C,6LAAC;gDACC,SAAS;oDACP,MAAM,WAAW,gBAAgB,CAAC,mBAAmB,EAAE;oDACvD,IAAI,UAAU;wDACZ,MAAM,SAAS,IAAI;wDACnB,OAAO,GAAG,CAAC,uBAAuB;wDAClC,IAAI,sBAAsB;4DACxB,OAAO,GAAG,CAAC,kBAAkB;wDAC/B;wDACA,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,SAAS,EAAE,CAAC,CAAC,EAAE,OAAO,QAAQ,IAAI;oDAChF;gDACF;gDACA,WAAU;;kEAEV,6LAAC;wDAAE,WAAU;;;;;;oDAA+B;;;;;;;;uDAKhD,kCAAkC,iBAClC,6LAAC;wCACC,SAAS;wCACT,UAAU;wCACV,WAAU;kDAET,yBACC;;8DACE,6LAAC;oDAAE,WAAU;;;;;;gDACZ,gBAAgB,cAAc;;yEAGjC;;8DACE,6LAAC;oDAAE,WAAU;;;;;;gDACZ,gBAAgB,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaxD;GA/tBM;;QACW,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;QACc,kIAAA,CAAA,UAAO;;;KAHrD;uCAiuBS", "debugId": null}}]}