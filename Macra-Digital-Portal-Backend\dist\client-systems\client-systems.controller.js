"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientSystemsController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const client_systems_service_1 = require("./client-systems.service");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const create_client_system_dto_1 = require("../dto/client-system/create-client-system.dto");
const update_client_system_dto_1 = require("../dto/client-system/update-client-system.dto");
const client_systems_entity_1 = require("../entities/client-systems.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
const pagination_interface_1 = require("../common/interfaces/pagination.interface");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
let ClientSystemsController = class ClientSystemsController {
    clientSystemsService;
    constructor(clientSystemsService) {
        this.clientSystemsService = clientSystemsService;
    }
    async create(createClientSystemDto, req) {
        return this.clientSystemsService.create(createClientSystemDto, req.user.userId);
    }
    async findAll(query) {
        const result = await this.clientSystemsService.findAll(query);
        return pagination_interface_1.PaginationTransformer.transform(result);
    }
    async getStats() {
        return this.clientSystemsService.getSystemStats();
    }
    async findOne(id) {
        return this.clientSystemsService.findOne(id);
    }
    async findBySystemCode(systemCode) {
        return this.clientSystemsService.findBySystemCode(systemCode);
    }
    async update(id, updateClientSystemDto, req) {
        return this.clientSystemsService.update(id, updateClientSystemDto, req.user.userId);
    }
    async updateLastAccessed(id) {
        await this.clientSystemsService.updateLastAccessed(id);
        return { message: 'Last accessed timestamp updated successfully' };
    }
    async remove(id) {
        await this.clientSystemsService.remove(id);
        return { message: 'Client system deleted successfully' };
    }
};
exports.ClientSystemsController = ClientSystemsController;
__decorate([
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create a new client system' }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Client system created successfully',
        type: client_systems_entity_1.ClientSystems,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.SYSTEM_MANAGEMENT,
        resourceType: 'ClientSystem',
        description: 'Created new client system',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_client_system_dto_1.CreateClientSystemDto, Object]),
    __metadata("design:returntype", Promise)
], ClientSystemsController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    (0, swagger_1.ApiOperation)({ summary: 'Get all client systems with pagination' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client systems retrieved successfully',
    }),
    __param(0, (0, nestjs_paginate_1.Paginate)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", Promise)
], ClientSystemsController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('stats'),
    (0, swagger_1.ApiOperation)({ summary: 'Get client systems statistics' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client systems statistics retrieved successfully',
    }),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], ClientSystemsController.prototype, "getStats", null);
__decorate([
    (0, common_1.Get)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a client system by ID' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Client system UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client system retrieved successfully',
        type: client_systems_entity_1.ClientSystems,
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClientSystemsController.prototype, "findOne", null);
__decorate([
    (0, common_1.Get)('code/:systemCode'),
    (0, swagger_1.ApiOperation)({ summary: 'Get a client system by system code' }),
    (0, swagger_1.ApiParam)({ name: 'systemCode', description: 'Client system code' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client system retrieved successfully',
        type: client_systems_entity_1.ClientSystems,
    }),
    __param(0, (0, common_1.Param)('systemCode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClientSystemsController.prototype, "findBySystemCode", null);
__decorate([
    (0, common_1.Put)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Update a client system' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Client system UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client system updated successfully',
        type: client_systems_entity_1.ClientSystems,
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.SYSTEM_MANAGEMENT,
        resourceType: 'ClientSystem',
        description: 'Updated client system',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Body)()),
    __param(2, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_client_system_dto_1.UpdateClientSystemDto, Object]),
    __metadata("design:returntype", Promise)
], ClientSystemsController.prototype, "update", null);
__decorate([
    (0, common_1.Patch)(':id/access'),
    (0, swagger_1.ApiOperation)({ summary: 'Update last accessed timestamp for a client system' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Client system UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Last accessed timestamp updated successfully',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClientSystemsController.prototype, "updateLastAccessed", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete a client system' }),
    (0, swagger_1.ApiParam)({ name: 'id', description: 'Client system UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Client system deleted successfully',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.SYSTEM_MANAGEMENT,
        resourceType: 'ClientSystem',
        description: 'Deleted client system',
    }),
    __param(0, (0, common_1.Param)('id', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ClientSystemsController.prototype, "remove", null);
exports.ClientSystemsController = ClientSystemsController = __decorate([
    (0, swagger_1.ApiTags)('client-systems'),
    (0, common_1.Controller)('client-systems'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    __metadata("design:paramtypes", [client_systems_service_1.ClientSystemsService])
], ClientSystemsController);
//# sourceMappingURL=client-systems.controller.js.map