'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { licenseCategoryService, LicenseCategory } from '@/services/licenseCategoryService';
import { applicationService } from '@/services/applicationService';
import ApplicantInfo from '@/components/applications/ApplicantInfo';
import { ApplicantInfoData } from '@/components/applications';

// Define application steps for navigation
const APPLICATION_STEPS = [
  { id: 'applicant-info', name: 'Applicant Information' },
  { id: 'company-profile', name: 'Company Profile' },
  { id: 'management', name: 'Management Team' },
  { id: 'professional-services', name: 'Professional Services' },
  { id: 'business-info', name: 'Business Information' },
  { id: 'service-scope', name: 'Service Scope' },
  { id: 'business-plan', name: 'Business Plan' },
  { id: 'legal-history', name: 'Legal History' },
  { id: 'review-submit', name: 'Review & Submit' },
];

const ApplicantInfoPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State
  const [licenseCategory, setLicenseCategory] = useState<LicenseCategory | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<ApplicantInfoData>({
    applicantName: '',
    postalPoBox: '',
    postalCity: '',
    postalCountry: '',
    physicalStreet: '',
    physicalCity: '',
    physicalCountry: '',
    telephone: '',
    fax: '',
    email: ''
  });
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  const currentStepIndex = APPLICATION_STEPS.findIndex(step => step.id === 'applicant-info');

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }
  }, [isAuthenticated, authLoading, router]);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        if (!licenseCategoryId) {
          setError('License category ID is required');
          setLoading(false);
          return;
        }

        // Load license category
        const category = await licenseCategoryService.getLicenseCategory(licenseCategoryId);
        if (!category) {
          setError('License category not found');
          setLoading(false);
          return;
        }

        setLicenseCategory(category);

        // If editing existing application, load application data
        if (applicationId) {
          try {
            const application = await applicationService.getApplication(applicationId);
            if (!application) {
              setError('Application not found');
              setLoading(false);
              return;
            }

            // Validate that application belongs to the specified license category
            if (application.license_category_id !== licenseCategoryId) {
              setError('Application does not belong to the specified license category');
              setLoading(false);
              return;
            }

            // Load applicant data if available
            // TODO: Load actual form data from the application
          } catch (err) {
            console.error('Error loading application:', err);
            setError('Failed to load application data');
            setLoading(false);
            return;
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load application data');
        setLoading(false);
      }
    };

    if (isAuthenticated && !authLoading) {
      loadData();
    }
  }, [licenseCategoryId, applicationId, isAuthenticated, authLoading]);

  // Form handlers
  const handleFormDataChange = (data: ApplicantInfoData) => {
    setFormData(data);
  };

  // Navigation handlers
  const handleNextStep = () => {
    const nextStep = APPLICATION_STEPS[currentStepIndex + 1];
    if (nextStep) {
      const params = new URLSearchParams();
      params.set('license_category_id', licenseCategoryId!);
      if (applicationId) {
        params.set('application_id', applicationId);
      }
      router.push(`/customer/applications/apply/${nextStep.id}?${params.toString()}`);
    }
  };

  const handlePreviousStep = () => {
    router.push('/customer/applications');
  };

  const handleStepClick = (stepIndex: number) => {
    const step = APPLICATION_STEPS[stepIndex];
    const params = new URLSearchParams();
    params.set('license_category_id', licenseCategoryId!);
    if (applicationId) {
      params.set('application_id', applicationId);
    }
    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);
  };

  const handleBackToApplications = () => {
    router.push('/customer/applications');
  };
  // Loading state
  if (authLoading || loading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="mb-4">
              <i className="ri-error-warning-line text-4xl text-red-500"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Error Loading Application
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {error}
            </p>
            <button
              onClick={handleBackToApplications}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Back to Applications
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  const currentStep = APPLICATION_STEPS[currentStepIndex];

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={handleBackToApplications}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mb-4"
          >
            <i className="ri-arrow-left-line mr-1"></i>
            Back to Applications
          </button>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {licenseCategory?.name} Application
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            {applicationId ? 'Edit your application' : 'Complete your license application'}
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <nav aria-label="Progress">
            <ol className="flex items-center">
              {APPLICATION_STEPS.map((step, index) => (
                <li key={step.id} className={`relative ${index !== APPLICATION_STEPS.length - 1 ? 'pr-8 sm:pr-20' : ''}`}>
                  {index !== APPLICATION_STEPS.length - 1 && (
                    <div className="absolute inset-0 flex items-center" aria-hidden="true">
                      <div className="h-0.5 w-full bg-gray-200 dark:bg-gray-700" />
                    </div>
                  )}
                  <button
                    onClick={() => handleStepClick(index)}
                    className={`relative w-8 h-8 flex items-center justify-center rounded-full border-2 ${
                      index === currentStepIndex
                        ? 'border-primary bg-primary text-white'
                        : index < currentStepIndex
                        ? 'border-primary bg-primary text-white'
                        : 'border-gray-300 bg-white text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400'
                    } hover:border-primary transition-colors`}
                  >
                    {index < currentStepIndex ? (
                      <i className="ri-check-line text-sm"></i>
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </button>
                  <span className="absolute top-10 left-1/2 transform -translate-x-1/2 text-xs font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap">
                    {step.name}
                  </span>
                </li>
              ))}
            </ol>
          </nav>
        </div>

        {/* Current Step Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {currentStep.name}
            </h2>
          </div>

          <div className="p-6">
            <ApplicantInfo
              data={formData}
              onChange={handleFormDataChange}
              errors={formErrors}
              disabled={loading}
            />
          </div>

          {/* Navigation */}
          <div className="px-6 py-4 border-t border-gray-200 dark:border-gray-700 flex justify-between">
            <button
              onClick={handlePreviousStep}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Back to Applications
            </button>
            <button
              onClick={handleNextStep}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              Next: Company Profile
              <i className="ri-arrow-right-line ml-2"></i>
            </button>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default ApplicantInfoPage;
