'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import TextInput from '@/components/common/TextInput';
import Select from '@/components/common/Select';
import { LicenseCategory } from '@/services/licenseCategoryService';
import { applicationFormDataService } from '@/services/applicationFormDataService';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { applicationProgressService } from '@/services/applicationProgressService';
import { validateSection } from '@/utils/formValidation';
import { getLicenseTypeStepConfig, StepConfig, LicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';
import { CustomerApiService } from '@/lib/customer-api';

const ApplicantInfoPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Create customer API service instance
  const customerApi = new CustomerApiService();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State
  const [licenseCategory, setLicenseCategory] = useState<LicenseCategory | null>(null);
  const [licenseTypeConfig, setLicenseTypeConfig] = useState<LicenseTypeStepConfig | null>(null);
  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form data state
  const [formData, setFormData] = useState({
    applicant_type: '',
    first_name: '',
    last_name: '',
    middle_name: '',
    email: '',
    phone: '',
    national_id: '',
    date_of_birth: '',
    nationality: 'Malawian',
    gender: '',
    postal_address: '',
    physical_address: '',
    city: '',
    district: '',
    postal_code: ''
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [applicationCreated, setApplicationCreated] = useState(false);
  const [createdApplicationId, setCreatedApplicationId] = useState<string | null>(null);

  const currentStepIndex = applicationSteps.findIndex(step => step.id === 'applicant-info');

  // Form handling functions
  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);

    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Save function
  const handleSave = async () => {
    setIsSaving(true);
    setValidationErrors({});

    try {
      // Validate form data
      const validation = validateSection('applicantInfo', formData as Record<string, any>);
      if (!validation.isValid) {
        setValidationErrors(validation.errors);
        setIsSaving(false);
        return;
      }

      let currentApplicationId = applicationId;

      // If no application exists, create one
      if (!currentApplicationId) {
        console.log('Creating new application...');

        // First create applicant
        const applicantData = {
          name: `${formData.first_name} ${formData.last_name}`,
          first_name: formData.first_name,
          last_name: formData.last_name,
          middle_name: formData.middle_name,
          email: formData.email,
          phone: formData.phone,
          national_id: formData.national_id,
          date_of_birth: formData.date_of_birth,
          nationality: formData.nationality,
          gender: formData.gender,
          postal_address: formData.postal_address,
          physical_address: formData.physical_address,
          city: formData.city,
          district: formData.district,
          postal_code: formData.postal_code,
          business_registration_number: '',
          tpin: '',
          website: '',
          business_type: formData.applicant_type || 'individual',
          date_incorporation: '',
          place_incorporation: ''
        };

        const applicant = await applicantService.createApplicant(applicantData);
        console.log('Applicant created:', applicant);

        // Then create application
        const applicationNumber = `APP-${Date.now()}-${Math.random().toString(36).substring(2, 11).toUpperCase()}`;
        const applicationData = {
          application_number: applicationNumber,
          license_category_id: licenseCategoryId!,
          applicant_id: applicant.applicant_id,
          status: 'draft' as any
        };

        const application = await applicationService.createApplication(applicationData);
        console.log('Application created:', application);

        currentApplicationId = application.application_id;
        setCreatedApplicationId(currentApplicationId);
        setApplicationCreated(true);
      } else {
        // Update existing applicant data
        console.log('Updating existing application...');

        const applicantData = {
          first_name: formData.first_name,
          last_name: formData.last_name,
          middle_name: formData.middle_name,
          email: formData.email,
          phone: formData.phone,
          national_id: formData.national_id,
          date_of_birth: formData.date_of_birth,
          nationality: formData.nationality,
          gender: formData.gender,
          postal_address: formData.postal_address,
          physical_address: formData.physical_address,
          city: formData.city,
          district: formData.district,
          postal_code: formData.postal_code
        };

        // Get application to find applicant_id
        const application = await applicationService.getApplication(currentApplicationId);
        if (application.applicant_id) {
          await applicantService.updateApplicant(application.applicant_id, applicantData);
        }
      }

      // Save form data
      await applicationFormDataService.saveFormSection(currentApplicationId, 'applicantInfo', formData);

      setHasUnsavedChanges(false);
      console.log('Application saved successfully');

    } catch (error) {
      console.error('Error saving application:', error);
      setValidationErrors({ save: 'Failed to save application. Please try again.' });
    } finally {
      setIsSaving(false);
    }
  };

  // Authentication check
  useEffect(() => {
    console.log('Auth state:', { isAuthenticated, authLoading });
    if (!authLoading && !isAuthenticated) {
      console.log('User not authenticated, redirecting to login');
      router.push('/customer/auth/login');
      return;
    }
  }, [isAuthenticated, authLoading, router]);

  // Load data
  useEffect(() => {
    const loadData = async () => {
      try {
        if (!licenseCategoryId) {
          setError('License category ID is required');
          setLoading(false);
          return;
        }

        console.log('Loading license category:', licenseCategoryId);

        // Load license category
        const category = await customerApi.getLicenseCategory(licenseCategoryId);
        console.log('License category response:', category);

        if (!category) {
          setError('License category not found');
          setLoading(false);
          return;
        }

        setLicenseCategory(category);

        // Load license type configuration to get the steps
        console.log('Loading license type config for:', category.license_type_id);
        console.log('Full category object:', category);

        if (!category.license_type_id) {
          setError('License category is missing license type information');
          setLoading(false);
          return;
        }

        const config = getLicenseTypeStepConfig(category.license_type_id);
        console.log('License type config:', config);

        if (!config) {
          setError(`No configuration found for license type: ${category.license_type_id}`);
          setLoading(false);
          return;
        }
        setLicenseTypeConfig(config);
        setApplicationSteps(config.steps);

        console.log('Data loading completed successfully');
        setLoading(false);
      } catch (err: any) {
        console.error('Error loading data:', err);
        console.error('Error details:', {
          message: err.message,
          response: err.response?.data,
          status: err.response?.status
        });

        // Provide more specific error message
        let errorMessage = 'Failed to load application data';
        if (err.response?.status === 404) {
          errorMessage = `License category not found (ID: ${licenseCategoryId}). Please go back to the applications page and select a valid license category.`;
        } else if (err.response?.status === 401) {
          errorMessage = 'You are not authorized to access this license category. Please log in again.';
        } else if (err.response?.status === 500) {
          errorMessage = 'Server error occurred. Please try again later or contact support.';
        } else if (err.message) {
          errorMessage = `Error: ${err.message}`;
        }

        setError(errorMessage);
        setLoading(false);
      }
    };

    if (isAuthenticated && !authLoading) {
      loadData();
    }
  }, [licenseCategoryId, applicationId, isAuthenticated, authLoading]);

  // Navigation handlers for the ApplicantInfo component
  const handleNext = () => {
    const nextStep = applicationSteps[currentStepIndex + 1];
    if (nextStep) {
      const params = new URLSearchParams();
      params.set('license_category_id', licenseCategoryId!);
      if (applicationId) {
        params.set('application_id', applicationId);
      }
      router.push(`/customer/applications/apply/${nextStep.id}?${params.toString()}`);
    }
  };

  const handlePrevious = () => {
    router.push('/customer/applications');
  };

  const handleStepComplete = (stepId: string, data?: any) => {
    console.log('Step completed:', stepId, data);
    // If data contains applicationId, update the URL
    if (data?.applicationId) {
      const params = new URLSearchParams(window.location.search);
      params.set('application_id', data.applicationId);
      const newUrl = `${window.location.pathname}?${params.toString()}`;
      window.history.replaceState({}, '', newUrl);
    }
  };

  const handleStepError = (stepId: string, errors: any) => {
    console.error('Step error:', stepId, errors);
    setError(typeof errors === 'string' ? errors : 'An error occurred while processing the step');
  };

  const handleStepClick = (stepIndex: number) => {
    // Prevent navigation to future steps if not editing an existing application
    if (!applicationId && stepIndex > currentStepIndex) {
      setError('Please complete the current step before proceeding to the next step');
      return;
    }

    const step = applicationSteps[stepIndex];
    const params = new URLSearchParams();
    params.set('license_category_id', licenseCategoryId!);
    if (applicationId) {
      params.set('application_id', applicationId);
    }
    router.push(`/customer/applications/apply/${step.id}?${params.toString()}`);
  };

  const handleBackToApplications = () => {
    router.push('/customer/applications');
  };
  // Loading state
  if (authLoading || loading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="mb-4">
              <i className="ri-error-warning-line text-4xl text-red-500"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Error Loading Application
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {error}
            </p>
            <button
              onClick={() => router.push('/customer/applications')}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Back to Applications
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  const currentStep = applicationSteps[currentStepIndex];

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={handleBackToApplications}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mb-4"
          >
            <i className="ri-arrow-left-line mr-1"></i>
            Back to Applications
          </button>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {licenseCategory?.name} Application
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            {applicationId ? 'Edit your application' : 'Complete your license application'}
          </p>
        </div>

        {/* Progress Steps - Vertical Layout for Scalability */}
        <div className="mb-8">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100 mb-4">
              Application Progress ({currentStepIndex + 1} of {applicationSteps.length})
            </h3>
            <div className="space-y-2">
              {applicationSteps.map((step, index) => {
                const isAccessible = applicationId || index <= currentStepIndex;
                return (
                  <div
                    key={step.id}
                    className={`flex items-center p-2 rounded-md transition-colors ${
                      isAccessible ? 'cursor-pointer' : 'cursor-not-allowed opacity-60'
                    } ${
                      index === currentStepIndex
                        ? 'bg-primary/10 border border-primary/20'
                        : index < currentStepIndex
                        ? 'bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800'
                        : 'bg-gray-50 dark:bg-gray-700/50 border border-gray-200 dark:border-gray-600'
                    }`}
                    onClick={() => isAccessible && handleStepClick(index)}
                  >
                  <div
                    className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-medium mr-3 ${
                      index === currentStepIndex
                        ? 'bg-primary text-white'
                        : index < currentStepIndex
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300'
                    }`}
                  >
                    {index < currentStepIndex ? (
                      <i className="ri-check-line"></i>
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="flex-1">
                    <div className={`text-sm font-medium ${
                      index === currentStepIndex
                        ? 'text-primary'
                        : index < currentStepIndex
                        ? 'text-green-700 dark:text-green-300'
                        : 'text-gray-600 dark:text-gray-400'
                    }`}>
                      {step.name}
                    </div>
                    {step.description && (
                      <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                        {step.description}
                      </div>
                    )}
                  </div>
                  {step.required && (
                    <span className="text-xs text-red-500 ml-2">*</span>
                  )}
                </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Current Step Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {currentStep.name}
            </h2>
          </div>

          <div className="p-6">
            {/* Header */}
            <div className="mb-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                Applicant Information
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Please provide your personal information. This will create your application record.
              </p>
              {!applicationId && (
                <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <i className="ri-information-line mr-1"></i>
                    Your application will be created when you save this step.
                  </p>
                </div>
              )}
              {applicationCreated && (
                <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-sm text-green-700 dark:text-green-300">
                    <i className="ri-check-line mr-1"></i>
                    Application created: {createdApplicationId?.slice(0, 8)}...
                  </p>
                </div>
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Applicant Type */}
              <div className="md:col-span-2">
                <Select
                  label="Applicant Type"
                  value={formData.applicant_type || ''}
                  onChange={(value) => handleFormChange('applicant_type', value)}
                  options={[
                    { value: 'individual', label: 'Individual' },
                    { value: 'company', label: 'Company' },
                    { value: 'organization', label: 'Organization' }
                  ]}
                  required
                  error={validationErrors.applicant_type}
                />
              </div>

              {/* Personal Information */}
              <TextInput
                label="First Name"
                value={formData.first_name || ''}
                onChange={(e) => handleFormChange('first_name', e.target.value)}
                required
                error={validationErrors.first_name}
              />

              <TextInput
                label="Last Name"
                value={formData.last_name || ''}
                onChange={(e) => handleFormChange('last_name', e.target.value)}
                required
                error={validationErrors.last_name}
              />

              <TextInput
                label="Middle Name"
                value={formData.middle_name || ''}
                onChange={(e) => handleFormChange('middle_name', e.target.value)}
                error={validationErrors.middle_name}
              />

              <Select
                label="Gender"
                value={formData.gender || ''}
                onChange={(value) => handleFormChange('gender', value)}
                options={[
                  { value: 'male', label: 'Male' },
                  { value: 'female', label: 'Female' },
                  { value: 'other', label: 'Other' }
                ]}
                required
                error={validationErrors.gender}
              />

              {/* Contact Information */}
              <TextInput
                label="Email Address"
                type="email"
                value={formData.email || ''}
                onChange={(e) => handleFormChange('email', e.target.value)}
                required
                error={validationErrors.email}
              />

              <TextInput
                label="Phone Number"
                value={formData.phone || ''}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                placeholder="+265 1 234 567"
                required
                error={validationErrors.phone}
              />

              {/* Identification */}
              <TextInput
                label="National ID"
                value={formData.national_id || ''}
                onChange={(e) => handleFormChange('national_id', e.target.value)}
                placeholder="**********"
                required
                error={validationErrors.national_id}
              />

              <TextInput
                label="Date of Birth"
                type="date"
                value={formData.date_of_birth || ''}
                onChange={(e) => handleFormChange('date_of_birth', e.target.value)}
                required
                error={validationErrors.date_of_birth}
              />

              <Select
                label="Nationality"
                value={formData.nationality || 'Malawian'}
                onChange={(value) => handleFormChange('nationality', value)}
                options={[
                  { value: 'Malawian', label: 'Malawian' },
                  { value: 'Other', label: 'Other' }
                ]}
                required
                error={validationErrors.nationality}
              />
            </div>

            {/* Address Information */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Address Information
              </h4>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="md:col-span-2">
                  <TextInput
                    label="Postal Address"
                    value={formData.postal_address || ''}
                    onChange={(e) => handleFormChange('postal_address', e.target.value)}
                    placeholder="P.O. Box 123"
                    required
                    error={validationErrors.postal_address}
                  />
                </div>

                <div className="md:col-span-2">
                  <TextInput
                    label="Physical Address"
                    value={formData.physical_address || ''}
                    onChange={(e) => handleFormChange('physical_address', e.target.value)}
                    placeholder="Street address"
                    required
                    error={validationErrors.physical_address}
                  />
                </div>

                <TextInput
                  label="City"
                  value={formData.city || ''}
                  onChange={(e) => handleFormChange('city', e.target.value)}
                  required
                  error={validationErrors.city}
                />

                <Select
                  label="District"
                  value={formData.district || ''}
                  onChange={(value) => handleFormChange('district', value)}
                  options={[
                    { value: 'Blantyre', label: 'Blantyre' },
                    { value: 'Lilongwe', label: 'Lilongwe' },
                    { value: 'Mzuzu', label: 'Mzuzu' },
                    { value: 'Zomba', label: 'Zomba' },
                    { value: 'Other', label: 'Other' }
                  ]}
                  required
                  error={validationErrors.district}
                />

                <TextInput
                  label="Postal Code"
                  value={formData.postal_code || ''}
                  onChange={(e) => handleFormChange('postal_code', e.target.value)}
                  error={validationErrors.postal_code}
                />
              </div>
            </div>

            {/* Error Display */}
            {validationErrors.save && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-center">
                  <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"></i>
                  <div>
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                      Error Saving Application
                    </h3>
                    <p className="text-red-700 dark:text-red-300 text-sm mt-1">
                      {validationErrors.save}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Success Message */}
            {applicationCreated && !applicationId && (
              <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
                <div className="flex items-center">
                  <i className="ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3"></i>
                  <div>
                    <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                      Application Created Successfully!
                    </h3>
                    <p className="text-green-700 dark:text-green-300 text-sm mt-1">
                      Your application has been created. You can now continue to the next step.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
              {applicationCreated && !applicationId ? (
                <>
                  {/* Save Changes Button (for edit mode) */}
                  <button
                    onClick={handleSave}
                    disabled={isSaving}
                    className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {isSaving ? (
                      <>
                        <i className="ri-loader-4-line animate-spin mr-2"></i>
                        Saving...
                      </>
                    ) : (
                      <>
                        <i className="ri-save-line mr-2"></i>
                        Save Changes
                      </>
                    )}
                  </button>

                  {/* Continue to Next Step Button */}
                  <button
                    onClick={() => {
                      const nextStep = applicationSteps[currentStepIndex + 1];
                      if (nextStep) {
                        const params = new URLSearchParams();
                        params.set('license_category_id', licenseCategoryId!);
                        if (createdApplicationId) {
                          params.set('application_id', createdApplicationId);
                        }
                        router.push(`/customer/applications/apply/${nextStep.id}?${params.toString()}`);
                      }
                    }}
                    className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
                  >
                    <i className="ri-arrow-right-line mr-2"></i>
                    Continue to Company Profile
                  </button>
                </>
              ) : (
                /* Create/Save Application Button */
                <button
                  onClick={handleSave}
                  disabled={isSaving}
                  className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isSaving ? (
                    <>
                      <i className="ri-loader-4-line animate-spin mr-2"></i>
                      {applicationId ? 'Saving...' : 'Creating Application...'}
                    </>
                  ) : (
                    <>
                      <i className="ri-save-line mr-2"></i>
                      {applicationId ? 'Save Changes' : 'Create Application'}
                    </>
                  )}
                </button>
              )}
            </div>
          </div>


        </div>
      </div>
    </CustomerLayout>
  );
};

export default ApplicantInfoPage;
