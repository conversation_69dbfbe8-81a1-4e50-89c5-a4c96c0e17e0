import { Repository } from 'typeorm';
import { ClientSystems } from '../entities/client-systems.entity';
import { CreateClientSystemDto } from '../dto/client-system/create-client-system.dto';
import { UpdateClientSystemDto } from '../dto/client-system/update-client-system.dto';
import { PaginateQuery, Paginated } from 'nestjs-paginate';
export declare class ClientSystemsService {
    private readonly clientSystemsRepository;
    constructor(clientSystemsRepository: Repository<ClientSystems>);
    create(createClientSystemDto: CreateClientSystemDto, userId: string): Promise<ClientSystems>;
    findAll(query: PaginateQuery): Promise<Paginated<ClientSystems>>;
    findOne(id: string): Promise<ClientSystems>;
    findBySystemCode(systemCode: string): Promise<ClientSystems>;
    update(id: string, updateClientSystemDto: UpdateClientSystemDto, userId: string): Promise<ClientSystems>;
    remove(id: string): Promise<void>;
    updateLastAccessed(id: string): Promise<void>;
    getSystemStats(): Promise<{
        total: number;
        active: number;
        inactive: number;
        maintenance: number;
        deprecated: number;
        byType: Record<string, number>;
    }>;
}
