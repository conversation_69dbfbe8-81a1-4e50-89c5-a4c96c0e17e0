(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/LogoutButton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LogoutButton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function LogoutButton({ variant = 'primary', size = 'md', className = '', showConfirmation = true, redirectTo = '/auth/login', children }) {
    _s();
    const [isLoggingOut, setIsLoggingOut] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showConfirm, setShowConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { logout, user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const handleLogout = async ()=>{
        if (showConfirmation && !showConfirm) {
            setShowConfirm(true);
            return;
        }
        setIsLoggingOut(true);
        try {
            // Call logout from context
            logout();
            // Small delay to ensure cleanup is complete
            await new Promise((resolve)=>setTimeout(resolve, 100));
            if (pathname.includes('customer')) {
                // Redirect to specified page
                router.push('/customer/auth/login');
            } else {
                router.push('/auth/login');
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally{
            setIsLoggingOut(false);
            setShowConfirm(false);
        }
    };
    const handleCancel = ()=>{
        setShowConfirm(false);
    };
    // Base styles for different variants
    const getVariantStyles = ()=>{
        switch(variant){
            case 'primary':
                return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';
            case 'secondary':
                return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';
            case 'text':
                return 'bg-transparent hover:bg-red-50 text-red-600 border-none';
            case 'icon':
                return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';
            default:
                return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';
        }
    };
    // Size styles
    const getSizeStyles = ()=>{
        switch(size){
            case 'sm':
                return 'px-3 py-1.5 text-sm';
            case 'md':
                return 'px-4 py-2 text-base';
            case 'lg':
                return 'px-6 py-3 text-lg';
            default:
                return 'px-4 py-2 text-base';
        }
    };
    const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;
    // Confirmation dialog
    if (showConfirm) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-shrink-0",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    className: "h-6 w-6 text-red-600",
                                    fill: "none",
                                    viewBox: "0 0 24 24",
                                    stroke: "currentColor",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/LogoutButton.tsx",
                                        lineNumber: 108,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/LogoutButton.tsx",
                                    lineNumber: 107,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 106,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "ml-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium text-gray-900",
                                    children: "Confirm Logout"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/LogoutButton.tsx",
                                    lineNumber: 112,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 111,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 105,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-gray-500",
                            children: [
                                "Are you sure you want to logout",
                                user?.first_name ? `, ${user.first_name}` : '',
                                "? You will need to login again to access your account."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/LogoutButton.tsx",
                            lineNumber: 117,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 116,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleLogout,
                                disabled: isLoggingOut,
                                className: "flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50",
                                children: isLoggingOut ? 'Logging out...' : 'Yes, Logout'
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 123,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleCancel,
                                className: "flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200",
                                children: "Cancel"
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 130,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 122,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/LogoutButton.tsx",
                lineNumber: 104,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/LogoutButton.tsx",
            lineNumber: 103,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: handleLogout,
        disabled: isLoggingOut,
        className: buttonStyles,
        title: "Logout",
        children: children || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: variant === 'icon' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "h-5 w-5",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                }, void 0, false, {
                    fileName: "[project]/src/components/LogoutButton.tsx",
                    lineNumber: 153,
                    columnNumber: 15
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/LogoutButton.tsx",
                lineNumber: 152,
                columnNumber: 13
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "h-4 w-4 mr-2",
                        fill: "none",
                        viewBox: "0 0 24 24",
                        stroke: "currentColor",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                        }, void 0, false, {
                            fileName: "[project]/src/components/LogoutButton.tsx",
                            lineNumber: 158,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 157,
                        columnNumber: 15
                    }, this),
                    isLoggingOut ? 'Logging out...' : 'Logout'
                ]
            }, void 0, true)
        }, void 0, false)
    }, void 0, false, {
        fileName: "[project]/src/components/LogoutButton.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
}
_s(LogoutButton, "GK6euFXnNSahEQDYKCx2YSuMdTI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = LogoutButton;
var _c;
__turbopack_context__.k.register(_c, "LogoutButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/CustomerLayout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/LoadingContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LogoutButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/LogoutButton.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
const CustomerLayout = ({ children, breadcrumbs })=>{
    _s();
    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isUserDropdownOpen, setIsUserDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user, logout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const { showLoader } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"])();
    // Memoize navigation items to prevent unnecessary re-renders
    const navigationItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CustomerLayout.useMemo[navigationItems]": ()=>[
                {
                    name: 'Dashboard',
                    href: '/customer',
                    icon: 'ri-dashboard-line',
                    current: pathname === '/customer'
                },
                {
                    name: 'My Licenses',
                    href: '/customer/my-licenses',
                    icon: 'ri-key-line',
                    current: pathname === '/customer/my-licenses'
                },
                {
                    name: 'New Applications',
                    href: '/customer/applications',
                    icon: 'ri-file-list-3-line',
                    current: pathname === '/customer/applications'
                },
                {
                    name: 'Payments',
                    href: '/customer/payments',
                    icon: 'ri-bank-card-line',
                    current: pathname === '/customer/payments'
                },
                {
                    name: 'Documents',
                    href: '/customer/documents',
                    icon: 'ri-file-text-line',
                    current: pathname === '/customer/documents'
                },
                {
                    name: 'Procurement',
                    href: '/customer/procurement',
                    icon: 'ri-auction-line',
                    current: pathname === '/customer/procurement'
                },
                {
                    name: 'Request Resource',
                    href: '/customer/resources',
                    icon: 'ri-hand-heart-line',
                    current: pathname === '/customer/resources'
                }
            ]
    }["CustomerLayout.useMemo[navigationItems]"], [
        pathname
    ]);
    const supportItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CustomerLayout.useMemo[supportItems]": ()=>[
                {
                    name: 'Consumer Affairs',
                    href: '/customer/consumer-affairs',
                    icon: 'ri-shield-user-line'
                },
                {
                    name: 'Help Center',
                    href: '/customer/help',
                    icon: 'ri-question-line'
                }
            ]
    }["CustomerLayout.useMemo[supportItems]"], []);
    // Prefetch customer pages on mount for faster navigation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CustomerLayout.useEffect": ()=>{
            const prefetchPages = {
                "CustomerLayout.useEffect.prefetchPages": ()=>{
                    const customerPages = [
                        '/customer',
                        '/customer/applications',
                        '/customer/applications/standards',
                        '/customer/payments',
                        '/customer/my-licenses',
                        '/customer/procurement',
                        '/customer/profile',
                        '/customer/consumer-affairs',
                        '/customer/resources',
                        '/customer/help'
                    ];
                    customerPages.forEach({
                        "CustomerLayout.useEffect.prefetchPages": (page)=>{
                            router.prefetch(page);
                        }
                    }["CustomerLayout.useEffect.prefetchPages"]);
                }
            }["CustomerLayout.useEffect.prefetchPages"];
            // Delay prefetching to not interfere with initial page load
            const timer = setTimeout(prefetchPages, 1000);
            return ({
                "CustomerLayout.useEffect": ()=>clearTimeout(timer)
            })["CustomerLayout.useEffect"];
        }
    }["CustomerLayout.useEffect"], [
        router
    ]);
    const toggleMobileSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[toggleMobileSidebar]": ()=>{
            setIsMobileSidebarOpen(!isMobileSidebarOpen);
        }
    }["CustomerLayout.useCallback[toggleMobileSidebar]"], [
        isMobileSidebarOpen
    ]);
    const handleNavClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[handleNavClick]": (href, name)=>{
            const pageMessages = {
                '/customer': 'Loading Dashboard...',
                '/customer/licenses': 'Loading My Licenses...',
                '/customer/applications': 'Loading Applications...',
                '/customer/applications/standards': 'Loading Standards License Options...',
                '/customer/payments': 'Loading Payments...',
                '/customer/documents': 'Loading Documents...',
                '/customer/procurement': 'Loading Procurement...',
                '/customer/resources': 'Loading Resources...',
                '/customer/consumer-affairs': 'Loading Consumer Affairs...',
                '/customer/help': 'Loading Help Center...',
                '/customer/profile': 'Loading Profile...',
                '/customer/settings': 'Loading Settings...'
            };
            const message = pageMessages[href] || `Loading ${name}...`;
            showLoader(message);
            setIsMobileSidebarOpen(false);
        }
    }["CustomerLayout.useCallback[handleNavClick]"], [
        showLoader
    ]);
    const handleNavHover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[handleNavHover]": (href)=>{
            // Prefetch on hover for instant navigation
            router.prefetch(href);
        }
    }["CustomerLayout.useCallback[handleNavHover]"], [
        router
    ]);
    const toggleUserDropdown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[toggleUserDropdown]": ()=>{
            setIsUserDropdownOpen(!isUserDropdownOpen);
        }
    }["CustomerLayout.useCallback[toggleUserDropdown]"], [
        isUserDropdownOpen
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-screen overflow-hidden",
        children: [
            isMobileSidebarOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",
                onClick: ()=>setIsMobileSidebarOpen(false)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                lineNumber: 152,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                className: `
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated
        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col h-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: "/images/macra-logo.png",
                                    alt: "MACRA Logo",
                                    className: "max-h-12 w-auto",
                                    width: 120,
                                    height: 48,
                                    priority: true
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 167,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                lineNumber: 166,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 165,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "mt-6 px-4 side-nav",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-1",
                                    children: navigationItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: item.href,
                                            onClick: ()=>handleNavClick(item.href, item.name),
                                            onMouseEnter: ()=>handleNavHover(item.href),
                                            className: `
                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated
                    ${item.current ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'}
                  `,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: `w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: item.icon
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 197,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 196,
                                                    columnNumber: 19
                                                }, this),
                                                item.name
                                            ]
                                        }, item.name, true, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 183,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 181,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-8",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider",
                                            children: "Support"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 206,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mt-2 space-y-1",
                                            children: supportItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    href: item.href,
                                                    onClick: ()=>handleNavClick(item.href, item.name),
                                                    onMouseEnter: ()=>handleNavHover(item.href),
                                                    className: "flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "w-5 h-5 flex items-center justify-center mr-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                className: item.icon
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                                lineNumber: 219,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                            lineNumber: 218,
                                                            columnNumber: 21
                                                        }, this),
                                                        item.name
                                                    ]
                                                }, item.name, true, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 211,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 209,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 205,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 179,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        className: "h-10 w-10 rounded-full object-cover",
                                        src: user?.profile_image || "https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp",
                                        alt: "Profile",
                                        width: 40,
                                        height: 40
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 231,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/customer/profile",
                                        className: "flex-1 min-w-0",
                                        onClick: ()=>handleNavClick('/customer/profile', 'Profile'),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 truncate",
                                                children: user ? `${user.first_name} ${user.last_name}` : 'Customer'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                lineNumber: 243,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-gray-500 dark:text-gray-400 truncate"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                lineNumber: 246,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 238,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                lineNumber: 230,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 229,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                    lineNumber: 163,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                lineNumber: 159,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 flex flex-col overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                        className: "bg-white dark:bg-gray-800 shadow-sm z-10",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between h-16 px-4 sm:px-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: toggleMobileSidebar,
                                    className: "md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none",
                                    "aria-label": "Open mobile menu",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-6 h-6 flex items-center justify-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "ri-menu-line ri-lg"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 267,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 266,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 260,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1",
                                    children: breadcrumbs && breadcrumbs.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                        className: "flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400",
                                        children: breadcrumbs.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                                                children: [
                                                    index > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "ri-arrow-right-s-line"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 276,
                                                        columnNumber: 37
                                                    }, this),
                                                    item.href ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: item.href,
                                                        className: "hover:text-primary",
                                                        children: item.label
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 278,
                                                        columnNumber: 25
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-gray-900 dark:text-gray-100",
                                                        children: item.label
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 282,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                lineNumber: 275,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 273,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 271,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            className: "flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "sr-only",
                                                    children: "View notifications"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 295,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-6 h-6 flex items-center justify-center",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "ri-notification-3-line ri-lg"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 297,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 296,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 299,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 291,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: toggleUserDropdown,
                                                    className: "flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "sr-only",
                                                            children: "Open user menu"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                            lineNumber: 308,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            className: "h-8 w-8 rounded-full",
                                                            src: user?.profile_image || "https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp",
                                                            alt: "Profile",
                                                            width: 32,
                                                            height: 32
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                            lineNumber: 309,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 303,
                                                    columnNumber: 17
                                                }, this),
                                                isUserDropdownOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "py-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                href: "/customer/profile",
                                                                className: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",
                                                                onClick: ()=>{
                                                                    handleNavClick('/customer/profile', 'Profile');
                                                                    setIsUserDropdownOpen(false);
                                                                },
                                                                children: "Your Profile"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                                lineNumber: 321,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LogoutButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                variant: "text",
                                                                size: "sm",
                                                                className: "w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",
                                                                showConfirmation: true,
                                                                children: "Sign out"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                                lineNumber: 332,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 320,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 319,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 302,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 290,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 259,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                        lineNumber: 258,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                        className: "flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "py-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                                children: children
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                lineNumber: 351,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 350,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                        lineNumber: 349,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                lineNumber: 256,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
        lineNumber: 149,
        columnNumber: 5
    }, this);
};
_s(CustomerLayout, "YYYJNDbRlkZoLk1sqCOGBpf4yIY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"]
    ];
});
_c = CustomerLayout;
const __TURBOPACK__default__export__ = CustomerLayout;
var _c;
__turbopack_context__.k.register(_c, "CustomerLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/cacheService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Cache service for API responses to reduce rate limiting
__turbopack_context__.s({
    "CACHE_KEYS": (()=>CACHE_KEYS),
    "CACHE_TTL": (()=>CACHE_TTL),
    "cacheService": (()=>cacheService),
    "default": (()=>__TURBOPACK__default__export__)
});
class CacheService {
    cache = new Map();
    defaultTTL = 5 * 60 * 1000;
    /**
   * Set cache item with TTL
   */ set(key, data, ttl = this.defaultTTL) {
        const now = Date.now();
        const item = {
            data,
            timestamp: now,
            expiresAt: now + ttl
        };
        this.cache.set(key, item);
        console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);
    }
    /**
   * Get cache item if not expired
   */ get(key) {
        const item = this.cache.get(key);
        if (!item) {
            console.log(`Cache MISS: ${key}`);
            return null;
        }
        const now = Date.now();
        if (now > item.expiresAt) {
            console.log(`Cache EXPIRED: ${key}`);
            this.cache.delete(key);
            return null;
        }
        console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);
        return item.data;
    }
    /**
   * Check if cache has valid item
   */ has(key) {
        return this.get(key) !== null;
    }
    /**
   * Delete cache item
   */ delete(key) {
        console.log(`Cache DELETE: ${key}`);
        return this.cache.delete(key);
    }
    /**
   * Clear all cache
   */ clear() {
        console.log('Cache CLEAR: All items');
        this.cache.clear();
    }
    /**
   * Get cache stats
   */ getStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
    /**
   * Clean expired items
   */ cleanup() {
        const now = Date.now();
        let cleaned = 0;
        for (const [key, item] of this.cache.entries()){
            if (now > item.expiresAt) {
                this.cache.delete(key);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);
        }
    }
    /**
   * Get or set pattern - fetch data if not cached
   */ async getOrSet(key, fetcher, ttl = this.defaultTTL) {
        // Try to get from cache first
        const cached = this.get(key);
        if (cached !== null) {
            return cached;
        }
        // Fetch fresh data
        console.log(`Cache FETCH: ${key}`);
        const data = await fetcher();
        // Store in cache
        this.set(key, data, ttl);
        return data;
    }
    /**
   * Invalidate cache by pattern
   */ invalidatePattern(pattern) {
        const regex = new RegExp(pattern);
        let invalidated = 0;
        for (const key of this.cache.keys()){
            if (regex.test(key)) {
                this.cache.delete(key);
                invalidated++;
            }
        }
        if (invalidated > 0) {
            console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);
        }
    }
}
const cacheService = new CacheService();
const CACHE_KEYS = {
    LICENSE_TYPES: 'license-types',
    LICENSE_CATEGORIES: 'license-categories',
    LICENSE_CATEGORIES_BY_TYPE: (typeId)=>`license-categories-type-${typeId}`,
    USER_APPLICATIONS: 'user-applications',
    APPLICATION: (id)=>`application-${id}`
};
const CACHE_TTL = {
    SHORT: 2 * 60 * 1000,
    MEDIUM: 5 * 60 * 1000,
    LONG: 15 * 60 * 1000,
    VERY_LONG: 60 * 60 * 1000
};
// Auto cleanup every 5 minutes
setInterval(()=>{
    cacheService.cleanup();
}, 5 * 60 * 1000);
const __TURBOPACK__default__export__ = cacheService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/licenseTypeService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "licenseTypeService": (()=>licenseTypeService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/cacheService.ts [app-client] (ecmascript)");
;
;
const licenseTypeService = {
    // Get all license types with pagination
    async getLicenseTypes (query = {}) {
        const params = new URLSearchParams();
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());
        if (query.search) params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach((sort)=>params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach((search)=>params.append('searchBy', search));
        }
        if (query.filter) {
            Object.entries(query.filter).forEach(([key, value])=>{
                if (Array.isArray(value)) {
                    value.forEach((v)=>params.append(`filter.${key}`, v));
                } else {
                    params.set(`filter.${key}`, value);
                }
            });
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-types?${params.toString()}`);
        return response.data;
    },
    // Get license type by ID
    async getLicenseType (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-types/${id}`);
        return response.data;
    },
    // Create new license type
    async createLicenseType (licenseTypeData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/license-types', licenseTypeData);
        return response.data;
    },
    // Update license type
    async updateLicenseType (id, licenseTypeData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/license-types/${id}`, licenseTypeData);
        return response.data;
    },
    // Delete license type
    async deleteLicenseType (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/license-types/${id}`);
        return response.data;
    },
    // Get all license types (simple list for dropdowns) with caching
    async getAllLicenseTypes () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEYS"].LICENSE_TYPES, async ()=>{
            console.log('Fetching license types from API...');
            // Reduce limit to avoid rate limiting
            const response = await this.getLicenseTypes({
                limit: 100
            });
            return response.data;
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].LONG // Cache for 15 minutes
        );
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/licenseCategoryService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addCodesToCategories": (()=>addCodesToCategories),
    "findCategoryByCode": (()=>findCategoryByCode),
    "findCategoryById": (()=>findCategoryById),
    "generateCategoryCode": (()=>generateCategoryCode),
    "licenseCategoryService": (()=>licenseCategoryService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/cacheService.ts [app-client] (ecmascript)");
;
;
const generateCategoryCode = (name)=>{
    return name.toLowerCase().replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50); // Limit length
};
const addCodesToCategories = (categories)=>{
    return categories.map((category)=>({
            ...category,
            code: generateCategoryCode(category.name),
            children: category.children ? addCodesToCategories(category.children) : undefined
        }));
};
const findCategoryByCode = (categories, code)=>{
    for (const category of categories){
        if (category.code === code) {
            return category;
        }
        if (category.children) {
            const found = findCategoryByCode(category.children, code);
            if (found) return found;
        }
    }
    return null;
};
const findCategoryById = (categories, id)=>{
    for (const category of categories){
        if (category.license_category_id === id) {
            return category;
        }
        if (category.children) {
            const found = findCategoryById(category.children, id);
            if (found) return found;
        }
    }
    return null;
};
const licenseCategoryService = {
    // Get all license categories with pagination
    async getLicenseCategories (query = {}) {
        const params = new URLSearchParams();
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());
        if (query.search) params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach((sort)=>params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach((search)=>params.append('searchBy', search));
        }
        if (query.filter) {
            Object.entries(query.filter).forEach(([key, value])=>{
                if (Array.isArray(value)) {
                    value.forEach((v)=>params.append(`filter.${key}`, v));
                } else {
                    params.set(`filter.${key}`, value);
                }
            });
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories?${params.toString()}`);
        return response.data;
    },
    // Get license category by ID
    async getLicenseCategory (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/${id}`);
        return response.data;
    },
    // Get license categories by license type
    async getLicenseCategoriesByType (licenseTypeId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`);
        return response.data;
    },
    // Create new license category
    async createLicenseCategory (licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/license-categories', licenseCategoryData);
        return response.data;
    },
    // Update license category
    async updateLicenseCategory (id, licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/license-categories/${id}`, licenseCategoryData);
        return response.data;
    },
    // Delete license category
    async deleteLicenseCategory (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/license-categories/${id}`);
        return response.data;
    },
    // Get all license categories (simple list for dropdowns) with caching
    async getAllLicenseCategories () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEYS"].LICENSE_CATEGORIES, async ()=>{
            console.log('Fetching license categories from API...');
            // Reduce limit to avoid rate limiting
            const response = await this.getLicenseCategories({
                limit: 100
            });
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].LONG // Cache for 15 minutes
        );
    },
    // Get hierarchical tree of categories for a license type with caching
    async getCategoryTree (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`category-tree-${licenseTypeId}`, async ()=>{
            console.log(`Fetching category tree for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/tree`);
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get root categories (no parent) for a license type with caching
    async getRootCategories (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`root-categories-${licenseTypeId}`, async ()=>{
            console.log(`Fetching root categories for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/root`);
            return response.data;
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get license categories for parent selection dropdown
    async getCategoriesForParentSelection (licenseTypeId, excludeId) {
        try {
            const params = excludeId ? {
                excludeId
            } : {};
            console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);
            // Try the new endpoint first
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, {
                    params
                });
                if (response.data && Array.isArray(response.data.data)) {
                    console.log('✅ Valid array response with', response.data.data.length, 'items');
                    return response.data.data;
                } else {
                    console.warn('⚠️ API returned non-array data:', response.data);
                    return [];
                }
            } catch (newEndpointError) {
                console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);
                // Fallback to existing endpoint
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`);
                console.log('🔄 Fallback response:', response.data);
                if (response.data && Array.isArray(response.data)) {
                    // Filter out the excluded category if specified
                    let categories = response.data;
                    if (excludeId) {
                        categories = categories.filter((cat)=>cat.license_category_id !== excludeId);
                    }
                    console.log('✅ Fallback successful with', categories.length, 'items');
                    return categories;
                } else {
                    console.warn('⚠️ Fallback also returned non-array data:', response.data);
                    return [];
                }
            }
        } catch (error) {
            return [];
        }
    },
    // Get potential parent categories for a license type
    async getPotentialParents (licenseTypeId, excludeId) {
        const params = excludeId ? {
            excludeId
        } : {};
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, {
            params
        });
        return response.data;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/config/licenseTypeStepConfig.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * License Type Step Configuration System
 * Defines which form steps are required for each license type
 */ __turbopack_context__.s({
    "LICENSE_TYPE_STEP_CONFIGS": (()=>LICENSE_TYPE_STEP_CONFIGS),
    "calculateProgress": (()=>calculateProgress),
    "getLicenseTypeStepConfig": (()=>getLicenseTypeStepConfig),
    "getNextStep": (()=>getNextStep),
    "getOptionalSteps": (()=>getOptionalSteps),
    "getPreviousStep": (()=>getPreviousStep),
    "getRequiredSteps": (()=>getRequiredSteps),
    "getStepByIndex": (()=>getStepByIndex),
    "getStepByRoute": (()=>getStepByRoute),
    "getStepIndex": (()=>getStepIndex),
    "getTotalSteps": (()=>getTotalSteps),
    "setLicenseTypeUUIDToCodeMap": (()=>setLicenseTypeUUIDToCodeMap)
});
// Base steps that can be used across license types
const BASE_STEPS = {
    applicantInfo: {
        id: 'applicant-info',
        name: 'Applicant Information',
        component: 'ApplicantInfo',
        route: 'applicant-info',
        required: true,
        description: 'Personal or company information of the applicant',
        estimatedTime: '5'
    },
    companyProfile: {
        id: 'company-profile',
        name: 'Company Profile',
        component: 'CompanyProfile',
        route: 'company-profile',
        required: true,
        description: 'Company structure, shareholders, and directors',
        estimatedTime: '10'
    },
    management: {
        id: 'management',
        name: 'Management Structure',
        component: 'Management',
        route: 'management',
        required: false,
        description: 'Management team and organizational structure',
        estimatedTime: '8'
    },
    professionalServices: {
        id: 'professional-services',
        name: 'Professional Services',
        component: 'ProfessionalServices',
        route: 'professional-services',
        required: false,
        description: 'External consultants and service providers',
        estimatedTime: '6'
    },
    businessInfo: {
        id: 'business-info',
        name: 'Business Information',
        component: 'BusinessInfo',
        route: 'business-info',
        required: true,
        description: 'Business description and operational plan',
        estimatedTime: '7'
    },
    serviceScope: {
        id: 'service-scope',
        name: 'Service Scope',
        component: 'ServiceScope',
        route: 'service-scope',
        required: true,
        description: 'Services offered and geographic coverage',
        estimatedTime: '8'
    },
    businessPlan: {
        id: 'business-plan',
        name: 'Business Plan',
        component: 'BusinessPlan',
        route: 'business-plan',
        required: true,
        description: 'Market analysis and financial projections',
        estimatedTime: '15'
    },
    legalHistory: {
        id: 'legal-history',
        name: 'Legal History',
        component: 'LegalHistory',
        route: 'legal-history',
        required: true,
        description: 'Legal compliance and regulatory history',
        estimatedTime: '5'
    },
    reviewSubmit: {
        id: 'review-submit',
        name: 'Review & Submit',
        component: 'ReviewSubmit',
        route: 'review-submit',
        required: true,
        description: 'Review all information and submit application',
        estimatedTime: '10'
    }
};
const LICENSE_TYPE_STEP_CONFIGS = {
    telecommunications: {
        licenseTypeId: 'telecommunications',
        name: 'Telecommunications License',
        description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.businessInfo,
            BASE_STEPS.serviceScope,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '74 minutes',
        requirements: [
            'Business registration certificate',
            'Tax compliance certificate',
            'Technical specifications',
            'Financial statements',
            'Management CVs',
            'Network coverage plans'
        ]
    },
    postal_services: {
        licenseTypeId: 'postal_services',
        name: 'Postal Services License',
        description: 'License for postal and courier service providers',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.businessInfo,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '42 minutes',
        requirements: [
            'Business registration certificate',
            'Fleet inventory',
            'Service coverage map',
            'Insurance certificates',
            'Premises documentation'
        ]
    },
    standards_compliance: {
        licenseTypeId: 'standards_compliance',
        name: 'Standards Compliance License',
        description: 'License for standards compliance and certification services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.businessInfo,
            BASE_STEPS.serviceScope,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '59 minutes',
        requirements: [
            'Accreditation certificates',
            'Technical competency proof',
            'Quality management system',
            'Laboratory facilities documentation',
            'Staff qualifications'
        ]
    },
    broadcasting: {
        licenseTypeId: 'broadcasting',
        name: 'Broadcasting License',
        description: 'License for radio and television broadcasting services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.businessInfo,
            BASE_STEPS.serviceScope,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '63 minutes',
        requirements: [
            'Broadcasting equipment specifications',
            'Content programming plan',
            'Studio facility documentation',
            'Transmission coverage maps',
            'Local content compliance plan'
        ]
    },
    spectrum_management: {
        licenseTypeId: 'spectrum_management',
        name: 'Spectrum Management License',
        description: 'License for radio frequency spectrum management and allocation',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.businessInfo,
            BASE_STEPS.serviceScope,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '74 minutes',
        requirements: [
            'Spectrum usage plan',
            'Technical interference analysis',
            'Equipment type approval',
            'Frequency coordination agreements',
            'Monitoring capabilities documentation'
        ]
    },
    clf: {
        licenseTypeId: 'clf',
        name: 'CLF License',
        description: 'Consumer Lending and Finance license',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.businessInfo,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '50 minutes',
        requirements: [
            'Financial institution license',
            'Capital adequacy documentation',
            'Risk management framework',
            'Consumer protection policies',
            'Anti-money laundering procedures'
        ]
    }
};
// License type name to config key mapping
const LICENSE_TYPE_NAME_MAPPING = {
    'telecommunications': 'telecommunications',
    'postal services': 'postal_services',
    'postal_services': 'postal_services',
    'standards compliance': 'standards_compliance',
    'standards_compliance': 'standards_compliance',
    'broadcasting': 'broadcasting',
    'spectrum management': 'spectrum_management',
    'spectrum_management': 'spectrum_management',
    'clf': 'clf',
    'consumer lending and finance': 'clf'
};
const getLicenseTypeStepConfig = (licenseTypeId)=>{
    console.log('getLicenseTypeStepConfig called with:', licenseTypeId);
    console.log('Available configs:', Object.keys(LICENSE_TYPE_STEP_CONFIGS));
    console.log('UUID to code map:', licenseTypeUUIDToCodeMap);
    // First try direct lookup
    let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];
    if (config) {
        console.log('Found config via direct lookup:', config.name);
        return config;
    }
    // Try normalized lookup
    const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');
    config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];
    if (config) {
        console.log('Found config via normalized lookup:', config.name);
        return config;
    }
    // Try name mapping
    const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];
    if (mappedKey) {
        console.log('Found config via name mapping:', mappedKey);
        return LICENSE_TYPE_STEP_CONFIGS[mappedKey];
    }
    // If licenseTypeId looks like a UUID, try to get the code from license types
    if (isUUID(licenseTypeId)) {
        console.log('Detected UUID, trying to get code...');
        const code = getLicenseTypeCodeFromUUID(licenseTypeId);
        console.log('Got code from UUID:', code);
        if (code) {
            const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];
            if (foundConfig) {
                console.log('Found config via UUID mapping:', foundConfig.name);
                return foundConfig;
            }
        }
    }
    console.log('No config found for license type:', licenseTypeId);
    return null;
};
// Helper function to check if a string is a UUID
const isUUID = (str)=>{
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
};
// Helper function to get license type code from UUID
// This will be populated by the license type service
let licenseTypeUUIDToCodeMap = {};
const setLicenseTypeUUIDToCodeMap = (map)=>{
    licenseTypeUUIDToCodeMap = map;
};
const getLicenseTypeCodeFromUUID = (uuid)=>{
    return licenseTypeUUIDToCodeMap[uuid] || null;
};
const getStepByRoute = (licenseTypeId, stepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return null;
    return config.steps.find((step)=>step.route === stepRoute) || null;
};
const getStepByIndex = (licenseTypeId, stepIndex)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config || stepIndex < 0 || stepIndex >= config.steps.length) return null;
    return config.steps[stepIndex];
};
const getStepIndex = (licenseTypeId, stepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return -1;
    return config.steps.findIndex((step)=>step.route === stepRoute);
};
const getTotalSteps = (licenseTypeId)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config ? config.steps.length : 0;
};
const getRequiredSteps = (licenseTypeId)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config ? config.steps.filter((step)=>step.required) : [];
};
const getOptionalSteps = (licenseTypeId)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config ? config.steps.filter((step)=>!step.required) : [];
};
const calculateProgress = (licenseTypeId, completedSteps)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return 0;
    const totalSteps = config.steps.length;
    const completed = completedSteps.length;
    return Math.round(completed / totalSteps * 100);
};
const getNextStep = (licenseTypeId, currentStepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return null;
    const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);
    if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;
    return config.steps[currentIndex + 1];
};
const getPreviousStep = (licenseTypeId, currentStepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return null;
    const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);
    if (currentIndex <= 0) return null;
    return config.steps[currentIndex - 1];
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/hooks/useLicenseData.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "useLicenseCategories": (()=>useLicenseCategories),
    "useLicenseData": (()=>useLicenseData),
    "useLicenseTypes": (()=>useLicenseTypes),
    "usePostalCourierLicenses": (()=>usePostalCourierLicenses)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseTypeService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseCategoryService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/licenseTypeStepConfig.ts [app-client] (ecmascript)");
var _s = __turbopack_context__.k.signature(), _s1 = __turbopack_context__.k.signature(), _s2 = __turbopack_context__.k.signature(), _s3 = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const useLicenseTypes = ()=>{
    _s();
    const [licenseTypes, setLicenseTypes] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const fetchLicenseTypes = async ()=>{
        setLoading(true);
        setError(null);
        try {
            const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseTypeService"].getAllLicenseTypes();
            setLicenseTypes(data);
            // Create UUID to code mapping for step configuration
            const uuidToCodeMap = {};
            console.log('License types received:', data);
            data.forEach((licenseType)=>{
                console.log('Processing license type:', licenseType.name, 'code:', licenseType.code, 'id:', licenseType.license_type_id);
                if (licenseType.code) {
                    uuidToCodeMap[licenseType.license_type_id] = licenseType.code;
                }
            });
            console.log('Setting UUID to code map:', uuidToCodeMap);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["setLicenseTypeUUIDToCodeMap"])(uuidToCodeMap);
        } catch (err) {
            let errorMessage = 'Failed to fetch license types';
            // Handle rate limiting specifically
            if (err.response?.status === 429) {
                errorMessage = 'Too many requests. Please wait a moment and try again.';
                console.warn('Rate limit hit for license types, using cached data if available');
                // Try to use any cached data as fallback
                try {
                    const cachedData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseTypeService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseTypeService"].getAllLicenseTypes();
                    if (cachedData && cachedData.length > 0) {
                        setLicenseTypes(cachedData);
                        setError(null);
                        return;
                    }
                } catch (cacheErr) {
                    console.error('No cached data available:', cacheErr);
                }
            } else {
                errorMessage = err.response?.data?.message || errorMessage;
            }
            setError(errorMessage);
            console.error('Error fetching license types:', err);
        } finally{
            setLoading(false);
        }
    };
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useLicenseTypes.useEffect": ()=>{
            fetchLicenseTypes();
        }
    }["useLicenseTypes.useEffect"], []);
    return {
        licenseTypes,
        loading,
        error,
        refetch: fetchLicenseTypes
    };
};
_s(useLicenseTypes, "M8BJorG+QRqhcrtA8itw9oFC4NA=");
const useLicenseCategories = ()=>{
    _s1();
    const [categories, setCategories] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const fetchCategories = async ()=>{
        setLoading(true);
        setError(null);
        try {
            const data = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseCategoryService"].getAllLicenseCategories();
            setCategories(data);
        } catch (err) {
            let errorMessage = 'Failed to fetch license categories';
            // Handle rate limiting specifically
            if (err.response?.status === 429) {
                errorMessage = 'Too many requests. Please wait a moment and try again.';
                console.warn('Rate limit hit for license categories, using cached data if available');
                // Try to use any cached data as fallback
                try {
                    const cachedData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseCategoryService"].getAllLicenseCategories();
                    if (cachedData && cachedData.length > 0) {
                        setCategories(cachedData);
                        setError(null);
                        return;
                    }
                } catch (cacheErr) {
                    console.error('No cached data available:', cacheErr);
                }
            } else {
                errorMessage = err.response?.data?.message || errorMessage;
            }
            setError(errorMessage);
            console.error('Error fetching license categories:', err);
        } finally{
            setLoading(false);
        }
    };
    const getCategoriesByType = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "useLicenseCategories.useCallback[getCategoriesByType]": (licenseTypeId)=>{
            return categories.filter({
                "useLicenseCategories.useCallback[getCategoriesByType]": (category)=>category.license_type_id === licenseTypeId
            }["useLicenseCategories.useCallback[getCategoriesByType]"]);
        }
    }["useLicenseCategories.useCallback[getCategoriesByType]"], [
        categories
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "useLicenseCategories.useEffect": ()=>{
            fetchCategories();
        }
    }["useLicenseCategories.useEffect"], []);
    return {
        categories,
        loading,
        error,
        refetch: fetchCategories,
        getCategoriesByType
    };
};
_s1(useLicenseCategories, "m79R7HUvtvvEObiQhFaxcqdRnbc=");
const useLicenseData = ()=>{
    _s2();
    const licenseTypesData = useLicenseTypes();
    const categoriesData = useLicenseCategories();
    const loading = licenseTypesData.loading || categoriesData.loading;
    const error = licenseTypesData.error || categoriesData.error;
    const getLicenseTypeWithCategories = (licenseTypeId)=>{
        const licenseType = licenseTypesData.licenseTypes.find((lt)=>lt.license_type_id === licenseTypeId);
        const categories = categoriesData.getCategoriesByType(licenseTypeId);
        return {
            licenseType,
            categories
        };
    };
    const refetch = ()=>{
        licenseTypesData.refetch();
        categoriesData.refetch();
    };
    return {
        licenseTypes: licenseTypesData.licenseTypes,
        categories: categoriesData.categories,
        loading,
        error,
        refetch,
        getLicenseTypeWithCategories,
        getCategoriesByType: categoriesData.getCategoriesByType
    };
};
_s2(useLicenseData, "SjtxgcVsYOs65XsBRywfa7JcqL4=", false, function() {
    return [
        useLicenseTypes,
        useLicenseCategories
    ];
});
const usePostalCourierLicenses = ()=>{
    _s3();
    const { licenseTypes, categories, loading, error, refetch } = useLicenseData();
    // Find postal/courier license types
    const postalCourierTypes = licenseTypes.filter((lt)=>lt.name.toLowerCase().includes('postal') || lt.name.toLowerCase().includes('courier') || lt.name.toLowerCase().includes('mail'));
    // Get categories for postal/courier license types
    const postalCourierCategories = categories.filter((cat)=>postalCourierTypes.some((lt)=>lt.license_type_id === cat.license_type_id));
    return {
        licenseTypes: postalCourierTypes,
        categories: postalCourierCategories,
        loading,
        error,
        refetch
    };
};
_s3(usePostalCourierLicenses, "STznB7uirtk3EzE1+ZsL0AnsNHA=", false, function() {
    return [
        useLicenseData
    ];
});
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/customer/applications/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/CustomerLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useLicenseData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/hooks/useLicenseData.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
// Helper function to get icon and styling based on license type name
const getLicenseTypeIcon = (name)=>{
    const nameLower = name.toLowerCase();
    if (nameLower.includes('postal') || nameLower.includes('courier') || nameLower.includes('mail')) {
        return {
            icon: 'ri-mail-send-line',
            iconBg: 'bg-blue-100',
            iconColor: 'text-blue-600',
            category: 'Postal'
        };
    } else if (nameLower.includes('telecom') || nameLower.includes('spectrum') || nameLower.includes('radio')) {
        return {
            icon: 'ri-signal-tower-line',
            iconBg: 'bg-indigo-100',
            iconColor: 'text-indigo-600',
            category: 'Telecommunications'
        };
    } else if (nameLower.includes('standard') || nameLower.includes('approval') || nameLower.includes('certificate')) {
        return {
            icon: 'ri-shield-check-line',
            iconBg: 'bg-emerald-100',
            iconColor: 'text-emerald-600',
            category: 'Standards'
        };
    } else if (nameLower.includes('clf') || nameLower.includes('converged') || nameLower.includes('framework')) {
        return {
            icon: 'ri-stack-line',
            iconBg: 'bg-purple-100',
            iconColor: 'text-purple-600',
            category: 'CLF'
        };
    } else if (nameLower.includes('internet') || nameLower.includes('isp') || nameLower.includes('network')) {
        return {
            icon: 'ri-wifi-line',
            iconBg: 'bg-green-100',
            iconColor: 'text-green-600',
            category: 'Internet'
        };
    } else if (nameLower.includes('broadcast') || nameLower.includes('media') || nameLower.includes('content')) {
        return {
            icon: 'ri-broadcast-line',
            iconBg: 'bg-orange-100',
            iconColor: 'text-orange-600',
            category: 'Broadcasting'
        };
    } else {
        return {
            icon: 'ri-file-text-line',
            iconBg: 'bg-gray-100',
            iconColor: 'text-gray-600',
            category: 'General'
        };
    }
};
const CustomerApplicationsPage = ()=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { licenseTypes, loading: licenseLoading, error: licenseError } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useLicenseData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLicenseTypes"])();
    const { loading: categoriesLoading, error: categoriesError, getCategoriesByType } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useLicenseData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLicenseCategories"])();
    const loading = licenseLoading || categoriesLoading;
    const error = licenseError || categoriesError;
    // Transform backend license types to display format
    const transformedLicenseTypes = licenseTypes.map((licenseType)=>{
        const iconData = getLicenseTypeIcon(licenseType.name);
        const relatedCategories = getCategoriesByType(licenseType.license_type_id);
        return {
            id: licenseType.license_type_id,
            name: licenseType.name,
            description: licenseType.description,
            validity: String(licenseType.validity),
            categories: relatedCategories.map((cat)=>({
                    id: cat.license_category_id,
                    name: cat.name,
                    description: cat.description,
                    fee: cat.fee
                })),
            ...iconData
        };
    });
    const handleApplyForLicense = (licenseType)=>{
        // Debug logging
        console.log('Navigating to license type:', {
            id: licenseType.id,
            name: licenseType.name,
            url: `/customer/applications/${licenseType.id}`
        });
        // Navigate to license category selection page for the selected license type
        router.push(`/customer/applications/${licenseType.id}`);
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center min-h-96",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "animate-spin rounded-full h-8 w-8 border-b-2 border-primary"
                }, void 0, false, {
                    fileName: "[project]/src/app/customer/applications/page.tsx",
                    lineNumber: 127,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/customer/applications/page.tsx",
                lineNumber: 126,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/customer/applications/page.tsx",
            lineNumber: 125,
            columnNumber: 7
        }, this);
    }
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center min-h-96",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "text-red-600 dark:text-red-400 mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-error-warning-line text-4xl"
                            }, void 0, false, {
                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                lineNumber: 139,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/page.tsx",
                            lineNumber: 138,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",
                            children: "Failed to load license types"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/page.tsx",
                            lineNumber: 141,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 dark:text-gray-400 mb-4",
                            children: error
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/page.tsx",
                            lineNumber: 144,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            type: "button",
                            onClick: ()=>window.location.reload(),
                            className: "bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors",
                            children: "Try Again"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/page.tsx",
                            lineNumber: 145,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/applications/page.tsx",
                    lineNumber: 137,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/customer/applications/page.tsx",
                lineNumber: 136,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/customer/applications/page.tsx",
            lineNumber: 135,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "min-h-screen flex flex-col",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                                            className: "text-2xl font-bold text-gray-900 dark:text-gray-100",
                                            children: "New License Applications"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/page.tsx",
                                            lineNumber: 166,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                                            children: "Choose a license type to start your application"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/page.tsx",
                                            lineNumber: 169,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/customer/applications/page.tsx",
                                    lineNumber: 165,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center space-x-4",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                        onClick: ()=>router.push('/customer/my-licenses'),
                                        className: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                className: "ri-file-list-line mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                lineNumber: 178,
                                                columnNumber: 19
                                            }, this),
                                            "My Applications"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/customer/applications/page.tsx",
                                        lineNumber: 174,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/applications/page.tsx",
                                    lineNumber: 173,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/customer/applications/page.tsx",
                            lineNumber: 164,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/customer/applications/page.tsx",
                        lineNumber: 163,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/customer/applications/page.tsx",
                    lineNumber: 162,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex-grow",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",
                            children: transformedLicenseTypes.map((license)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 transition-all duration-300 hover:-translate-y-1 hover:shadow-lg hover:border-primary group h-full",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "p-4 h-full flex flex-col",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center justify-between mb-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: `w-10 h-10 ${license.iconBg} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: `${license.icon} text-lg ${license.iconColor}`
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/customer/applications/page.tsx",
                                                            lineNumber: 200,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/customer/applications/page.tsx",
                                                        lineNumber: 199,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-xs font-medium text-gray-500 dark:text-gray-400 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-full",
                                                        children: license.category
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/customer/applications/page.tsx",
                                                        lineNumber: 202,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                lineNumber: 198,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                                className: "text-base font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-primary transition-colors duration-300 line-clamp-2",
                                                children: license.name
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                lineNumber: 208,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "space-y-1 mb-3",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between text-xs",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-gray-500 dark:text-gray-400",
                                                                children: "Validity:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                                lineNumber: 215,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-medium text-gray-900 dark:text-gray-100",
                                                                children: license.validity || 'N/A'
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                                lineNumber: 216,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/customer/applications/page.tsx",
                                                        lineNumber: 214,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "flex items-center justify-between text-xs",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "text-gray-500 dark:text-gray-400",
                                                                children: "Categories:"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                                lineNumber: 219,
                                                                columnNumber: 25
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                                className: "font-medium text-gray-900 dark:text-gray-100",
                                                                children: license.categories?.length || 0
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                                lineNumber: 220,
                                                                columnNumber: 25
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/customer/applications/page.tsx",
                                                        lineNumber: 218,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                lineNumber: 213,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "mb-3 flex-grow",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                    className: "text-xs text-gray-600 dark:text-gray-400 line-clamp-3",
                                                    children: license.description || 'No description available'
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/customer/applications/page.tsx",
                                                    lineNumber: 228,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                lineNumber: 227,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                type: "button",
                                                onClick: ()=>handleApplyForLicense(license),
                                                className: "w-full bg-primary text-white py-2 px-3 rounded-lg text-sm font-medium hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors duration-300",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "ri-file-add-line mr-1"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/customer/applications/page.tsx",
                                                        lineNumber: 239,
                                                        columnNumber: 23
                                                    }, this),
                                                    "Apply Now"
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/customer/applications/page.tsx",
                                                lineNumber: 234,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/customer/applications/page.tsx",
                                        lineNumber: 196,
                                        columnNumber: 19
                                    }, this)
                                }, license.id, false, {
                                    fileName: "[project]/src/app/customer/applications/page.tsx",
                                    lineNumber: 192,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/page.tsx",
                            lineNumber: 190,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/customer/applications/page.tsx",
                        lineNumber: 188,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/customer/applications/page.tsx",
                    lineNumber: 187,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("footer", {
                    className: "mt-8 bg-blue-50 dark:bg-blue-900/20 border-t border-blue-200 dark:border-blue-800",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-start",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-information-line text-blue-600 dark:text-blue-400 text-xl mr-3 mt-0.5"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/applications/page.tsx",
                                    lineNumber: 253,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "text-lg font-medium text-blue-900 dark:text-blue-100 mb-2",
                                            children: "Need Help with Your Application?"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/page.tsx",
                                            lineNumber: 255,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            className: "text-blue-800 dark:text-blue-200 mb-4",
                                            children: "Our team is here to assist you throughout the application process. Contact us for guidance on requirements, documentation, or any questions about the licensing process."
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/page.tsx",
                                            lineNumber: 258,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "flex flex-wrap gap-4",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                    href: "mailto:<EMAIL>",
                                                    className: "inline-flex items-center text-blue-700 dark:text-blue-300 hover:text-blue-900 dark:hover:text-blue-100 font-medium",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: "ri-mail-line mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/customer/applications/page.tsx",
                                                            lineNumber: 267,
                                                            columnNumber: 21
                                                        }, this),
                                                        "<EMAIL>"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/customer/applications/page.tsx",
                                                    lineNumber: 263,
                                                    columnNumber: 19
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("a", {
                                                    href: "tel:+265123456789",
                                                    className: "inline-flex items-center text-blue-700 dark:text-blue-300 hover:text-blue-900 dark:hover:text-blue-100 font-medium",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                            className: "ri-phone-line mr-2"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/customer/applications/page.tsx",
                                                            lineNumber: 274,
                                                            columnNumber: 21
                                                        }, this),
                                                        "+265 123 456 789"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/customer/applications/page.tsx",
                                                    lineNumber: 270,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/app/customer/applications/page.tsx",
                                            lineNumber: 262,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/customer/applications/page.tsx",
                                    lineNumber: 254,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/customer/applications/page.tsx",
                            lineNumber: 252,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/customer/applications/page.tsx",
                        lineNumber: 251,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/customer/applications/page.tsx",
                    lineNumber: 250,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/customer/applications/page.tsx",
            lineNumber: 160,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/customer/applications/page.tsx",
        lineNumber: 159,
        columnNumber: 5
    }, this);
};
_s(CustomerApplicationsPage, "M/zzinsR1qGFmvcQ6k2AMHZ13V8=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useLicenseData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLicenseTypes"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$hooks$2f$useLicenseData$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLicenseCategories"]
    ];
});
_c = CustomerApplicationsPage;
const __TURBOPACK__default__export__ = CustomerApplicationsPage;
var _c;
__turbopack_context__.k.register(_c, "CustomerApplicationsPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_4308dc74._.js.map