"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationFormData = void 0;
const typeorm_1 = require("typeorm");
const uuid_1 = require("uuid");
const class_validator_1 = require("class-validator");
const applications_entity_1 = require("./applications.entity");
const user_entity_1 = require("./user.entity");
let ApplicationFormData = class ApplicationFormData {
    form_data_id;
    application_id;
    section_name;
    section_data;
    completed;
    created_at;
    created_by;
    updated_at;
    updated_by;
    deleted_at;
    application;
    creator;
    updater;
    generateId() {
        if (!this.form_data_id) {
            this.form_data_id = (0, uuid_1.v4)();
        }
    }
};
exports.ApplicationFormData = ApplicationFormData;
__decorate([
    (0, typeorm_1.PrimaryGeneratedColumn)('uuid'),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ApplicationFormData.prototype, "form_data_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    (0, class_validator_1.IsUUID)(),
    __metadata("design:type", String)
], ApplicationFormData.prototype, "application_id", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'varchar', length: 100 }),
    (0, class_validator_1.IsString)(),
    __metadata("design:type", String)
], ApplicationFormData.prototype, "section_name", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'json' }),
    __metadata("design:type", Object)
], ApplicationFormData.prototype, "section_data", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'boolean', default: false }),
    (0, class_validator_1.IsBoolean)(),
    __metadata("design:type", Boolean)
], ApplicationFormData.prototype, "completed", void 0);
__decorate([
    (0, typeorm_1.CreateDateColumn)(),
    __metadata("design:type", Date)
], ApplicationFormData.prototype, "created_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid' }),
    __metadata("design:type", String)
], ApplicationFormData.prototype, "created_by", void 0);
__decorate([
    (0, typeorm_1.UpdateDateColumn)(),
    __metadata("design:type", Date)
], ApplicationFormData.prototype, "updated_at", void 0);
__decorate([
    (0, typeorm_1.Column)({ type: 'uuid', nullable: true }),
    (0, class_validator_1.IsOptional)(),
    __metadata("design:type", String)
], ApplicationFormData.prototype, "updated_by", void 0);
__decorate([
    (0, typeorm_1.DeleteDateColumn)(),
    __metadata("design:type", Date)
], ApplicationFormData.prototype, "deleted_at", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => applications_entity_1.Applications, { onDelete: 'CASCADE' }),
    (0, typeorm_1.JoinColumn)({ name: 'application_id' }),
    __metadata("design:type", applications_entity_1.Applications)
], ApplicationFormData.prototype, "application", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User),
    (0, typeorm_1.JoinColumn)({ name: 'created_by' }),
    __metadata("design:type", user_entity_1.User)
], ApplicationFormData.prototype, "creator", void 0);
__decorate([
    (0, typeorm_1.ManyToOne)(() => user_entity_1.User, { nullable: true }),
    (0, typeorm_1.JoinColumn)({ name: 'updated_by' }),
    __metadata("design:type", user_entity_1.User)
], ApplicationFormData.prototype, "updater", void 0);
__decorate([
    (0, typeorm_1.BeforeInsert)(),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], ApplicationFormData.prototype, "generateId", null);
exports.ApplicationFormData = ApplicationFormData = __decorate([
    (0, typeorm_1.Entity)('application_form_data'),
    (0, typeorm_1.Index)(['application_id', 'section_name'], { unique: true })
], ApplicationFormData);
//# sourceMappingURL=application-form-data.entity.js.map