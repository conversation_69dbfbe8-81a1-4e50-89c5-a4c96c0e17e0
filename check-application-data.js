// Script to check application data and form sections
// Usage: node check-application-data.js

const axios = require('axios');

const API_BASE_URL = 'http://localhost:3001'; // Adjust to your backend URL
const APPLICATION_ID = 'a6a29647-ba0f-482e-8e10-8dabf3902d43';

// You'll need to get a valid JWT token from your authentication system
const JWT_TOKEN = 'your-jwt-token-here';

const checkApplicationData = async () => {
  try {
    console.log(`Checking application: ${APPLICATION_ID}`);
    
    // 1. Check if application exists
    try {
      const appResponse = await axios.get(
        `${API_BASE_URL}/applications/${APPLICATION_ID}`,
        {
          headers: {
            'Authorization': `Bearer ${JWT_TOKEN}`,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('✅ Application exists:', appResponse.data);
    } catch (error) {
      console.log('❌ Application not found:', error.response?.data || error.message);
      return;
    }

    // 2. Check all form sections for this application
    try {
      const sectionsResponse = await axios.get(
        `${API_BASE_URL}/application-form-data/${APPLICATION_ID}`,
        {
          headers: {
            'Authorization': `Bearer ${JWT_TOKEN}`,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('📋 Existing form sections:', sectionsResponse.data);
    } catch (error) {
      console.log('📋 No form sections found:', error.response?.data || error.message);
    }

    // 3. Specifically check for applicantInfo section
    try {
      const applicantInfoResponse = await axios.get(
        `${API_BASE_URL}/application-form-data/${APPLICATION_ID}/applicantInfo`,
        {
          headers: {
            'Authorization': `Bearer ${JWT_TOKEN}`,
            'Content-Type': 'application/json'
          }
        }
      );
      console.log('✅ ApplicantInfo section exists:', applicantInfoResponse.data);
    } catch (error) {
      console.log('❌ ApplicantInfo section not found:', error.response?.data || error.message);
    }

  } catch (error) {
    console.error('Error checking application data:', error.message);
  }
};

checkApplicationData();
