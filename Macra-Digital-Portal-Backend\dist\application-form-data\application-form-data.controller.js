"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ApplicationFormDataController = void 0;
const common_1 = require("@nestjs/common");
const swagger_1 = require("@nestjs/swagger");
const application_form_data_service_1 = require("./application-form-data.service");
const create_application_form_data_dto_1 = require("../dto/application-form-data/create-application-form-data.dto");
const update_application_form_data_dto_1 = require("../dto/application-form-data/update-application-form-data.dto");
const application_form_data_entity_1 = require("../entities/application-form-data.entity");
const audit_trail_entity_1 = require("../entities/audit-trail.entity");
const jwt_auth_guard_1 = require("../auth/guards/jwt-auth.guard");
const audit_interceptor_1 = require("../common/interceptors/audit.interceptor");
let ApplicationFormDataController = class ApplicationFormDataController {
    applicationFormDataService;
    constructor(applicationFormDataService) {
        this.applicationFormDataService = applicationFormDataService;
    }
    async create(createApplicationFormDataDto, req) {
        const userId = req.user?.user_id || req.user?.id;
        return this.applicationFormDataService.upsert(createApplicationFormDataDto, userId);
    }
    async findByApplicationAndSection(applicationId, sectionName) {
        return this.applicationFormDataService.findByApplicationAndSection(applicationId, sectionName);
    }
    async findByApplication(applicationId) {
        return this.applicationFormDataService.findByApplicationId(applicationId);
    }
    async update(applicationId, sectionName, updateApplicationFormDataDto, req) {
        const userId = req.user?.user_id || req.user?.id;
        return this.applicationFormDataService.update(applicationId, sectionName, updateApplicationFormDataDto, userId);
    }
    async remove(applicationId, sectionName) {
        await this.applicationFormDataService.remove(applicationId, sectionName);
        return { message: 'Form section deleted successfully' };
    }
    async removeByApplication(applicationId) {
        await this.applicationFormDataService.removeByApplicationId(applicationId);
        return { message: 'All form sections deleted successfully' };
    }
};
exports.ApplicationFormDataController = ApplicationFormDataController;
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Post)(),
    (0, swagger_1.ApiOperation)({ summary: 'Create application form section data' }),
    (0, swagger_1.ApiBody)({ type: create_application_form_data_dto_1.CreateApplicationFormDataDto }),
    (0, swagger_1.ApiResponse)({
        status: 201,
        description: 'Form section data created successfully',
        type: application_form_data_entity_1.ApplicationFormData,
    }),
    (0, swagger_1.ApiResponse)({
        status: 409,
        description: 'Section already exists for this application',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.CREATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ApplicationFormData',
        description: 'Created application form section data',
    }),
    __param(0, (0, common_1.Body)()),
    __param(1, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_application_form_data_dto_1.CreateApplicationFormDataDto, Object]),
    __metadata("design:returntype", Promise)
], ApplicationFormDataController.prototype, "create", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Get)(':applicationId/:sectionName'),
    (0, swagger_1.ApiOperation)({ summary: 'Get specific form section for an application' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiParam)({ name: 'sectionName', description: 'Section name' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Form section retrieved successfully',
        type: application_form_data_entity_1.ApplicationFormData,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Form section not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ApplicationFormData',
        description: 'Viewed application form section',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('sectionName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ApplicationFormDataController.prototype, "findByApplicationAndSection", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Get)(':applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Get all form sections for an application' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Form sections retrieved successfully',
        type: [application_form_data_entity_1.ApplicationFormData],
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.VIEW,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ApplicationFormData',
        description: 'Viewed application form data',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationFormDataController.prototype, "findByApplication", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Put)(':applicationId/:sectionName'),
    (0, swagger_1.ApiOperation)({ summary: 'Update application form section data' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiParam)({ name: 'sectionName', description: 'Section name' }),
    (0, swagger_1.ApiBody)({ type: update_application_form_data_dto_1.UpdateApplicationFormDataDto }),
    (0, swagger_1.ApiResponse)({
        status: 200,
        description: 'Form section updated successfully',
        type: application_form_data_entity_1.ApplicationFormData,
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Form section not found',
    }),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.UPDATE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ApplicationFormData',
        description: 'Updated application form section data',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('sectionName')),
    __param(2, (0, common_1.Body)()),
    __param(3, (0, common_1.Request)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, update_application_form_data_dto_1.UpdateApplicationFormDataDto, Object]),
    __metadata("design:returntype", Promise)
], ApplicationFormDataController.prototype, "update", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Delete)(':applicationId/:sectionName'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete application form section data' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiParam)({ name: 'sectionName', description: 'Section name' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'Form section deleted successfully',
    }),
    (0, swagger_1.ApiResponse)({
        status: 404,
        description: 'Form section not found',
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ApplicationFormData',
        description: 'Deleted application form section data',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __param(1, (0, common_1.Param)('sectionName')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], ApplicationFormDataController.prototype, "remove", null);
__decorate([
    (0, swagger_1.ApiBearerAuth)('JWT-auth'),
    (0, common_1.Delete)(':applicationId'),
    (0, swagger_1.ApiOperation)({ summary: 'Delete all form sections for an application' }),
    (0, swagger_1.ApiParam)({ name: 'applicationId', description: 'Application UUID' }),
    (0, swagger_1.ApiResponse)({
        status: 204,
        description: 'All form sections deleted successfully',
    }),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    (0, audit_interceptor_1.Audit)({
        action: audit_trail_entity_1.AuditAction.DELETE,
        module: audit_trail_entity_1.AuditModule.LICENSE_MANAGEMENT,
        resourceType: 'ApplicationFormData',
        description: 'Deleted all application form data',
    }),
    __param(0, (0, common_1.Param)('applicationId', common_1.ParseUUIDPipe)),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], ApplicationFormDataController.prototype, "removeByApplication", null);
exports.ApplicationFormDataController = ApplicationFormDataController = __decorate([
    (0, swagger_1.ApiTags)('application-form-data'),
    (0, common_1.Controller)('application-form-data'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard),
    __metadata("design:paramtypes", [application_form_data_service_1.ApplicationFormDataService])
], ApplicationFormDataController);
//# sourceMappingURL=application-form-data.controller.js.map