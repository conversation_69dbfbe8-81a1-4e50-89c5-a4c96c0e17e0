{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,6LAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,6LAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,6LAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,6LAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,6LAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD;GAvJwB;;QAUG,kIAAA,CAAA,UAAO;QACjB,qIAAA,CAAA,YAAS;QACP,qIAAA,CAAA,cAAW;;;KAZN", "debugId": null}}, {"offset": {"line": 277, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Consumer Affairs',\r\n      href: '/customer/consumer-affairs',\r\n      icon: 'ri-shield-user-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/consumer-affairs',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/standards': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/consumer-affairs': 'Loading Consumer Affairs...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;AARA;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;mDAAE,IAAM;gBACpC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;oBACN,SAAS,aAAa;gBACxB;aACD;kDAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,6JAAA,CAAA,UAAO,AAAD;gDAAE,IAAM;gBACjC;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;gBACA;oBACE,MAAM;oBACN,MAAM;oBACN,MAAM;gBACR;aACD;+CAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM;0DAAgB;oBACpB,MAAM,gBAAgB;wBACpB;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;wBACA;qBACD;oBAED,cAAc,OAAO;kEAAC,CAAA;4BACpB,OAAO,QAAQ,CAAC;wBAClB;;gBACF;;YAEA,4DAA4D;YAC5D,MAAM,QAAQ,WAAW,eAAe;YACxC;4CAAO,IAAM,aAAa;;QAC5B;mCAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2DAAE;YACtC,uBAAuB,CAAC;QAC1B;0DAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC,MAAc;YAChD,MAAM,eAA0C;gBAC9C,aAAa;gBACb,sBAAsB;gBACtB,0BAA0B;gBAC1B,oCAAoC;gBACpC,sBAAsB;gBACtB,uBAAuB;gBACvB,yBAAyB;gBACzB,uBAAuB;gBACvB,8BAA8B;gBAC9B,kBAAkB;gBAClB,qBAAqB;gBACrB,sBAAsB;YACxB;YAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;YAC1D,WAAW;YACX,uBAAuB;QACzB;qDAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;sDAAE,CAAC;YAClC,2CAA2C;YAC3C,OAAO,QAAQ,CAAC;QAClB;qDAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;0DAAE;YACrC,sBAAsB,CAAC;QACzB;yDAAG;QAAC;KAAmB;IAEvB,qBACE,6LAAC;QAAI,WAAU;;YAEZ,qCACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,6LAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,6LAAC,+JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,6LAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,6LAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,6LAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,6LAAC,+JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,gIAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,6LAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,6LAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,6LAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,6LAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,6LAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,6LAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,6LAAC,+JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,6LAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,6LAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,6LAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6LAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,6LAAC,gIAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;;0EACb,6LAAC,+JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,6LAAC,qIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAlVM;;QAGa,qIAAA,CAAA,cAAW;QACb,qIAAA,CAAA,YAAS;QACC,kIAAA,CAAA,UAAO;QACT,qIAAA,CAAA,aAAU;;;KAN7B;uCAoVS", "debugId": null}}, {"offset": {"line": 924, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    console.log('ApplicationService.createApplication called with:', data);\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ApplicationService.createApplication error:', error);\r\n      console.error('Error response:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return response.data;\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      console.log('Creating application with data:', data);\r\n\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      console.log('Creating application with:', {\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id,\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft'\r\n      });\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 14 // ~1/7 steps completed\r\n      });\r\n\r\n      console.log('Application created:', application);\r\n\r\n      // Save applicant form data separately if needed\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        await applicationFormDataService.saveFormSection(\r\n          application.application_id,\r\n          'applicantInfo',\r\n          data.applicant_data\r\n        );\r\n        console.log('Applicant form data saved');\r\n      } catch (formDataError) {\r\n        console.warn('Could not save form data separately, continuing...', formDataError);\r\n        // Continue even if form data saving fails\r\n      }\r\n\r\n      console.log('Application created successfully');\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n\r\n        // Save section data using form data service\r\n        await applicationFormDataService.saveOrUpdateFormSection(\r\n          applicationId,\r\n          sectionName,\r\n          sectionData\r\n        );\r\n\r\n        // Get all form data to calculate progress\r\n        const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n        completedSections = Object.keys(allFormData).length;\r\n\r\n        console.log(`Form data saved using form data service`);\r\n      } catch (formDataError) {\r\n        console.warn('Form data service not available, using basic progress tracking:', formDataError);\r\n\r\n        // Fallback: estimate progress based on section name\r\n        const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n        const sectionIndex = sectionOrder.indexOf(sectionName);\r\n        completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n      }\r\n\r\n      // Calculate progress based on completed sections\r\n      const totalSections = 7; // Total number of form sections\r\n      const progressPercentage = Math.round((completedSections / totalSections) * 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Get all applications - the backend should filter by authenticated user\r\n      const response = await apiClient.get('/applications');\r\n\r\n      console.log('Applications API response:', response.data);\r\n\r\n      // Handle different response structures\r\n      let applications = [];\r\n      if (response.data?.data) {\r\n        applications = Array.isArray(response.data.data) ? response.data.data : [];\r\n      } else if (Array.isArray(response.data)) {\r\n        applications = response.data;\r\n      } else if (response.data) {\r\n        // Single application or other structure\r\n        applications = [response.data];\r\n      }\r\n\r\n      console.log('Processed applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get form data from the form data service\r\n      let formData: Record<string, any> = {};\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        formData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n      } catch (formDataError) {\r\n        console.warn('Could not retrieve form data for validation:', formDataError);\r\n        // Continue with empty form data\r\n      }\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,QAAQ,GAAG,CAAC,qDAAqD;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ,KAAK,CAAC,mBAAoB,OAAe,UAAU;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;YACV;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,GAAG,uBAAuB;YACjD;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,gDAAgD;YAChD,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,MAAM,2BAA2B,eAAe,CAC9C,YAAY,cAAc,EAC1B,iBACA,KAAK,cAAc;gBAErB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,sDAAsD;YACnE,0CAA0C;YAC5C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBAEvC,4CAA4C;gBAC5C,MAAM,2BAA2B,uBAAuB,CACtD,eACA,aACA;gBAGF,0CAA0C;gBAC1C,MAAM,cAAc,MAAM,2BAA2B,sBAAsB,CAAC;gBAC5E,oBAAoB,OAAO,IAAI,CAAC,aAAa,MAAM;gBAEnD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,mEAAmE;gBAEhF,oDAAoD;gBACpD,MAAM,eAAe;oBAAC;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAgB;oBAAgB;iBAAe;gBACxI,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAC7D;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,GAAG,gCAAgC;YACzD,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB;YAE5E,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,SAAS,IAAI;YAChE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YAEvD,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,SAAS,IAAI,EAAE,MAAM;gBACvB,eAAe,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,EAAE;YAC5E,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACvC,eAAe,SAAS,IAAI;YAC9B,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,wCAAwC;gBACxC,eAAe;oBAAC,SAAS,IAAI;iBAAC;YAChC;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,0HAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,2CAA2C;YAC3C,IAAI,WAAgC,CAAC;YACrC,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,WAAW,MAAM,2BAA2B,sBAAsB,CAAC;YACrE,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,gDAAgD;YAC7D,gCAAgC;YAClC;YAEA,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1223, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/my-licenses/page.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useCallback } from 'react';\r\nimport { useRouter, useSearchParams } from 'next/navigation';\r\nimport CustomerLayout from '@/components/customer/CustomerLayout';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { applicationService } from '@/services/applicationService';\r\n\r\n// Types\r\ninterface Application {\r\n  application_id: string;\r\n  application_number: string;\r\n  status: string;\r\n  progress_percentage: number;\r\n  current_step: number;\r\n  submitted_at?: string;\r\n  created_at: string;\r\n  license_category_id: string;\r\n  license_category?: {\r\n    license_category_id: string;\r\n    name: string;\r\n    description: string;\r\n    license_type_id: string;\r\n    license_type?: {\r\n      license_type_id: string;\r\n      name: string;\r\n      description?: string;\r\n    };\r\n  };\r\n}\r\n\r\n\r\n// Status Modal Component\r\ninterface StatusModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  application: Application | null;\r\n  onContinueApplication?: (application: Application) => void;\r\n  canContinueApplication?: (application: Application) => boolean;\r\n}\r\n\r\nconst StatusModal: React.FC<StatusModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  application,\r\n  onContinueApplication,\r\n  canContinueApplication\r\n}) => {\r\n  if (!isOpen || !application) return null;\r\n\r\n  const steps = [\r\n    { id: 1, name: 'Draft', description: 'Application incomplete', icon: 'ri-file-text-line' },\r\n    { id: 2, name: 'Submitted', description: 'Application received and logged', icon: 'ri-file-text-line' },\r\n    { id: 3, name: 'Under Review', description: 'Being reviewed by MACRA team', icon: 'ri-search-line' },\r\n    { id: 4, name: 'Evaluation', description: 'Technical evaluation in progress', icon: 'ri-clipboard-line' },\r\n    { id: 5, name: 'Approved', description: 'License approved and issued', icon: 'ri-check-line' }\r\n  ];\r\n\r\n  const getStepStatus = (stepId: number) => {\r\n    const currentStep = application.current_step;\r\n    if (application.status === 'rejected') return stepId === 1 ? 'complete' : 'error';\r\n    if (application.status === 'approved') return 'complete';\r\n    return stepId <= currentStep ? (stepId === currentStep ? 'current' : 'complete') : 'upcoming';\r\n  };\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 overflow-y-auto\">\r\n      <div className=\"flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0\">\r\n        {/* Background overlay */}\r\n        <div\r\n          className=\"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity\"\r\n          onClick={onClose}\r\n        ></div>\r\n\r\n        {/* Modal panel */}\r\n        <div className=\"inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full\">\r\n          <div className=\"bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4\">\r\n            {/* Modal header */}\r\n            <div className=\"flex items-center justify-between mb-4\">\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n                Application Status\r\n              </h3>\r\n              <button\r\n                type=\"button\"\r\n                title=\"Close modal\"\r\n                onClick={onClose}\r\n                className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\r\n              >\r\n                <i className=\"ri-close-line text-xl\"></i>\r\n              </button>\r\n            </div>\r\n\r\n            {/* Application info */}\r\n            <div className=\"mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg\">\r\n              <h4 className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                {application.application_number}\r\n              </h4>\r\n              <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                {application.license_category?.name || 'License Category'}\r\n              </p>\r\n            </div>\r\n\r\n            {/* Status tracker */}\r\n            <div className=\"relative mb-6\">\r\n              {/* Progress line */}\r\n              <div className=\"absolute top-6 left-6 right-6 h-0.5 bg-gray-200 dark:bg-gray-600\">\r\n                <div\r\n                  className={`h-full bg-primary transition-all duration-500 ${\r\n                    application.status === 'approved' ? 'w-full' :\r\n                    application.current_step === 4 ? 'w-full' :\r\n                    application.current_step === 3 ? 'w-2/3' :\r\n                    application.current_step === 2 ? 'w-1/3' :\r\n                    application.current_step === 1 ? 'w-0' : 'w-0'\r\n                  }`}\r\n                />\r\n              </div>\r\n\r\n              {/* Steps */}\r\n              <div className=\"relative flex justify-between\">\r\n                {steps.map((step) => {\r\n                  const stepStatus = getStepStatus(step.id);\r\n                  return (\r\n                    <div key={step.id} className=\"flex flex-col items-center\">\r\n                      <div className={`\r\n                        w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300\r\n                        ${stepStatus === 'complete' ? 'bg-primary border-primary text-white' :\r\n                          stepStatus === 'current' ? 'bg-primary border-primary text-white animate-pulse' :\r\n                          stepStatus === 'error' ? 'bg-red-500 border-red-500 text-white' :\r\n                          'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400'}\r\n                      `}>\r\n                        <i className={step.icon}></i>\r\n                      </div>\r\n                      <div className=\"mt-3 text-center\">\r\n                        <div className={`text-sm font-medium ${\r\n                          stepStatus === 'complete' || stepStatus === 'current' ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'\r\n                        }`}>\r\n                          {step.name}\r\n                        </div>\r\n                        <div className=\"text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-20\">\r\n                          {step.description}\r\n                        </div>\r\n                      </div>\r\n                    </div>\r\n                  );\r\n                })}\r\n              </div>\r\n            </div>\r\n\r\n            {/* Status details */}\r\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\r\n              <div className=\"grid grid-cols-2 gap-4 text-sm\">\r\n                <div>\r\n                  <p className=\"text-gray-500 dark:text-gray-400\">Current Status</p>\r\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                    {application.status.replace('_', ' ').toUpperCase()}\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-gray-500 dark:text-gray-400\">Progress</p>\r\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                    {application.progress_percentage || 0}% Complete\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-gray-500 dark:text-gray-400\">Submitted</p>\r\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                    {application.submitted_at\r\n                      ? new Date(application.submitted_at).toLocaleDateString('en-US', {\r\n                          year: 'numeric',\r\n                          month: 'long',\r\n                          day: 'numeric'\r\n                        })\r\n                      : new Date(application.created_at).toLocaleDateString('en-US', {\r\n                          year: 'numeric',\r\n                          month: 'long',\r\n                          day: 'numeric'\r\n                        })\r\n                    }\r\n                  </p>\r\n                </div>\r\n                <div>\r\n                  <p className=\"text-gray-500 dark:text-gray-400\">Estimated Time</p>\r\n                  <p className=\"font-medium text-gray-900 dark:text-gray-100\">\r\n                    30-45 business days\r\n                  </p>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n\r\n          {/* Modal footer */}\r\n          <div className=\"bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={onClose}\r\n              className=\"w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm\"\r\n            >\r\n              Close\r\n            </button>\r\n\r\n            {/* Continue Application Button */}\r\n            {application && canContinueApplication && onContinueApplication && canContinueApplication(application) && (\r\n              <button\r\n                type=\"button\"\r\n                onClick={() => {\r\n                  onContinueApplication(application);\r\n                  onClose();\r\n                }}\r\n                className=\"w-full inline-flex justify-center rounded-md border border-blue-300 shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mr-3 sm:w-auto sm:text-sm\"\r\n              >\r\n                <i className=\"ri-edit-line mr-2\"></i>\r\n                Continue Application\r\n              </button>\r\n            )}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nconst MyLicensesPage: React.FC = () => {\r\n  const { isAuthenticated} = useAuth();\r\n  const router = useRouter();\r\n  const searchParams = useSearchParams();\r\n  const [applications, setApplications] = useState<Application[]>([]);\r\n  const [loading, setLoading] = useState<boolean>(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [activeFilter, setActiveFilter] = useState<string>('all');\r\n  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);\r\n  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);\r\n  const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);\r\n\r\n  // Check for success message from URL params\r\n  useEffect(() => {\r\n    if (searchParams.get('submitted') === 'true') {\r\n      setShowSuccessMessage(true);\r\n      // Remove the parameter from URL\r\n      const newUrl = new URL(window.location.href);\r\n      newUrl.searchParams.delete('submitted');\r\n      window.history.replaceState({}, '', newUrl.toString());\r\n\r\n      // Hide success message after 5 seconds\r\n      setTimeout(() => setShowSuccessMessage(false), 5000);\r\n    }\r\n  }, [searchParams]);\r\n\r\n  const breadcrumbs = [\r\n    { label: 'Dashboard', href: '/customer' },\r\n    { label: 'My Licenses', href: '/customer/my-licenses' }\r\n  ];\r\n\r\n  // Modal functions\r\n  const openStatusModal = (application: Application) => {\r\n    setSelectedApplication(application);\r\n    setIsModalOpen(true);\r\n  };\r\n\r\n  const closeStatusModal = () => {\r\n    setSelectedApplication(null);\r\n    setIsModalOpen(false);\r\n  };\r\n\r\n  // Function to continue working on an application\r\n  const handleContinueApplication = async (application: Application) => {\r\n    console.log('Continuing application:', application);\r\n\r\n    // Clear any existing errors\r\n    setError(null);\r\n\r\n    // Get the license type ID from the license category\r\n    const licenseTypeId = application.license_category?.license_type_id ||\r\n                         application.license_category?.license_type?.license_type_id;\r\n\r\n    console.log('License type ID found:', licenseTypeId);\r\n    console.log('License category data:', application.license_category);\r\n\r\n    if (!licenseTypeId) {\r\n      console.error('License type ID not found in application data:', application);\r\n      setError('Unable to continue application: License type information is missing. Please contact support.');\r\n      return;\r\n    }\r\n\r\n    try {\r\n      // Import step validation service dynamically\r\n      const { stepValidationService } = await import('@/services/stepValidationService');\r\n      const { getLicenseTypeStepConfig, getStepByIndex } = await import('@/config/licenseTypeStepConfig');\r\n\r\n      // Get license configuration to determine steps\r\n      const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\r\n      if (!licenseConfig) {\r\n        console.error('No license configuration found for license type:', licenseTypeId);\r\n        setError('Unable to continue application: License type configuration not found.');\r\n        return;\r\n      }\r\n\r\n      // Determine the appropriate step to continue from\r\n      let targetStepRoute: string;\r\n\r\n      try {\r\n        // Try to get the next available step based on current progress\r\n        const nextAvailableStepId = await stepValidationService.getNextAvailableStep(\r\n          application.application_id,\r\n          licenseTypeId\r\n        );\r\n\r\n        if (nextAvailableStepId) {\r\n          // Find the step configuration for this step ID\r\n          const stepConfig = licenseConfig.steps.find(step => step.id === nextAvailableStepId);\r\n          if (stepConfig) {\r\n            targetStepRoute = stepConfig.route;\r\n            console.log('Found next available step:', nextAvailableStepId, 'route:', targetStepRoute);\r\n          } else {\r\n            throw new Error('Step configuration not found for step ID: ' + nextAvailableStepId);\r\n          }\r\n        } else {\r\n          // Fallback: use current step from application\r\n          const currentStepIndex = Math.max(0, (application.current_step || 1) - 1); // Convert 1-based to 0-based\r\n          const currentStepConfig = getStepByIndex(licenseTypeId, currentStepIndex);\r\n\r\n          if (currentStepConfig) {\r\n            targetStepRoute = currentStepConfig.route;\r\n            console.log('Using current step from application:', application.current_step, 'route:', targetStepRoute);\r\n          } else {\r\n            // Final fallback: use first step\r\n            targetStepRoute = licenseConfig.steps[0].route;\r\n            console.log('Fallback to first step:', targetStepRoute);\r\n          }\r\n        }\r\n      } catch (stepError) {\r\n        console.warn('Error determining next step, falling back to current step:', stepError);\r\n\r\n        // Fallback: use current step from application\r\n        const currentStepIndex = Math.max(0, (application.current_step || 1) - 1); // Convert 1-based to 0-based\r\n        const currentStepConfig = getStepByIndex(licenseTypeId, currentStepIndex);\r\n\r\n        if (currentStepConfig) {\r\n          targetStepRoute = currentStepConfig.route;\r\n          console.log('Fallback: using current step from application:', application.current_step, 'route:', targetStepRoute);\r\n        } else {\r\n          // Final fallback: use first step\r\n          targetStepRoute = licenseConfig.steps[0].route;\r\n          console.log('Final fallback to first step:', targetStepRoute);\r\n        }\r\n      }\r\n\r\n      // Build the continue URL\r\n      const continueUrl = `/customer/applications/apply/${licenseTypeId}/${application.license_category_id}/${targetStepRoute}?app=${application.application_id}`;\r\n\r\n      console.log('Continue URL:', continueUrl);\r\n      console.log('Application data:', {\r\n        applicationId: application.application_id,\r\n        licenseTypeId,\r\n        licenseCategoryId: application.license_category_id,\r\n        currentStep: application.current_step,\r\n        targetStep: targetStepRoute\r\n      });\r\n\r\n      router.push(continueUrl);\r\n    } catch (error) {\r\n      console.error('Error determining continue step:', error);\r\n      setError('Unable to continue application: Error determining next step. Please try again.');\r\n    }\r\n  };\r\n\r\n  // Check if application can be continued (not submitted and incomplete)\r\n  const canContinueApplication = (application: Application) => {\r\n    // Only allow continuation if:\r\n    // 1. Progress is less than 100% (not completed)\r\n    // 2. Status is not 'submitted', 'approved', or 'rejected'\r\n    // 3. Application is still in draft/progress state\r\n    const completionRate = application.progress_percentage || 0;\r\n    const excludedStatuses = ['submitted', 'approved', 'rejected', 'under_review', 'evaluation'];\r\n\r\n    return completionRate < 100 && !excludedStatuses.includes(application.status);\r\n  };\r\n\r\n  // Function to fetch user applications\r\n  const fetchUserApplications = useCallback(async () => {\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      const applicationsData = await applicationService.getUserApplications();\r\n      console.log('User applications response:', applicationsData);\r\n\r\n      // Ensure we have an array\r\n      const applications = Array.isArray(applicationsData) ? applicationsData : [];\r\n\r\n      // Fix empty status values and add default values\r\n      const processedApplications = applications.map((app: any) => ({\r\n        ...app,\r\n        status: app.status || 'draft', // Default empty status to 'draft'\r\n        progress_percentage: app.progress_percentage || 0,\r\n        current_step: app.current_step || 1,\r\n        application_number: app.application_number || `APP-${app.application_id?.slice(0, 8)}`,\r\n        license_category: app.license_category ? {\r\n          ...app.license_category, // Preserve all license_category properties including license_type_id\r\n          name: app.license_category.name || 'License Category',\r\n          description: app.license_category.description || 'Category description'\r\n        } : {\r\n          name: 'License Category',\r\n          description: 'Category description'\r\n        }\r\n      }));\r\n\r\n      console.log('Processed applications:', processedApplications);\r\n\r\n      // Log status distribution for debugging\r\n      if (processedApplications.length > 0) {\r\n        const statusCounts = processedApplications.reduce((acc: Record<string, number>, app: Application) => {\r\n          acc[app.status] = (acc[app.status] || 0) + 1;\r\n          return acc;\r\n        }, {});\r\n        console.log('Status distribution:', statusCounts);\r\n      }\r\n\r\n      setApplications(processedApplications);\r\n\r\n    } catch (err: unknown) {\r\n      console.error('Applications fetch error:', err);\r\n      const error = err as { response?: { status?: number; data?: { message?: string } }; message?: string };\r\n\r\n      if (error.response?.status === 404) {\r\n        // No applications found - this is okay\r\n        setApplications([]);\r\n        setError(null);\r\n      } else if (error.response?.status === 401) {\r\n        setError('Authentication required. Please log in again.');\r\n        router.push('/customer/auth/login');\r\n        return;\r\n      } else {\r\n        setError(error.response?.data?.message || error.message || 'Failed to fetch applications');\r\n      }\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  }, [router]);\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    if (!isAuthenticated) {\r\n      router.push('/customer/auth/login');\r\n      return;\r\n    }\r\n\r\n    if (isAuthenticated) {\r\n      // Add a small delay to avoid rate limiting\r\n      const timer = setTimeout(() => {\r\n        fetchUserApplications(); // Load user applications\r\n      }, 500);\r\n\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [isAuthenticated, router, fetchUserApplications]);\r\n\r\n  // Show loading state\r\n  if (loading) {\r\n    return (\r\n      <CustomerLayout breadcrumbs={breadcrumbs}>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto\"></div>\r\n            <p className=\"mt-4 text-gray-600\">Loading your licenses...</p>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  // Show error state\r\n  if (error) {\r\n    return (\r\n      <CustomerLayout breadcrumbs={breadcrumbs}>\r\n        <div className=\"flex items-center justify-center min-h-96\">\r\n          <div className=\"text-center\">\r\n            <div className=\"text-red-600 dark:text-red-400 mb-4\">\r\n              <i className=\"ri-error-warning-line text-4xl\"></i>\r\n            </div>\r\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n              Failed to load applications\r\n            </h3>\r\n            <p className=\"text-gray-600 dark:text-gray-400 mb-4\">{error}</p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={() => {\r\n                // Add delay to avoid rate limiting\r\n                setTimeout(() => fetchUserApplications(), 500);\r\n              }}\r\n              className=\"bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors\"\r\n            >\r\n              Try Again\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </CustomerLayout>\r\n    );\r\n  }\r\n\r\n  // Client-side filtering with proper debugging\r\n  const filteredApplications = applications.filter((app: Application) => {\r\n    if (activeFilter === 'all') return true;\r\n\r\n    // Special handling for \"in_progress\" filter\r\n    if (activeFilter === 'in_progress') {\r\n      return canContinueApplication(app);\r\n    }\r\n\r\n    const matches = app.status === activeFilter;\r\n\r\n    // Debug logging for submitted filter\r\n    if (activeFilter === 'submitted') {\r\n      console.log(`App ${app.application_number}: status=\"${app.status}\", matches=${matches}`);\r\n    }\r\n\r\n    return matches;\r\n  });\r\n\r\n  // Debug: Log filter results\r\n  console.log(`Active filter: ${activeFilter}, Total apps: ${applications.length}, Filtered: ${filteredApplications.length}`);\r\n\r\n  // Debug: Show all unique statuses in the data\r\n  const uniqueStatuses = [...new Set(applications.map(app => app.status))];\r\n  console.log('Unique statuses in data:', uniqueStatuses);\r\n\r\n  const getStatusBadge = (status: string): React.ReactElement => {\r\n    const statusConfig = {\r\n      'draft': { color: 'bg-blue-100 text-blue-800', label: 'Draft' },\r\n      'submitted': { color: 'bg-blue-100 text-blue-800', label: 'Submitted' },\r\n      'under_review': { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review' },\r\n      'evaluation': { color: 'bg-purple-100 text-purple-800', label: 'Evaluation' },\r\n      'approved': { color: 'bg-green-100 text-green-800', label: 'Approved' },\r\n      'rejected': { color: 'bg-red-100 text-red-800', label: 'Rejected' }\r\n    };\r\n\r\n    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.submitted;\r\n    return (\r\n      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${config.color}`}>\r\n        {config.label}\r\n      </span>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <CustomerLayout breadcrumbs={breadcrumbs}>\r\n      <div className=\"max-w-7xl mx-auto\">\r\n        {/* Page header */}\r\n        <div className=\"mb-8\">\r\n          <div>\r\n            <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">My Licenses</h1>\r\n            <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\r\n              Track your license applications and manage your approved licenses.\r\n            </p>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Success Message */}\r\n        {showSuccessMessage && (\r\n          <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6\">\r\n            <div className=\"flex items-center\">\r\n              <i className=\"ri-check-circle-line text-green-600 dark:text-green-400 text-xl mr-3\"></i>\r\n              <div>\r\n                <h3 className=\"text-sm font-medium text-green-800 dark:text-green-200\">\r\n                  Application Submitted Successfully!\r\n                </h3>\r\n                <p className=\"text-sm text-green-700 dark:text-green-300 mt-1\">\r\n                  Your license application has been submitted and is now under review. You will receive email notifications about status updates.\r\n                </p>\r\n              </div>\r\n              <button\r\n                onClick={() => setShowSuccessMessage(false)}\r\n                className=\"ml-auto text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200\"\r\n              >\r\n                <i className=\"ri-close-line text-lg\"></i>\r\n              </button>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Filter tabs */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8\">\r\n          <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4\">Filter Applications</h2>\r\n          <div className=\"flex flex-wrap gap-2\">\r\n            {[\r\n              { key: 'all', label: 'All Applications' },\r\n              { key: 'in_progress', label: 'In Progress' },\r\n              { key: 'submitted', label: 'Submitted' },\r\n              { key: 'under_review', label: 'Under Review' },\r\n              { key: 'evaluation', label: 'Evaluation' },\r\n              { key: 'approved', label: 'Approved' },\r\n              { key: 'rejected', label: 'Rejected' },\r\n              { key: 'draft', label: 'Draft' }\r\n            ].map(filter => (\r\n              <button\r\n                key={filter.key}\r\n                type=\"button\"\r\n                onClick={() => setActiveFilter(filter.key)}\r\n                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${\r\n                  activeFilter === filter.key\r\n                    ? 'bg-primary text-white'\r\n                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'\r\n                }`}\r\n              >\r\n                {filter.label}\r\n              </button>\r\n            ))}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Applications table */}\r\n        <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700\">\r\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\r\n            <h2 className=\"text-lg font-semibold text-gray-900 dark:text-gray-100\">Applications Overview</h2>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n              View and track all your license applications\r\n            </p>\r\n          </div>\r\n\r\n          {filteredApplications.length === 0 ? (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"text-gray-400 dark:text-gray-500 mb-4\">\r\n                <i className=\"ri-file-list-line text-4xl\"></i>\r\n              </div>\r\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\r\n                No applications found\r\n              </h3>\r\n              <p className=\"text-gray-600 dark:text-gray-400\">\r\n                {activeFilter === 'all'\r\n                  ? \"You haven't submitted any license applications yet.\"\r\n                  : `No applications with status \"${activeFilter.replace('_', ' ')}\" found.`\r\n                }\r\n              </p>\r\n            </div>\r\n          ) : (\r\n            <div className=\"overflow-x-auto\">\r\n              <table className=\"min-w-full divide-y divide-gray-200 dark:divide-gray-700\">\r\n                <thead className=\"bg-gray-50 dark:bg-gray-700\">\r\n                  <tr>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Application Details\r\n                    </th>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      License Category\r\n                    </th>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Status\r\n                    </th>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Progress\r\n                    </th>\r\n                    <th scope=\"col\" className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                      Submitted\r\n                    </th>\r\n                    <th scope=\"col\" className=\"relative px-6 py-3\">\r\n                      <span className=\"sr-only\">Actions</span>\r\n                    </th>\r\n                  </tr>\r\n                </thead>\r\n                <tbody className=\"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700\">\r\n                  {filteredApplications.map((application) => (\r\n                    <tr key={application.application_id} className=\"hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors\">\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3\">\r\n                            <i className=\"ri-file-text-line text-blue-600 dark:text-blue-400\"></i>\r\n                          </div>\r\n                          <div>\r\n                            <div className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\r\n                              {application.application_number}\r\n                            </div>\r\n                            <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                              Application ID: {application.application_id.slice(0, 8)}...\r\n                            </div>\r\n                          </div>\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                          {application.license_category?.name || 'License Category'}\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                          {application.license_category?.description || 'Category description'}\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        {getStatusBadge(application.status)}\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"flex items-center\">\r\n                          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 relative overflow-hidden\">\r\n                            <div\r\n                              className={`${\r\n                                canContinueApplication(application) ? 'bg-blue-500' : 'bg-primary'\r\n                              } h-2 rounded-full transition-all duration-300 absolute top-0 left-0 ${\r\n                                (application.progress_percentage || 0) >= 100 ? 'w-full' :\r\n                                (application.progress_percentage || 0) >= 75 ? 'w-3/4' :\r\n                                (application.progress_percentage || 0) >= 50 ? 'w-1/2' :\r\n                                (application.progress_percentage || 0) >= 25 ? 'w-1/4' :\r\n                                (application.progress_percentage || 0) > 0 ? 'w-1/12' : 'w-0'\r\n                              }`}\r\n                            />\r\n                          </div>\r\n                          <div className=\"flex flex-col\">\r\n                            <span className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n                              {application.progress_percentage || 0}%\r\n                            </span>\r\n                            {canContinueApplication(application) && (\r\n                              <span className=\"text-xs text-blue-600 dark:text-blue-400 font-medium\">\r\n                                In Progress\r\n                              </span>\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap\">\r\n                        <div className=\"text-sm text-gray-900 dark:text-gray-100\">\r\n                          {application.submitted_at\r\n                            ? new Date(application.submitted_at).toLocaleDateString()\r\n                            : new Date(application.created_at).toLocaleDateString()\r\n                          }\r\n                        </div>\r\n                        <div className=\"text-sm text-gray-500 dark:text-gray-400\">\r\n                          Step {application.current_step} of 6\r\n                        </div>\r\n                      </td>\r\n                      <td className=\"px-6 py-4 whitespace-nowrap text-right text-sm font-medium\">\r\n                        <div className=\"flex items-center justify-end space-x-2\">\r\n                          {/* Continue Application Button - for in-progress applications */}\r\n                          {canContinueApplication(application) && (\r\n                            <button\r\n                              type=\"button\"\r\n                              title=\"Continue working on this application\"\r\n                              onClick={() => handleContinueApplication(application)}\r\n                              className=\"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full hover:bg-blue-200 transition-colors\"\r\n                            >\r\n                              <i className=\"ri-edit-line\"></i>\r\n                              View Continue\r\n                            </button>\r\n                          )}\r\n\r\n                          {/* View Status Button */}\r\n                          <button\r\n                            type=\"button\"\r\n                            title=\"View application status\"\r\n                            onClick={() => openStatusModal(application)}\r\n                            className=\"text-primary hover:text-red-700 transition-colors\"\r\n                          >\r\n                            <i className=\"ri-eye-line\"></i> View\r\n                          </button>\r\n\r\n                          {/* Download License Button - for approved applications */}\r\n                          {application.status === 'approved' && (\r\n                            <button\r\n                              type=\"button\"\r\n                              title=\"Download license\"\r\n                              className=\"text-primary hover:text-red-700 transition-colors\"\r\n                            >\r\n                              <i className=\"ri-download-line\"></i>\r\n                            </button>\r\n                          )}\r\n                        </div>\r\n                      </td>\r\n                    </tr>\r\n                  ))}\r\n                </tbody>\r\n              </table>\r\n            </div>\r\n          )}\r\n        </div>\r\n      </div>\r\n\r\n      {/* Status Modal */}\r\n      <StatusModal\r\n        isOpen={isModalOpen}\r\n        onClose={closeStatusModal}\r\n        application={selectedApplication}\r\n        onContinueApplication={handleContinueApplication}\r\n        canContinueApplication={canContinueApplication}\r\n      />\r\n    </CustomerLayout>\r\n  );\r\n};\r\n\r\nexport default MyLicensesPage;\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAyCA,MAAM,cAA0C,CAAC,EAC/C,MAAM,EACN,OAAO,EACP,WAAW,EACX,qBAAqB,EACrB,sBAAsB,EACvB;IACC,IAAI,CAAC,UAAU,CAAC,aAAa,OAAO;IAEpC,MAAM,QAAQ;QACZ;YAAE,IAAI;YAAG,MAAM;YAAS,aAAa;YAA0B,MAAM;QAAoB;QACzF;YAAE,IAAI;YAAG,MAAM;YAAa,aAAa;YAAmC,MAAM;QAAoB;QACtG;YAAE,IAAI;YAAG,MAAM;YAAgB,aAAa;YAAgC,MAAM;QAAiB;QACnG;YAAE,IAAI;YAAG,MAAM;YAAc,aAAa;YAAoC,MAAM;QAAoB;QACxG;YAAE,IAAI;YAAG,MAAM;YAAY,aAAa;YAA+B,MAAM;QAAgB;KAC9F;IAED,MAAM,gBAAgB,CAAC;QACrB,MAAM,cAAc,YAAY,YAAY;QAC5C,IAAI,YAAY,MAAM,KAAK,YAAY,OAAO,WAAW,IAAI,aAAa;QAC1E,IAAI,YAAY,MAAM,KAAK,YAAY,OAAO;QAC9C,OAAO,UAAU,cAAe,WAAW,cAAc,YAAY,aAAc;IACrF;IAEA,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBACC,WAAU;oBACV,SAAS;;;;;;8BAIX,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAuD;;;;;;sDAGrE,6LAAC;4CACC,MAAK;4CACL,OAAM;4CACN,SAAS;4CACT,WAAU;sDAEV,cAAA,6LAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;8CAKjB,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDACX,YAAY,kBAAkB;;;;;;sDAEjC,6LAAC;4CAAE,WAAU;sDACV,YAAY,gBAAgB,EAAE,QAAQ;;;;;;;;;;;;8CAK3C,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAW,CAAC,8CAA8C,EACxD,YAAY,MAAM,KAAK,aAAa,WACpC,YAAY,YAAY,KAAK,IAAI,WACjC,YAAY,YAAY,KAAK,IAAI,UACjC,YAAY,YAAY,KAAK,IAAI,UACjC,YAAY,YAAY,KAAK,IAAI,QAAQ,OACzC;;;;;;;;;;;sDAKN,6LAAC;4CAAI,WAAU;sDACZ,MAAM,GAAG,CAAC,CAAC;gDACV,MAAM,aAAa,cAAc,KAAK,EAAE;gDACxC,qBACE,6LAAC;oDAAkB,WAAU;;sEAC3B,6LAAC;4DAAI,WAAW,CAAC;;wBAEf,EAAE,eAAe,aAAa,yCAC5B,eAAe,YAAY,uDAC3B,eAAe,UAAU,yCACzB,+EAA+E;sBACnF,CAAC;sEACC,cAAA,6LAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;sEAEzB,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAW,CAAC,oBAAoB,EACnC,eAAe,cAAc,eAAe,YAAY,qCAAqC,oCAC7F;8EACC,KAAK,IAAI;;;;;;8EAEZ,6LAAC;oEAAI,WAAU;8EACZ,KAAK,WAAW;;;;;;;;;;;;;mDAjBb,KAAK,EAAE;;;;;4CAsBrB;;;;;;;;;;;;8CAKJ,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,6LAAC;wDAAE,WAAU;kEACV,YAAY,MAAM,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;;;;;;;;;;;;0DAGrD,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,6LAAC;wDAAE,WAAU;;4DACV,YAAY,mBAAmB,IAAI;4DAAE;;;;;;;;;;;;;0DAG1C,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,6LAAC;wDAAE,WAAU;kEACV,YAAY,YAAY,GACrB,IAAI,KAAK,YAAY,YAAY,EAAE,kBAAkB,CAAC,SAAS;4DAC7D,MAAM;4DACN,OAAO;4DACP,KAAK;wDACP,KACA,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB,CAAC,SAAS;4DAC3D,MAAM;4DACN,OAAO;4DACP,KAAK;wDACP;;;;;;;;;;;;0DAIR,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEAAmC;;;;;;kEAChD,6LAAC;wDAAE,WAAU;kEAA+C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASpE,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;gCAKA,eAAe,0BAA0B,yBAAyB,uBAAuB,8BACxF,6LAAC;oCACC,MAAK;oCACL,SAAS;wCACP,sBAAsB;wCACtB;oCACF;oCACA,WAAU;;sDAEV,6LAAC;4CAAE,WAAU;;;;;;wCAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrD;KAlLM;AAoLN,MAAM,iBAA2B;;IAC/B,MAAM,EAAE,eAAe,EAAC,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IACjC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,qIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAClE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsB;IACnF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IACxD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAW;IAEtE,4CAA4C;IAC5C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,aAAa,GAAG,CAAC,iBAAiB,QAAQ;gBAC5C,sBAAsB;gBACtB,gCAAgC;gBAChC,MAAM,SAAS,IAAI,IAAI,OAAO,QAAQ,CAAC,IAAI;gBAC3C,OAAO,YAAY,CAAC,MAAM,CAAC;gBAC3B,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI,OAAO,QAAQ;gBAEnD,uCAAuC;gBACvC;gDAAW,IAAM,sBAAsB;+CAAQ;YACjD;QACF;mCAAG;QAAC;KAAa;IAEjB,MAAM,cAAc;QAClB;YAAE,OAAO;YAAa,MAAM;QAAY;QACxC;YAAE,OAAO;YAAe,MAAM;QAAwB;KACvD;IAED,kBAAkB;IAClB,MAAM,kBAAkB,CAAC;QACvB,uBAAuB;QACvB,eAAe;IACjB;IAEA,MAAM,mBAAmB;QACvB,uBAAuB;QACvB,eAAe;IACjB;IAEA,iDAAiD;IACjD,MAAM,4BAA4B,OAAO;QACvC,QAAQ,GAAG,CAAC,2BAA2B;QAEvC,4BAA4B;QAC5B,SAAS;QAET,oDAAoD;QACpD,MAAM,gBAAgB,YAAY,gBAAgB,EAAE,mBAC/B,YAAY,gBAAgB,EAAE,cAAc;QAEjE,QAAQ,GAAG,CAAC,0BAA0B;QACtC,QAAQ,GAAG,CAAC,0BAA0B,YAAY,gBAAgB;QAElE,IAAI,CAAC,eAAe;YAClB,QAAQ,KAAK,CAAC,kDAAkD;YAChE,SAAS;YACT;QACF;QAEA,IAAI;YACF,6CAA6C;YAC7C,MAAM,EAAE,qBAAqB,EAAE,GAAG;YAClC,MAAM,EAAE,wBAAwB,EAAE,cAAc,EAAE,GAAG;YAErD,+CAA+C;YAC/C,MAAM,gBAAgB,yBAAyB;YAC/C,IAAI,CAAC,eAAe;gBAClB,QAAQ,KAAK,CAAC,oDAAoD;gBAClE,SAAS;gBACT;YACF;YAEA,kDAAkD;YAClD,IAAI;YAEJ,IAAI;gBACF,+DAA+D;gBAC/D,MAAM,sBAAsB,MAAM,sBAAsB,oBAAoB,CAC1E,YAAY,cAAc,EAC1B;gBAGF,IAAI,qBAAqB;oBACvB,+CAA+C;oBAC/C,MAAM,aAAa,cAAc,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;oBAChE,IAAI,YAAY;wBACd,kBAAkB,WAAW,KAAK;wBAClC,QAAQ,GAAG,CAAC,8BAA8B,qBAAqB,UAAU;oBAC3E,OAAO;wBACL,MAAM,IAAI,MAAM,+CAA+C;oBACjE;gBACF,OAAO;oBACL,8CAA8C;oBAC9C,MAAM,mBAAmB,KAAK,GAAG,CAAC,GAAG,CAAC,YAAY,YAAY,IAAI,CAAC,IAAI,IAAI,6BAA6B;oBACxG,MAAM,oBAAoB,eAAe,eAAe;oBAExD,IAAI,mBAAmB;wBACrB,kBAAkB,kBAAkB,KAAK;wBACzC,QAAQ,GAAG,CAAC,wCAAwC,YAAY,YAAY,EAAE,UAAU;oBAC1F,OAAO;wBACL,iCAAiC;wBACjC,kBAAkB,cAAc,KAAK,CAAC,EAAE,CAAC,KAAK;wBAC9C,QAAQ,GAAG,CAAC,2BAA2B;oBACzC;gBACF;YACF,EAAE,OAAO,WAAW;gBAClB,QAAQ,IAAI,CAAC,8DAA8D;gBAE3E,8CAA8C;gBAC9C,MAAM,mBAAmB,KAAK,GAAG,CAAC,GAAG,CAAC,YAAY,YAAY,IAAI,CAAC,IAAI,IAAI,6BAA6B;gBACxG,MAAM,oBAAoB,eAAe,eAAe;gBAExD,IAAI,mBAAmB;oBACrB,kBAAkB,kBAAkB,KAAK;oBACzC,QAAQ,GAAG,CAAC,kDAAkD,YAAY,YAAY,EAAE,UAAU;gBACpG,OAAO;oBACL,iCAAiC;oBACjC,kBAAkB,cAAc,KAAK,CAAC,EAAE,CAAC,KAAK;oBAC9C,QAAQ,GAAG,CAAC,iCAAiC;gBAC/C;YACF;YAEA,yBAAyB;YACzB,MAAM,cAAc,CAAC,6BAA6B,EAAE,cAAc,CAAC,EAAE,YAAY,mBAAmB,CAAC,CAAC,EAAE,gBAAgB,KAAK,EAAE,YAAY,cAAc,EAAE;YAE3J,QAAQ,GAAG,CAAC,iBAAiB;YAC7B,QAAQ,GAAG,CAAC,qBAAqB;gBAC/B,eAAe,YAAY,cAAc;gBACzC;gBACA,mBAAmB,YAAY,mBAAmB;gBAClD,aAAa,YAAY,YAAY;gBACrC,YAAY;YACd;YAEA,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;YAClD,SAAS;QACX;IACF;IAEA,uEAAuE;IACvE,MAAM,yBAAyB,CAAC;QAC9B,8BAA8B;QAC9B,gDAAgD;QAChD,0DAA0D;QAC1D,kDAAkD;QAClD,MAAM,iBAAiB,YAAY,mBAAmB,IAAI;QAC1D,MAAM,mBAAmB;YAAC;YAAa;YAAY;YAAY;YAAgB;SAAa;QAE5F,OAAO,iBAAiB,OAAO,CAAC,iBAAiB,QAAQ,CAAC,YAAY,MAAM;IAC9E;IAEA,sCAAsC;IACtC,MAAM,wBAAwB,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;6DAAE;YACxC,WAAW;YACX,SAAS;YAET,IAAI;gBACF,QAAQ,GAAG,CAAC;gBAEZ,MAAM,mBAAmB,MAAM,wIAAA,CAAA,qBAAkB,CAAC,mBAAmB;gBACrE,QAAQ,GAAG,CAAC,+BAA+B;gBAE3C,0BAA0B;gBAC1B,MAAM,eAAe,MAAM,OAAO,CAAC,oBAAoB,mBAAmB,EAAE;gBAE5E,iDAAiD;gBACjD,MAAM,wBAAwB,aAAa,GAAG;+FAAC,CAAC,MAAa,CAAC;4BAC5D,GAAG,GAAG;4BACN,QAAQ,IAAI,MAAM,IAAI;4BACtB,qBAAqB,IAAI,mBAAmB,IAAI;4BAChD,cAAc,IAAI,YAAY,IAAI;4BAClC,oBAAoB,IAAI,kBAAkB,IAAI,CAAC,IAAI,EAAE,IAAI,cAAc,EAAE,MAAM,GAAG,IAAI;4BACtF,kBAAkB,IAAI,gBAAgB,GAAG;gCACvC,GAAG,IAAI,gBAAgB;gCACvB,MAAM,IAAI,gBAAgB,CAAC,IAAI,IAAI;gCACnC,aAAa,IAAI,gBAAgB,CAAC,WAAW,IAAI;4BACnD,IAAI;gCACF,MAAM;gCACN,aAAa;4BACf;wBACF,CAAC;;gBAED,QAAQ,GAAG,CAAC,2BAA2B;gBAEvC,wCAAwC;gBACxC,IAAI,sBAAsB,MAAM,GAAG,GAAG;oBACpC,MAAM,eAAe,sBAAsB,MAAM;0FAAC,CAAC,KAA6B;4BAC9E,GAAG,CAAC,IAAI,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,MAAM,CAAC,IAAI,CAAC,IAAI;4BAC3C,OAAO;wBACT;yFAAG,CAAC;oBACJ,QAAQ,GAAG,CAAC,wBAAwB;gBACtC;gBAEA,gBAAgB;YAElB,EAAE,OAAO,KAAc;gBACrB,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,MAAM,QAAQ;gBAEd,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;oBAClC,uCAAuC;oBACvC,gBAAgB,EAAE;oBAClB,SAAS;gBACX,OAAO,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;oBACzC,SAAS;oBACT,OAAO,IAAI,CAAC;oBACZ;gBACF,OAAO;oBACL,SAAS,MAAM,QAAQ,EAAE,MAAM,WAAW,MAAM,OAAO,IAAI;gBAC7D;YACF,SAAU;gBACR,WAAW;YACb;QACF;4DAAG;QAAC;KAAO;IAIX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,CAAC,iBAAiB;gBACpB,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,iBAAiB;gBACnB,2CAA2C;gBAC3C,MAAM,QAAQ;sDAAW;wBACvB,yBAAyB,yBAAyB;oBACpD;qDAAG;gBAEH;gDAAO,IAAM,aAAa;;YAC5B;QACF;mCAAG;QAAC;QAAiB;QAAQ;KAAsB;IAEnD,qBAAqB;IACrB,IAAI,SAAS;QACX,qBACE,6LAAC,mJAAA,CAAA,UAAc;YAAC,aAAa;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;IAK5C;IAEA,mBAAmB;IACnB,IAAI,OAAO;QACT,qBACE,6LAAC,mJAAA,CAAA,UAAc;YAAC,aAAa;sBAC3B,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,6LAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,6LAAC;4BAAE,WAAU;sCAAyC;;;;;;sCACtD,6LAAC;4BACC,MAAK;4BACL,SAAS;gCACP,mCAAmC;gCACnC,WAAW,IAAM,yBAAyB;4BAC5C;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,8CAA8C;IAC9C,MAAM,uBAAuB,aAAa,MAAM,CAAC,CAAC;QAChD,IAAI,iBAAiB,OAAO,OAAO;QAEnC,4CAA4C;QAC5C,IAAI,iBAAiB,eAAe;YAClC,OAAO,uBAAuB;QAChC;QAEA,MAAM,UAAU,IAAI,MAAM,KAAK;QAE/B,qCAAqC;QACrC,IAAI,iBAAiB,aAAa;YAChC,QAAQ,GAAG,CAAC,CAAC,IAAI,EAAE,IAAI,kBAAkB,CAAC,UAAU,EAAE,IAAI,MAAM,CAAC,WAAW,EAAE,SAAS;QACzF;QAEA,OAAO;IACT;IAEA,4BAA4B;IAC5B,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,aAAa,cAAc,EAAE,aAAa,MAAM,CAAC,YAAY,EAAE,qBAAqB,MAAM,EAAE;IAE1H,8CAA8C;IAC9C,MAAM,iBAAiB;WAAI,IAAI,IAAI,aAAa,GAAG,CAAC,CAAA,MAAO,IAAI,MAAM;KAAG;IACxE,QAAQ,GAAG,CAAC,4BAA4B;IAExC,MAAM,iBAAiB,CAAC;QACtB,MAAM,eAAe;YACnB,SAAS;gBAAE,OAAO;gBAA6B,OAAO;YAAQ;YAC9D,aAAa;gBAAE,OAAO;gBAA6B,OAAO;YAAY;YACtE,gBAAgB;gBAAE,OAAO;gBAAiC,OAAO;YAAe;YAChF,cAAc;gBAAE,OAAO;gBAAiC,OAAO;YAAa;YAC5E,YAAY;gBAAE,OAAO;gBAA+B,OAAO;YAAW;YACtE,YAAY;gBAAE,OAAO;gBAA2B,OAAO;YAAW;QACpE;QAEA,MAAM,SAAS,YAAY,CAAC,OAAoC,IAAI,aAAa,SAAS;QAC1F,qBACE,6LAAC;YAAK,WAAW,CAAC,mEAAmE,EAAE,OAAO,KAAK,EAAE;sBAClG,OAAO,KAAK;;;;;;IAGnB;IAEA,qBACE,6LAAC,mJAAA,CAAA,UAAc;QAAC,aAAa;;0BAC3B,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAsD;;;;;;8CACpE,6LAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;;;;;;oBAOxD,oCACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;;;;;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAyD;;;;;;sDAGvE,6LAAC;4CAAE,WAAU;sDAAkD;;;;;;;;;;;;8CAIjE,6LAAC;oCACC,SAAS,IAAM,sBAAsB;oCACrC,WAAU;8CAEV,cAAA,6LAAC;wCAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;kCAOrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAC5E,6LAAC;gCAAI,WAAU;0CACZ;oCACC;wCAAE,KAAK;wCAAO,OAAO;oCAAmB;oCACxC;wCAAE,KAAK;wCAAe,OAAO;oCAAc;oCAC3C;wCAAE,KAAK;wCAAa,OAAO;oCAAY;oCACvC;wCAAE,KAAK;wCAAgB,OAAO;oCAAe;oCAC7C;wCAAE,KAAK;wCAAc,OAAO;oCAAa;oCACzC;wCAAE,KAAK;wCAAY,OAAO;oCAAW;oCACrC;wCAAE,KAAK;wCAAY,OAAO;oCAAW;oCACrC;wCAAE,KAAK;wCAAS,OAAO;oCAAQ;iCAChC,CAAC,GAAG,CAAC,CAAA,uBACJ,6LAAC;wCAEC,MAAK;wCACL,SAAS,IAAM,gBAAgB,OAAO,GAAG;wCACzC,WAAW,CAAC,2DAA2D,EACrE,iBAAiB,OAAO,GAAG,GACvB,0BACA,0GACJ;kDAED,OAAO,KAAK;uCATR,OAAO,GAAG;;;;;;;;;;;;;;;;kCAgBvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyD;;;;;;kDACvE,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;4BAK9D,qBAAqB,MAAM,KAAK,kBAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;;;;;;;;;;;kDAEf,6LAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,6LAAC;wCAAE,WAAU;kDACV,iBAAiB,QACd,wDACA,CAAC,6BAA6B,EAAE,aAAa,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC;;;;;;;;;;;qDAKhF,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,WAAU;;sDACf,6LAAC;4CAAM,WAAU;sDACf,cAAA,6LAAC;;kEACC,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEAAoG;;;;;;kEAG9H,6LAAC;wDAAG,OAAM;wDAAM,WAAU;kEACxB,cAAA,6LAAC;4DAAK,WAAU;sEAAU;;;;;;;;;;;;;;;;;;;;;;sDAIhC,6LAAC;4CAAM,WAAU;sDACd,qBAAqB,GAAG,CAAC,CAAC,4BACzB,6LAAC;oDAAoC,WAAU;;sEAC7C,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EAAE,WAAU;;;;;;;;;;;kFAEf,6LAAC;;0FACC,6LAAC;gFAAI,WAAU;0FACZ,YAAY,kBAAkB;;;;;;0FAEjC,6LAAC;gFAAI,WAAU;;oFAA2C;oFACvC,YAAY,cAAc,CAAC,KAAK,CAAC,GAAG;oFAAG;;;;;;;;;;;;;;;;;;;;;;;;sEAKhE,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;8EACZ,YAAY,gBAAgB,EAAE,QAAQ;;;;;;8EAEzC,6LAAC;oEAAI,WAAU;8EACZ,YAAY,gBAAgB,EAAE,eAAe;;;;;;;;;;;;sEAGlD,6LAAC;4DAAG,WAAU;sEACX,eAAe,YAAY,MAAM;;;;;;sEAEpC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EACC,WAAW,GACT,uBAAuB,eAAe,gBAAgB,aACvD,oEAAoE,EACnE,CAAC,YAAY,mBAAmB,IAAI,CAAC,KAAK,MAAM,WAChD,CAAC,YAAY,mBAAmB,IAAI,CAAC,KAAK,KAAK,UAC/C,CAAC,YAAY,mBAAmB,IAAI,CAAC,KAAK,KAAK,UAC/C,CAAC,YAAY,mBAAmB,IAAI,CAAC,KAAK,KAAK,UAC/C,CAAC,YAAY,mBAAmB,IAAI,CAAC,IAAI,IAAI,WAAW,OACxD;;;;;;;;;;;kFAGN,6LAAC;wEAAI,WAAU;;0FACb,6LAAC;gFAAK,WAAU;;oFACb,YAAY,mBAAmB,IAAI;oFAAE;;;;;;;4EAEvC,uBAAuB,8BACtB,6LAAC;gFAAK,WAAU;0FAAuD;;;;;;;;;;;;;;;;;;;;;;;sEAO/E,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;8EACZ,YAAY,YAAY,GACrB,IAAI,KAAK,YAAY,YAAY,EAAE,kBAAkB,KACrD,IAAI,KAAK,YAAY,UAAU,EAAE,kBAAkB;;;;;;8EAGzD,6LAAC;oEAAI,WAAU;;wEAA2C;wEAClD,YAAY,YAAY;wEAAC;;;;;;;;;;;;;sEAGnC,6LAAC;4DAAG,WAAU;sEACZ,cAAA,6LAAC;gEAAI,WAAU;;oEAEZ,uBAAuB,8BACtB,6LAAC;wEACC,MAAK;wEACL,OAAM;wEACN,SAAS,IAAM,0BAA0B;wEACzC,WAAU;;0FAEV,6LAAC;gFAAE,WAAU;;;;;;4EAAmB;;;;;;;kFAMpC,6LAAC;wEACC,MAAK;wEACL,OAAM;wEACN,SAAS,IAAM,gBAAgB;wEAC/B,WAAU;;0FAEV,6LAAC;gFAAE,WAAU;;;;;;4EAAkB;;;;;;;oEAIhC,YAAY,MAAM,KAAK,4BACtB,6LAAC;wEACC,MAAK;wEACL,OAAM;wEACN,WAAU;kFAEV,cAAA,6LAAC;4EAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;mDAjGd,YAAY,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgHjD,6LAAC;gBACC,QAAQ;gBACR,SAAS;gBACT,aAAa;gBACb,uBAAuB;gBACvB,wBAAwB;;;;;;;;;;;;AAIhC;GAnjBM;;QACuB,kIAAA,CAAA,UAAO;QACnB,qIAAA,CAAA,YAAS;QACH,qIAAA,CAAA,kBAAe;;;MAHhC;uCAqjBS", "debugId": null}}]}