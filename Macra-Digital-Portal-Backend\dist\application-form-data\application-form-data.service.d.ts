import { Repository } from 'typeorm';
import { ApplicationFormData } from '../entities/application-form-data.entity';
import { CreateApplicationFormDataDto } from '../dto/application-form-data/create-application-form-data.dto';
import { UpdateApplicationFormDataDto } from '../dto/application-form-data/update-application-form-data.dto';
export declare class ApplicationFormDataService {
    private readonly applicationFormDataRepository;
    constructor(applicationFormDataRepository: Repository<ApplicationFormData>);
    create(createApplicationFormDataDto: CreateApplicationFormDataDto, userId: string): Promise<ApplicationFormData>;
    findByApplicationId(applicationId: string): Promise<ApplicationFormData[]>;
    findByApplicationAndSection(applicationId: string, sectionName: string): Promise<ApplicationFormData>;
    findByApplicationAndSectionOptional(applicationId: string, sectionName: string): Promise<ApplicationFormData | null>;
    update(applicationId: string, sectionName: string, updateApplicationFormDataDto: UpdateApplicationFormDataDto, userId: string): Promise<ApplicationFormData>;
    upsert(createApplicationFormDataDto: CreateApplicationFormDataDto, userId: string): Promise<ApplicationFormData>;
    remove(applicationId: string, sectionName: string): Promise<void>;
    removeByApplicationId(applicationId: string): Promise<void>;
}
