"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientSystemsService = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const typeorm_2 = require("typeorm");
const client_systems_entity_1 = require("../entities/client-systems.entity");
const nestjs_paginate_1 = require("nestjs-paginate");
let ClientSystemsService = class ClientSystemsService {
    clientSystemsRepository;
    constructor(clientSystemsRepository) {
        this.clientSystemsRepository = clientSystemsRepository;
    }
    async create(createClientSystemDto, userId) {
        const existingSystem = await this.clientSystemsRepository.findOne({
            where: { system_code: createClientSystemDto.system_code },
        });
        if (existingSystem) {
            throw new common_1.ConflictException(`Client system with code '${createClientSystemDto.system_code}' already exists`);
        }
        const clientSystem = this.clientSystemsRepository.create({
            ...createClientSystemDto,
            created_by: userId,
        });
        return await this.clientSystemsRepository.save(clientSystem);
    }
    async findAll(query) {
        const queryBuilder = this.clientSystemsRepository
            .createQueryBuilder('client_systems')
            .leftJoinAndSelect('client_systems.creator', 'creator')
            .leftJoinAndSelect('client_systems.updater', 'updater')
            .orderBy('client_systems.created_at', 'DESC');
        return (0, nestjs_paginate_1.paginate)(query, queryBuilder, {
            sortableColumns: [
                'name',
                'system_code',
                'system_type',
                'status',
                'created_at',
                'updated_at',
            ],
            searchableColumns: [
                'name',
                'system_code',
                'description',
                'organization',
                'contact_email',
            ],
            defaultSortBy: [['created_at', 'DESC']],
            filterableColumns: {
                system_type: true,
                status: true,
                organization: true,
            },
        });
    }
    async findOne(id) {
        const clientSystem = await this.clientSystemsRepository.findOne({
            where: { client_system_id: id },
            relations: ['creator', 'updater'],
        });
        if (!clientSystem) {
            throw new common_1.NotFoundException(`Client system with ID '${id}' not found`);
        }
        return clientSystem;
    }
    async findBySystemCode(systemCode) {
        const clientSystem = await this.clientSystemsRepository.findOne({
            where: { system_code: systemCode },
            relations: ['creator', 'updater'],
        });
        if (!clientSystem) {
            throw new common_1.NotFoundException(`Client system with code '${systemCode}' not found`);
        }
        return clientSystem;
    }
    async update(id, updateClientSystemDto, userId) {
        const clientSystem = await this.findOne(id);
        if (updateClientSystemDto.system_code &&
            updateClientSystemDto.system_code !== clientSystem.system_code) {
            const existingSystem = await this.clientSystemsRepository.findOne({
                where: { system_code: updateClientSystemDto.system_code },
            });
            if (existingSystem) {
                throw new common_1.ConflictException(`Client system with code '${updateClientSystemDto.system_code}' already exists`);
            }
        }
        Object.assign(clientSystem, {
            ...updateClientSystemDto,
            updated_by: userId,
        });
        return await this.clientSystemsRepository.save(clientSystem);
    }
    async remove(id) {
        const clientSystem = await this.findOne(id);
        await this.clientSystemsRepository.softDelete(id);
    }
    async updateLastAccessed(id) {
        await this.clientSystemsRepository.update(id, {
            last_accessed_at: new Date(),
        });
    }
    async getSystemStats() {
        const [total, active, inactive, maintenance, deprecated] = await Promise.all([
            this.clientSystemsRepository.count(),
            this.clientSystemsRepository.count({ where: { status: client_systems_entity_1.ClientSystemStatus.ACTIVE } }),
            this.clientSystemsRepository.count({ where: { status: client_systems_entity_1.ClientSystemStatus.INACTIVE } }),
            this.clientSystemsRepository.count({ where: { status: client_systems_entity_1.ClientSystemStatus.MAINTENANCE } }),
            this.clientSystemsRepository.count({ where: { status: client_systems_entity_1.ClientSystemStatus.DEPRECATED } }),
        ]);
        const typeStats = await this.clientSystemsRepository
            .createQueryBuilder('client_systems')
            .select('client_systems.system_type', 'type')
            .addSelect('COUNT(*)', 'count')
            .groupBy('client_systems.system_type')
            .getRawMany();
        const byType = typeStats.reduce((acc, stat) => {
            acc[stat.type] = parseInt(stat.count);
            return acc;
        }, {});
        return {
            total,
            active,
            inactive,
            maintenance,
            deprecated,
            byType,
        };
    }
};
exports.ClientSystemsService = ClientSystemsService;
exports.ClientSystemsService = ClientSystemsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, typeorm_1.InjectRepository)(client_systems_entity_1.ClientSystems)),
    __metadata("design:paramtypes", [typeorm_2.Repository])
], ClientSystemsService);
//# sourceMappingURL=client-systems.service.js.map