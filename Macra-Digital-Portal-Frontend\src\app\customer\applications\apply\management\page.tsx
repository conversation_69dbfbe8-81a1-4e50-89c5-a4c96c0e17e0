'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { CustomerApiService } from '@/lib/customer-api';
import { TextInput, TextArea, Select } from '@/components/forms';
import { applicationService } from '@/services/applicationService';
import { applicationProgressService } from '@/services/applicationProgressService';
import { LicenseType } from '@/services/licenseTypeService';
import { getLicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';

// Stakeholder data structure matching backend entity
interface StakeholderData {
  stakeholder_id?: string;
  first_name: string;
  last_name: string;
  middle_name?: string;
  nationality: string;
  position: 'CEO' | 'SHAREHOLDER' | 'AUDITOR' | 'LAWYER';
  profile: string;
  contact_id?: string;
  cv_document_id?: string;
}

const ManagementPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State management
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});

  // License data
  const [licenseType, setLicenseType] = useState<LicenseType | null>(null);

  // Form data state
  const [formData, setFormData] = useState({
    stakeholders: [] as StakeholderData[],
    organizational_structure: '',
    key_personnel: '',
    management_experience: '',
    leadership_approach: '',
    succession_planning: ''
  });

  // Form handling functions
  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  // Stakeholder management functions
  const addStakeholder = () => {
    const newStakeholder: StakeholderData = {
      first_name: '',
      last_name: '',
      middle_name: '',
      nationality: '',
      position: 'CEO',
      profile: ''
    };
    const updatedStakeholders = [...formData.stakeholders, newStakeholder];
    handleFormChange('stakeholders', updatedStakeholders);
  };

  const updateStakeholder = (index: number, field: keyof StakeholderData, value: string) => {
    const updatedStakeholders = formData.stakeholders.map((stakeholder, i) =>
      i === index ? { ...stakeholder, [field]: value } : stakeholder
    );
    handleFormChange('stakeholders', updatedStakeholders);
  };

  const removeStakeholder = (index: number) => {
    const updatedStakeholders = formData.stakeholders.filter((_, i) => i !== index);
    handleFormChange('stakeholders', updatedStakeholders);
  };

  // Load data on component mount
  useEffect(() => {
    const loadData = async () => {
      if (!licenseCategoryId) {
        setError('License category ID is required');
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        // Load license category and type data
        const categoryData = await CustomerApiService.getLicenseCategory(licenseCategoryId);
        if (categoryData && categoryData.license_type) {
          setLicenseType(categoryData.license_type);
        }

        // Load existing application data if editing
        if (applicationId) {
          // TODO: Load existing management/stakeholder data
          // For now, initialize with empty stakeholder if none exist
          if (formData.stakeholders.length === 0) {
            addStakeholder();
          }
        } else {
          // Initialize with one stakeholder for new applications
          addStakeholder();
        }

      } catch (err) {
        console.error('Error loading data:', err);
        setError('Failed to load form data');
      } finally {
        setIsLoading(false);
      }
    };

    if (!authLoading) {
      if (!isAuthenticated) {
        router.push('/auth/login');
        return;
      }
      loadData();
    }
  }, [licenseCategoryId, applicationId, authLoading, isAuthenticated, router]);

  // Save form data
  const saveFormData = async (): Promise<boolean> => {
    setIsSaving(true);
    try {
      // Basic validation - check required fields
      const errors: Record<string, string> = {};

      if (formData.stakeholders.length === 0) {
        errors.stakeholders = 'At least one stakeholder is required';
      } else {
        formData.stakeholders.forEach((stakeholder, index) => {
          if (!stakeholder.first_name.trim()) errors[`stakeholder_${index}_first_name`] = 'First name is required';
          if (!stakeholder.last_name.trim()) errors[`stakeholder_${index}_last_name`] = 'Last name is required';
          if (!stakeholder.nationality.trim()) errors[`stakeholder_${index}_nationality`] = 'Nationality is required';
          if (!stakeholder.profile.trim()) errors[`stakeholder_${index}_profile`] = 'Profile is required';
        });
      }

      if (!formData.organizational_structure.trim()) errors.organizational_structure = 'Organizational structure is required';

      if (Object.keys(errors).length > 0) {
        setValidationErrors(errors);
        setIsSaving(false);
        return false;
      }

      // Save management data to application
      // Note: This is a placeholder - actual implementation may need a different approach
      await applicationService.updateApplication(applicationId, {
        // Save as JSON in a text field or use a separate service
      });

      // Mark step as completed
      await applicationProgressService.markStepCompleted(applicationId, 'management');

      setHasUnsavedChanges(false);
      setValidationErrors({});
      return true;

    } catch (error) {
      console.error('Error saving management data:', error);
      setValidationErrors({ save: 'Failed to save management data. Please try again.' });
      return false;
    } finally {
      setIsSaving(false);
    }
  };



  // Handle next button click
  const handleNext = async () => {
    const saved = await saveFormData();
    if (saved && licenseType && licenseType.code) {
      const config = getLicenseTypeStepConfig(licenseType.code);
      const currentStepIndex = config?.steps.findIndex(step => step.id === 'management') || 0;
      const nextStep = config?.steps[currentStepIndex + 1];

      if (nextStep) {
        router.push(`/customer/applications/apply/${nextStep.route}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
      }
    }
  };

  // Handle previous button click
  const handlePrevious = () => {
    if (licenseType && licenseType.code) {
      const config = getLicenseTypeStepConfig(licenseType.code);
      const currentStepIndex = config?.steps.findIndex(step => step.id === 'management') || 0;
      const previousStep = config?.steps[currentStepIndex - 1];

      if (previousStep) {
        router.push(`/customer/applications/apply/${previousStep.route}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
      }
    }
  };

  if (authLoading || isLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading management form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (error) {
    return (
      <CustomerLayout>
        <div className="max-w-4xl mx-auto p-6">
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div className="flex items-center">
              <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4"></i>
              <div>
                <h3 className="text-lg font-medium text-red-800 dark:text-red-200">Error Loading Form</h3>
                <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                <button
                  onClick={() => router.back()}
                  className="mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40"
                >
                  <i className="ri-arrow-left-line mr-2"></i>
                  Go Back
                </button>
              </div>
            </div>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2">
            Management Team Information
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Provide details about your organization's management team and structure.
          </p>
          {licenseType && (
            <div className="mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
              <p className="text-sm text-blue-700 dark:text-blue-300">
                <i className="ri-info-line mr-1"></i>
                License Type: {licenseType.name}
              </p>
            </div>
          )}
        </div>

        {/* Validation Errors */}
        {Object.keys(validationErrors).length > 0 && (
          <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200 mb-2">Please fix the following errors:</h3>
            <ul className="text-sm text-red-700 dark:text-red-300 list-disc list-inside">
              {Object.entries(validationErrors).map(([field, error]) => (
                <li key={field}>{error}</li>
              ))}
            </ul>
          </div>
        )}

        {/* Form Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          {/* Stakeholders Section */}
          <div className="mb-8">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Management Team Members
              </h3>
              <button
                type="button"
                onClick={addStakeholder}
                className="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
              >
                <i className="ri-add-line mr-1"></i>
                Add Member
              </button>
            </div>

            {formData.stakeholders.map((stakeholder, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4 mb-4">
                <div className="flex items-center justify-between mb-4">
                  <h4 className="text-md font-medium text-gray-900 dark:text-gray-100">
                    Team Member {index + 1}
                  </h4>
                  {formData.stakeholders.length > 1 && (
                    <button
                      type="button"
                      onClick={() => removeStakeholder(index)}
                      className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                    >
                      <i className="ri-delete-bin-line"></i>
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <TextInput
                    label="First Name"
                    value={stakeholder.first_name}
                    onChange={(value) => updateStakeholder(index, 'first_name', value)}
                    error={validationErrors[`stakeholder_${index}_first_name`]}
                    required
                  />
                  <TextInput
                    label="Last Name"
                    value={stakeholder.last_name}
                    onChange={(value) => updateStakeholder(index, 'last_name', value)}
                    error={validationErrors[`stakeholder_${index}_last_name`]}
                    required
                  />
                  <TextInput
                    label="Middle Name"
                    value={stakeholder.middle_name || ''}
                    onChange={(value) => updateStakeholder(index, 'middle_name', value)}
                  />
                  <TextInput
                    label="Nationality"
                    value={stakeholder.nationality}
                    onChange={(value) => updateStakeholder(index, 'nationality', value)}
                    error={validationErrors[`stakeholder_${index}_nationality`]}
                    required
                  />
                  <Select
                    label="Position"
                    value={stakeholder.position}
                    onChange={(value) => updateStakeholder(index, 'position', value)}
                    options={[
                      { value: 'CEO', label: 'Chief Executive Officer (CEO)' },
                      { value: 'SHAREHOLDER', label: 'Shareholder' },
                      { value: 'AUDITOR', label: 'Auditor' },
                      { value: 'LAWYER', label: 'Lawyer' }
                    ]}
                    required
                  />
                </div>

                <div className="mt-4">
                  <TextArea
                    label="Profile/Background"
                    value={stakeholder.profile}
                    onChange={(value) => updateStakeholder(index, 'profile', value)}
                    error={validationErrors[`stakeholder_${index}_profile`]}
                    rows={3}
                    placeholder="Describe the professional background and qualifications of this team member..."
                    required
                  />
                </div>
              </div>
            ))}
          </div>

          {/* Organizational Information Section */}
          <div className="border-t border-gray-200 dark:border-gray-700 pt-8">
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-4">
              Organizational Information
            </h3>

            <div className="space-y-4">
              <TextArea
                label="Organizational Structure"
                value={formData.organizational_structure}
                onChange={(value) => handleFormChange('organizational_structure', value)}
                error={validationErrors.organizational_structure}
                rows={4}
                placeholder="Describe your organization's structure, hierarchy, and reporting relationships..."
                required
              />

              <TextArea
                label="Key Personnel"
                value={formData.key_personnel}
                onChange={(value) => handleFormChange('key_personnel', value)}
                rows={3}
                placeholder="List and describe other key personnel not mentioned above..."
              />

              <TextArea
                label="Management Experience"
                value={formData.management_experience}
                onChange={(value) => handleFormChange('management_experience', value)}
                rows={3}
                placeholder="Describe the collective management experience of your team..."
              />

              <TextArea
                label="Leadership Approach"
                value={formData.leadership_approach}
                onChange={(value) => handleFormChange('leadership_approach', value)}
                rows={3}
                placeholder="Describe your organization's leadership philosophy and approach..."
              />

              <TextArea
                label="Succession Planning"
                value={formData.succession_planning}
                onChange={(value) => handleFormChange('succession_planning', value)}
                rows={3}
                placeholder="Describe your succession planning strategy for key positions..."
              />
            </div>
          </div>
        </div>

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <button
            type="button"
            onClick={handlePrevious}
            className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
          >
            <i className="ri-arrow-left-line mr-2"></i>
            Previous
          </button>

          <button
            type="button"
            onClick={handleNext}
            disabled={isSaving}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              <>
                Save & Continue
                <i className="ri-arrow-right-line ml-2"></i>
              </>
            )}
          </button>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default ManagementPage;
