{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|images).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|images).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "NxTYcanOzavf6GTL+lK1iSKVRUpUBVljI2zCk3fePC0=", "__NEXT_PREVIEW_MODE_ID": "d8ce415b7d12a0c67e04cb13c2460689", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "83381d1c888b57f7b4ee0a31747228bd333b71be451d1a019cba16e0be9481bb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "987b9a35d957e4ef00d8ae6562a9fc36072311729cec6dd95c8aaac6ceef0cef"}}}, "sortedMiddleware": ["/"], "functions": {}}