import { MigrationInterface, QueryRunner, Table, Index, ForeignKey } from 'typeorm';

export class CreateApplicationFormDataTable1704201600000 implements MigrationInterface {
  name = 'CreateApplicationFormDataTable1704201600000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        name: 'application_form_data',
        columns: [
          {
            name: 'form_data_id',
            type: 'varchar',
            length: '36',
            isPrimary: true,
            generationStrategy: 'uuid',
            default: 'uuid()',
          },
          {
            name: 'application_id',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'section_name',
            type: 'varchar',
            length: '100',
            isNullable: false,
          },
          {
            name: 'section_data',
            type: 'json',
            isNullable: false,
          },
          {
            name: 'completed',
            type: 'boolean',
            default: false,
          },
          {
            name: 'created_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'created_by',
            type: 'varchar',
            length: '36',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'timestamp',
            default: 'CURRENT_TIMESTAMP',
            onUpdate: 'CURRENT_TIMESTAMP',
          },
          {
            name: 'updated_by',
            type: 'varchar',
            length: '36',
            isNullable: true,
          },
          {
            name: 'deleted_at',
            type: 'timestamp',
            isNullable: true,
          },
        ],
      }),
      true,
    );

    // Create unique index for application_id + section_name
    await queryRunner.createIndex(
      'application_form_data',
      new Index('IDX_application_form_data_app_section', ['application_id', 'section_name'], {
        isUnique: true,
      }),
    );

    // Create foreign key constraint to applications table
    await queryRunner.createForeignKey(
      'application_form_data',
      new ForeignKey({
        columnNames: ['application_id'],
        referencedColumnNames: ['application_id'],
        referencedTableName: 'applications',
        onDelete: 'CASCADE',
        onUpdate: 'CASCADE',
      }),
    );

    // Create foreign key constraint to users table for created_by
    await queryRunner.createForeignKey(
      'application_form_data',
      new ForeignKey({
        columnNames: ['created_by'],
        referencedColumnNames: ['user_id'],
        referencedTableName: 'users',
        onDelete: 'RESTRICT',
        onUpdate: 'CASCADE',
      }),
    );

    // Create foreign key constraint to users table for updated_by
    await queryRunner.createForeignKey(
      'application_form_data',
      new ForeignKey({
        columnNames: ['updated_by'],
        referencedColumnNames: ['user_id'],
        referencedTableName: 'users',
        onDelete: 'SET NULL',
        onUpdate: 'CASCADE',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropTable('application_form_data');
  }
}
