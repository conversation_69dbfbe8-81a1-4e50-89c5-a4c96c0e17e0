{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Consumer Affairs',\r\n      href: '/customer/consumer-affairs',\r\n      icon: 'ri-shield-user-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/consumer-affairs',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/standards': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/consumer-affairs': 'Loading Consumer Affairs...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,sBAAsB;YACtB,0BAA0B;YAC1B,oCAAoC;YACpC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,8BAA8B;YAC9B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/FileUpload.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useRef } from 'react';\r\n\r\ninterface FileUploadProps {\r\n  id: string;\r\n  label: string;\r\n  accept?: string;\r\n  maxSize?: number; // in MB\r\n  required?: boolean;\r\n  value?: File | null;\r\n  onChange: (file: File | null) => void;\r\n  description?: string;\r\n  className?: string;\r\n}\r\n\r\nconst FileUpload: React.FC<FileUploadProps> = ({\r\n  id,\r\n  label,\r\n  accept = '.pdf',\r\n  maxSize = 10,\r\n  required = false,\r\n  value,\r\n  onChange,\r\n  description,\r\n  className = ''\r\n}) => {\r\n  const fileInputRef = useRef<HTMLInputElement>(null);\r\n\r\n  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {\r\n    const file = event.target.files?.[0] || null;\r\n    \r\n    if (file) {\r\n      // Check file size\r\n      if (file.size > maxSize * 1024 * 1024) {\r\n        alert(`File size must be less than ${maxSize}MB`);\r\n        return;\r\n      }\r\n      \r\n      // Check file type\r\n      if (accept && !accept.split(',').some(type => file.name.toLowerCase().endsWith(type.trim().replace('*', '')))) {\r\n        alert(`File type must be: ${accept}`);\r\n        return;\r\n      }\r\n    }\r\n    \r\n    onChange(file);\r\n  };\r\n\r\n  const handleClick = () => {\r\n    fileInputRef.current?.click();\r\n  };\r\n\r\n  const handleRemove = (e: React.MouseEvent) => {\r\n    e.stopPropagation();\r\n    onChange(null);\r\n    if (fileInputRef.current) {\r\n      fileInputRef.current.value = '';\r\n    }\r\n  };\r\n\r\n  return (\r\n    <div className={className}>\r\n      <label htmlFor={id} className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      <div \r\n        onClick={handleClick}\r\n        className=\"border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center cursor-pointer hover:border-primary hover:bg-gray-50 dark:hover:bg-gray-700/50 transition-colors\"\r\n      >\r\n        <input\r\n          ref={fileInputRef}\r\n          type=\"file\"\r\n          accept={accept}\r\n          onChange={handleFileChange}\r\n          className=\"hidden\"\r\n          id={id}\r\n          required={required}\r\n        />\r\n        \r\n        {value ? (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-file-text-line text-3xl text-green-500\"></i>\r\n            </div>\r\n            <p className=\"text-sm font-medium text-green-600 dark:text-green-400\">\r\n              {value.name}\r\n            </p>\r\n            <p className=\"text-xs text-gray-500 dark:text-gray-400\">\r\n              {(value.size / 1024 / 1024).toFixed(2)} MB\r\n            </p>\r\n            <button\r\n              type=\"button\"\r\n              onClick={handleRemove}\r\n              className=\"inline-flex items-center px-3 py-1 bg-red-100 text-red-700 hover:bg-red-200 rounded-md text-xs font-medium transition-colors\"\r\n            >\r\n              <i className=\"ri-delete-bin-line mr-1\"></i>\r\n              Remove\r\n            </button>\r\n          </div>\r\n        ) : (\r\n          <div className=\"space-y-2\">\r\n            <div className=\"flex items-center justify-center\">\r\n              <i className=\"ri-upload-cloud-2-line text-3xl text-gray-400\"></i>\r\n            </div>\r\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\r\n              Click to upload {label.toLowerCase()}\r\n            </p>\r\n            {description && (\r\n              <p className=\"text-xs text-gray-500 dark:text-gray-500\">\r\n                {description}\r\n              </p>\r\n            )}\r\n            <div className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors\">\r\n              <i className=\"ri-folder-upload-line mr-2\"></i>\r\n              Choose File\r\n            </div>\r\n          </div>\r\n        )}\r\n      </div>\r\n      \r\n      {description && !value && (\r\n        <p className=\"mt-2 text-xs text-gray-500 dark:text-gray-400\">\r\n          {description}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FileUpload;\r\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBA,MAAM,aAAwC,CAAC,EAC7C,EAAE,EACF,KAAK,EACL,SAAS,MAAM,EACf,UAAU,EAAE,EACZ,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,WAAW,EACX,YAAY,EAAE,EACf;IACC,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE9C,MAAM,mBAAmB,CAAC;QACxB,MAAM,OAAO,MAAM,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE,IAAI;QAExC,IAAI,MAAM;YACR,kBAAkB;YAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;gBACrC,MAAM,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;gBAChD;YACF;YAEA,kBAAkB;YAClB,IAAI,UAAU,CAAC,OAAO,KAAK,CAAC,KAAK,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,KAAK,IAAI,GAAG,OAAO,CAAC,KAAK,OAAO;gBAC7G,MAAM,CAAC,mBAAmB,EAAE,QAAQ;gBACpC;YACF;QACF;QAEA,SAAS;IACX;IAEA,MAAM,cAAc;QAClB,aAAa,OAAO,EAAE;IACxB;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,eAAe;QACjB,SAAS;QACT,IAAI,aAAa,OAAO,EAAE;YACxB,aAAa,OAAO,CAAC,KAAK,GAAG;QAC/B;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,SAAS;gBAAI,WAAU;;oBAC3B;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;0BAGtD,8OAAC;gBACC,SAAS;gBACT,WAAU;;kCAEV,8OAAC;wBACC,KAAK;wBACL,MAAK;wBACL,QAAQ;wBACR,UAAU;wBACV,WAAU;wBACV,IAAI;wBACJ,UAAU;;;;;;oBAGX,sBACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;0CACV,MAAM,IAAI;;;;;;0CAEb,8OAAC;gCAAE,WAAU;;oCACV,CAAC,MAAM,IAAI,GAAG,OAAO,IAAI,EAAE,OAAO,CAAC;oCAAG;;;;;;;0CAEzC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAA8B;;;;;;;;;;;;6CAK/C,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;;;;;;;;;;;0CAEf,8OAAC;gCAAE,WAAU;;oCAA2C;oCACrC,MAAM,WAAW;;;;;;;4BAEnC,6BACC,8OAAC;gCAAE,WAAU;0CACV;;;;;;0CAGL,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;;;;;oCAAiC;;;;;;;;;;;;;;;;;;;YAOrD,eAAe,CAAC,uBACf,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/FormField.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface FormFieldProps {\r\n  label: string;\r\n  required?: boolean;\r\n  error?: string;\r\n  children: React.ReactNode;\r\n  className?: string;\r\n  description?: string;\r\n}\r\n\r\nconst FormField: React.FC<FormFieldProps> = ({\r\n  label,\r\n  required = false,\r\n  error,\r\n  children,\r\n  className = '',\r\n  description\r\n}) => {\r\n  return (\r\n    <div className={className}>\r\n      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\r\n        {label} {required && <span className=\"text-red-500\">*</span>}\r\n      </label>\r\n      \r\n      {description && (\r\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mb-2\">\r\n          {description}\r\n        </p>\r\n      )}\r\n      \r\n      {children}\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          <i className=\"ri-error-warning-line mr-1\"></i>\r\n          {error}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FormField;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAaA,MAAM,YAAsC,CAAC,EAC3C,KAAK,EACL,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,YAAY,EAAE,EACd,WAAW,EACZ;IACC,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAM,WAAU;;oBACd;oBAAM;oBAAE,0BAAY,8OAAC;wBAAK,WAAU;kCAAe;;;;;;;;;;;;YAGrD,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ;YAEA,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 1165, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/ProgressIndicator.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\n\r\ninterface Step {\r\n  id: number;\r\n  label: string;\r\n  completed?: boolean;\r\n}\r\n\r\ninterface ProgressIndicatorProps {\r\n  steps: Step[];\r\n  currentStep: number;\r\n  className?: string;\r\n}\r\n\r\nconst ProgressIndicator: React.FC<ProgressIndicatorProps> = ({\r\n  steps,\r\n  currentStep,\r\n  className = ''\r\n}) => {\r\n  return (\r\n    <div className={className}>\r\n      <div className=\"flex items-center justify-between\">\r\n        {steps.map((step, index) => (\r\n          <div key={step.id} className=\"flex items-center\">\r\n            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${\r\n              step.id === currentStep\r\n                ? 'bg-primary text-white'\r\n                : step.id < currentStep || step.completed\r\n                ? 'bg-green-500 text-white'\r\n                : 'bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400'\r\n            }`}>\r\n              {step.id < currentStep || step.completed ? (\r\n                <i className=\"ri-check-line\"></i>\r\n              ) : (\r\n                step.id\r\n              )}\r\n            </div>\r\n            {index < steps.length - 1 && (\r\n              <div className={`w-12 h-0.5 mx-2 transition-colors ${\r\n                step.id < currentStep || step.completed ? 'bg-green-500' : 'bg-gray-200 dark:bg-gray-700'\r\n              }`}></div>\r\n            )}\r\n          </div>\r\n        ))}\r\n      </div>\r\n      \r\n      <div className=\"flex justify-between text-xs text-gray-500 dark:text-gray-400 mt-2\">\r\n        {steps.map((step) => (\r\n          <span key={step.id} className=\"text-center max-w-20 truncate\">\r\n            {step.label}\r\n          </span>\r\n        ))}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ProgressIndicator;\r\n"], "names": [], "mappings": ";;;;AAAA;;AAgBA,MAAM,oBAAsD,CAAC,EAC3D,KAAK,EACL,WAAW,EACX,YAAY,EAAE,EACf;IACC,qBACE,8OAAC;QAAI,WAAW;;0BACd,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,8OAAC;wBAAkB,WAAU;;0CAC3B,8OAAC;gCAAI,WAAW,CAAC,4FAA4F,EAC3G,KAAK,EAAE,KAAK,cACR,0BACA,KAAK,EAAE,GAAG,eAAe,KAAK,SAAS,GACvC,4BACA,iEACJ;0CACC,KAAK,EAAE,GAAG,eAAe,KAAK,SAAS,iBACtC,8OAAC;oCAAE,WAAU;;;;;2CAEb,KAAK,EAAE;;;;;;4BAGV,QAAQ,MAAM,MAAM,GAAG,mBACtB,8OAAC;gCAAI,WAAW,CAAC,kCAAkC,EACjD,KAAK,EAAE,GAAG,eAAe,KAAK,SAAS,GAAG,iBAAiB,gCAC3D;;;;;;;uBAjBI,KAAK,EAAE;;;;;;;;;;0BAuBrB,8OAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,8OAAC;wBAAmB,WAAU;kCAC3B,KAAK,KAAK;uBADF,KAAK,EAAE;;;;;;;;;;;;;;;;AAO5B;uCAEe", "debugId": null}}, {"offset": {"line": 1241, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/CountryDropdown.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useRef } from 'react';\r\n\r\n// Country list for nationality dropdown\r\nconst COUNTRIES = [\r\n  'Afghanistan', 'Albania', 'Algeria', 'Andorra', 'Angola', 'Antigua and Barbuda', 'Argentina', 'Armenia', 'Australia', 'Austria',\r\n  'Azerbaijan', 'Bahamas', 'Bahrain', 'Bangladesh', 'Barbados', 'Belarus', 'Belgium', 'Belize', 'Benin', 'Bhutan',\r\n  'Bolivia', 'Bosnia and Herzegovina', 'Botswana', 'Brazil', 'Brunei', 'Bulgaria', 'Burkina Faso', 'Burundi', 'Cabo Verde', 'Cambodia',\r\n  'Cameroon', 'Canada', 'Central African Republic', 'Chad', 'Chile', 'China', 'Colombia', 'Comoros', 'Congo (Congo-Brazzaville)', 'Congo (Democratic Republic)',\r\n  'Costa Rica', 'Croatia', 'Cuba', 'Cyprus', 'Czechia (Czech Republic)', 'Denmark', 'Djibouti', 'Dominica', 'Dominican Republic', 'Ecuador',\r\n  'Egypt', 'El Salvador', 'Equatorial Guinea', 'Eritrea', 'Estonia', '<PERSON><PERSON><PERSON><PERSON>', 'Ethiopia', 'Fiji', 'Finland', 'France',\r\n  'Gabon', 'Gambia', 'Georgia', 'Germany', 'Ghana', 'Greece', 'Grenada', 'Guatemala', 'Guinea', 'Guinea-Bissau',\r\n  'Guyana', 'Haiti', 'Holy See', 'Honduras', 'Hungary', 'Iceland', 'India', 'Indonesia', 'Iran', 'Iraq',\r\n  'Ireland', 'Israel', 'Italy', 'Jamaica', 'Japan', 'Jordan', 'Kazakhstan', 'Kenya', 'Kiribati', 'Kuwait',\r\n  'Kyrgyzstan', 'Laos', 'Latvia', 'Lebanon', 'Lesotho', 'Liberia', 'Libya', 'Liechtenstein', 'Lithuania', 'Luxembourg',\r\n  'Madagascar', 'Malawi', 'Malaysia', 'Maldives', 'Mali', 'Malta', 'Marshall Islands', 'Mauritania', 'Mauritius', 'Mexico',\r\n  'Micronesia', 'Moldova', 'Monaco', 'Mongolia', 'Montenegro', 'Morocco', 'Mozambique', 'Myanmar (formerly Burma)', 'Namibia', 'Nauru',\r\n  'Nepal', 'Netherlands', 'New Zealand', 'Nicaragua', 'Niger', 'Nigeria', 'North Korea', 'North Macedonia', 'Norway', 'Oman',\r\n  'Pakistan', 'Palau', 'Palestine State', 'Panama', 'Papua New Guinea', 'Paraguay', 'Peru', 'Philippines', 'Poland', 'Portugal',\r\n  'Qatar', 'Romania', 'Russia', 'Rwanda', 'Saint Kitts and Nevis', 'Saint Lucia', 'Saint Vincent and the Grenadines', 'Samoa', 'San Marino', 'Sao Tome and Principe',\r\n  'Saudi Arabia', 'Senegal', 'Serbia', 'Seychelles', 'Sierra Leone', 'Singapore', 'Slovakia', 'Slovenia', 'Solomon Islands', 'Somalia',\r\n  'South Africa', 'South Korea', 'South Sudan', 'Spain', 'Sri Lanka', 'Sudan', 'Suriname', 'Sweden', 'Switzerland', 'Syria',\r\n  'Tajikistan', 'Tanzania', 'Thailand', 'Timor-Leste', 'Togo', 'Tonga', 'Trinidad and Tobago', 'Tunisia', 'Turkey', 'Turkmenistan',\r\n  'Tuvalu', 'Uganda', 'Ukraine', 'United Arab Emirates', 'United Kingdom', 'United States of America', 'Uruguay', 'Uzbekistan', 'Vanuatu', 'Venezuela',\r\n  'Vietnam', 'Yemen', 'Zambia', 'Zimbabwe'\r\n];\r\n\r\ninterface CountryDropdownProps {\r\n  label?: string;\r\n  value: string;\r\n  onChange: (value: string) => void;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  className?: string;\r\n  disabled?: boolean;\r\n  error?: string;\r\n  id?: string;\r\n  name?: string;\r\n}\r\n\r\nconst CountryDropdown: React.FC<CountryDropdownProps> = ({ \r\n  label,\r\n  value, \r\n  onChange, \r\n  placeholder = \"Select or type country name\", \r\n  required = false,\r\n  className = \"\",\r\n  disabled = false,\r\n  error = \"\",\r\n  id,\r\n  name\r\n}) => {\r\n  const [isOpen, setIsOpen] = useState(false);\r\n  const [searchTerm, setSearchTerm] = useState('');\r\n  const [filteredCountries, setFilteredCountries] = useState(COUNTRIES);\r\n  const [activeIndex, setActiveIndex] = useState(-1);\r\n  const dropdownRef = useRef<HTMLDivElement>(null);\r\n  const inputRef = useRef<HTMLInputElement>(null);\r\n  const listboxId = `${id || 'country-dropdown'}-listbox`;\r\n\r\n  // Filter countries based on search term\r\n  useEffect(() => {\r\n    const filtered = COUNTRIES.filter(country =>\r\n      country.toLowerCase().includes(searchTerm.toLowerCase())\r\n    );\r\n    setFilteredCountries(filtered);\r\n    setActiveIndex(-1); // Reset active index when filtering\r\n  }, [searchTerm]);\r\n\r\n  // Close dropdown when clicking outside\r\n  useEffect(() => {\r\n    const handleClickOutside = (event: MouseEvent) => {\r\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\r\n        setIsOpen(false);\r\n        setActiveIndex(-1);\r\n      }\r\n    };\r\n\r\n    document.addEventListener('mousedown', handleClickOutside);\r\n    return () => {\r\n      document.removeEventListener('mousedown', handleClickOutside);\r\n    };\r\n  }, []);\r\n\r\n  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {\r\n    const inputValue = e.target.value;\r\n    setSearchTerm(inputValue);\r\n    onChange(inputValue);\r\n    if (!disabled) {\r\n      setIsOpen(true);\r\n    }\r\n  };\r\n\r\n  const handleCountrySelect = (country: string) => {\r\n    onChange(country);\r\n    setSearchTerm(country);\r\n    setIsOpen(false);\r\n    setActiveIndex(-1);\r\n  };\r\n\r\n  const handleInputFocus = () => {\r\n    if (!disabled) {\r\n      setIsOpen(true);\r\n      setSearchTerm(value);\r\n    }\r\n  };\r\n\r\n  const handleInputBlur = () => {\r\n    // Delay hiding dropdown to allow for country selection\r\n    setTimeout(() => {\r\n      setIsOpen(false);\r\n      setActiveIndex(-1);\r\n    }, 150);\r\n  };\r\n\r\n  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {\r\n    if (e.key === 'Escape') {\r\n      setIsOpen(false);\r\n      setActiveIndex(-1);\r\n      inputRef.current?.blur();\r\n    } else if (e.key === 'ArrowDown') {\r\n      e.preventDefault();\r\n      if (!isOpen) {\r\n        setIsOpen(true);\r\n      }\r\n      setActiveIndex(prev =>\r\n        prev < filteredCountries.length - 1 ? prev + 1 : 0\r\n      );\r\n    } else if (e.key === 'ArrowUp') {\r\n      e.preventDefault();\r\n      if (!isOpen) {\r\n        setIsOpen(true);\r\n      }\r\n      setActiveIndex(prev =>\r\n        prev > 0 ? prev - 1 : filteredCountries.length - 1\r\n      );\r\n    } else if (e.key === 'Enter') {\r\n      e.preventDefault();\r\n      if (isOpen && activeIndex >= 0 && filteredCountries[activeIndex]) {\r\n        handleCountrySelect(filteredCountries[activeIndex]);\r\n      }\r\n    } else if (e.key === 'Tab') {\r\n      // Allow tab to close dropdown and move to next element\r\n      setIsOpen(false);\r\n      setActiveIndex(-1);\r\n    }\r\n  };\r\n\r\n  // Base input styling with proper text visibility\r\n  const baseInputClass = `w-full px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    className.includes('text-sm') ? 'py-1.5 text-sm' : 'py-2'\r\n  }`;\r\n  \r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  }`;\r\n\r\n  const labelClass = \"block mb-1 font-medium text-gray-700 dark:text-gray-200\";\r\n\r\n  return (\r\n    <div className={`relative ${className}`} ref={dropdownRef}>\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      <input\r\n        ref={inputRef}\r\n        type=\"text\"\r\n        id={id}\r\n        name={name}\r\n        value={isOpen ? searchTerm : value}\r\n        onChange={handleInputChange}\r\n        onFocus={handleInputFocus}\r\n        onBlur={handleInputBlur}\r\n        onKeyDown={handleKeyDown}\r\n        className={inputClass}\r\n        placeholder={placeholder}\r\n        required={required}\r\n        disabled={disabled}\r\n        autoComplete=\"country\"\r\n        aria-expanded={isOpen}\r\n        aria-haspopup=\"listbox\"\r\n        aria-controls={isOpen ? listboxId : undefined}\r\n        aria-activedescendant={activeIndex >= 0 && isOpen ? `${listboxId}-option-${activeIndex}` : undefined}\r\n        role=\"combobox\"\r\n        aria-autocomplete=\"list\"\r\n        aria-describedby={error ? `${id}-error` : undefined}\r\n      />\r\n      \r\n      {/* Dropdown icon */}\r\n      <div className={`absolute inset-y-0 right-0 top-7 flex items-center pr-3 pointer-events-none ${disabled ? 'opacity-50' : ''}`}>\r\n        <i className={`ri-arrow-down-s-line text-gray-400 transition-transform duration-200 ${isOpen ? 'rotate-180' : ''}`}></i>\r\n      </div>\r\n\r\n      {/* Dropdown list */}\r\n      {isOpen && !disabled && (\r\n        <div \r\n          id={listboxId}\r\n          className=\"absolute z-50 w-full mt-1 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto\"\r\n          role=\"listbox\"\r\n          aria-label=\"Country options\"\r\n        >\r\n          {filteredCountries.length > 0 ? (\r\n            filteredCountries.map((country, index) => (\r\n              <div\r\n                key={country}\r\n                id={`${listboxId}-option-${index}`}\r\n                className={`px-3 py-2 cursor-pointer text-sm transition-colors duration-150 ${\r\n                  index === activeIndex\r\n                    ? 'font-semibold text-red-600 dark:text-red-300 bg-gray-100 dark:bg-gray-800'\r\n                    : 'font-normal text-gray-900 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-600'\r\n                }`}\r\n                onClick={() => handleCountrySelect(country)}\r\n                onMouseDown={(e) => e.preventDefault()} // Prevent input blur\r\n                onMouseEnter={() => setActiveIndex(index)}\r\n                role=\"option\"\r\n                aria-selected={value === country}\r\n              >\r\n                {country}\r\n              </div>\r\n            ))\r\n          ) : (\r\n            <div className=\"px-3 py-2 text-sm text-gray-500 dark:text-gray-400\" role=\"option\" aria-disabled=\"true\" aria-selected=\"false\">\r\n              No countries found\r\n            </div>\r\n          )}\r\n        </div>\r\n      )}\r\n\r\n      {/* Error message */}\r\n      {error && (\r\n        <p id={`${id}-error`} className=\"mt-1 text-sm text-red-600 dark:text-red-400\" role=\"alert\">\r\n          {error}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CountryDropdown;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAIA,wCAAwC;AACxC,MAAM,YAAY;IAChB;IAAe;IAAW;IAAW;IAAW;IAAU;IAAuB;IAAa;IAAW;IAAa;IACtH;IAAc;IAAW;IAAW;IAAc;IAAY;IAAW;IAAW;IAAU;IAAS;IACvG;IAAW;IAA0B;IAAY;IAAU;IAAU;IAAY;IAAgB;IAAW;IAAc;IAC1H;IAAY;IAAU;IAA4B;IAAQ;IAAS;IAAS;IAAY;IAAW;IAA6B;IAChI;IAAc;IAAW;IAAQ;IAAU;IAA4B;IAAW;IAAY;IAAY;IAAsB;IAChI;IAAS;IAAe;IAAqB;IAAW;IAAW;IAAY;IAAY;IAAQ;IAAW;IAC9G;IAAS;IAAU;IAAW;IAAW;IAAS;IAAU;IAAW;IAAa;IAAU;IAC9F;IAAU;IAAS;IAAY;IAAY;IAAW;IAAW;IAAS;IAAa;IAAQ;IAC/F;IAAW;IAAU;IAAS;IAAW;IAAS;IAAU;IAAc;IAAS;IAAY;IAC/F;IAAc;IAAQ;IAAU;IAAW;IAAW;IAAW;IAAS;IAAiB;IAAa;IACxG;IAAc;IAAU;IAAY;IAAY;IAAQ;IAAS;IAAoB;IAAc;IAAa;IAChH;IAAc;IAAW;IAAU;IAAY;IAAc;IAAW;IAAc;IAA4B;IAAW;IAC7H;IAAS;IAAe;IAAe;IAAa;IAAS;IAAW;IAAe;IAAmB;IAAU;IACpH;IAAY;IAAS;IAAmB;IAAU;IAAoB;IAAY;IAAQ;IAAe;IAAU;IACnH;IAAS;IAAW;IAAU;IAAU;IAAyB;IAAe;IAAoC;IAAS;IAAc;IAC3I;IAAgB;IAAW;IAAU;IAAc;IAAgB;IAAa;IAAY;IAAY;IAAmB;IAC3H;IAAgB;IAAe;IAAe;IAAS;IAAa;IAAS;IAAY;IAAU;IAAe;IAClH;IAAc;IAAY;IAAY;IAAe;IAAQ;IAAS;IAAuB;IAAW;IAAU;IAClH;IAAU;IAAU;IAAW;IAAwB;IAAkB;IAA4B;IAAW;IAAc;IAAW;IACzI;IAAW;IAAS;IAAU;CAC/B;AAeD,MAAM,kBAAkD,CAAC,EACvD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,cAAc,6BAA6B,EAC3C,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,WAAW,KAAK,EAChB,QAAQ,EAAE,EACV,EAAE,EACF,IAAI,EACL;IACC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;IAChD,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAC1C,MAAM,YAAY,GAAG,MAAM,mBAAmB,QAAQ,CAAC;IAEvD,wCAAwC;IACxC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW,UAAU,MAAM,CAAC,CAAA,UAChC,QAAQ,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEvD,qBAAqB;QACrB,eAAe,CAAC,IAAI,oCAAoC;IAC1D,GAAG;QAAC;KAAW;IAEf,uCAAuC;IACvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E,UAAU;gBACV,eAAe,CAAC;YAClB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB,CAAC;QACzB,MAAM,aAAa,EAAE,MAAM,CAAC,KAAK;QACjC,cAAc;QACd,SAAS;QACT,IAAI,CAAC,UAAU;YACb,UAAU;QACZ;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,SAAS;QACT,cAAc;QACd,UAAU;QACV,eAAe,CAAC;IAClB;IAEA,MAAM,mBAAmB;QACvB,IAAI,CAAC,UAAU;YACb,UAAU;YACV,cAAc;QAChB;IACF;IAEA,MAAM,kBAAkB;QACtB,uDAAuD;QACvD,WAAW;YACT,UAAU;YACV,eAAe,CAAC;QAClB,GAAG;IACL;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,UAAU;YACV,eAAe,CAAC;YAChB,SAAS,OAAO,EAAE;QACpB,OAAO,IAAI,EAAE,GAAG,KAAK,aAAa;YAChC,EAAE,cAAc;YAChB,IAAI,CAAC,QAAQ;gBACX,UAAU;YACZ;YACA,eAAe,CAAA,OACb,OAAO,kBAAkB,MAAM,GAAG,IAAI,OAAO,IAAI;QAErD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;YAC9B,EAAE,cAAc;YAChB,IAAI,CAAC,QAAQ;gBACX,UAAU;YACZ;YACA,eAAe,CAAA,OACb,OAAO,IAAI,OAAO,IAAI,kBAAkB,MAAM,GAAG;QAErD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;YAC5B,EAAE,cAAc;YAChB,IAAI,UAAU,eAAe,KAAK,iBAAiB,CAAC,YAAY,EAAE;gBAChE,oBAAoB,iBAAiB,CAAC,YAAY;YACpD;QACF,OAAO,IAAI,EAAE,GAAG,KAAK,OAAO;YAC1B,uDAAuD;YACvD,UAAU;YACV,eAAe,CAAC;QAClB;IACF;IAEA,iDAAiD;IACjD,MAAM,iBAAiB,CAAC,yPAAyP,EAC/Q,UAAU,QAAQ,CAAC,aAAa,mBAAmB,QACnD;IAEF,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,IACJ;IAEF,MAAM,aAAa;IAEnB,qBACE,8OAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;QAAE,KAAK;;YAC3C,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAGrD,8OAAC;gBACC,KAAK;gBACL,MAAK;gBACL,IAAI;gBACJ,MAAM;gBACN,OAAO,SAAS,aAAa;gBAC7B,UAAU;gBACV,SAAS;gBACT,QAAQ;gBACR,WAAW;gBACX,WAAW;gBACX,aAAa;gBACb,UAAU;gBACV,UAAU;gBACV,cAAa;gBACb,iBAAe;gBACf,iBAAc;gBACd,iBAAe,SAAS,YAAY;gBACpC,yBAAuB,eAAe,KAAK,SAAS,GAAG,UAAU,QAAQ,EAAE,aAAa,GAAG;gBAC3F,MAAK;gBACL,qBAAkB;gBAClB,oBAAkB,QAAQ,GAAG,GAAG,MAAM,CAAC,GAAG;;;;;;0BAI5C,8OAAC;gBAAI,WAAW,CAAC,4EAA4E,EAAE,WAAW,eAAe,IAAI;0BAC3H,cAAA,8OAAC;oBAAE,WAAW,CAAC,qEAAqE,EAAE,SAAS,eAAe,IAAI;;;;;;;;;;;YAInH,UAAU,CAAC,0BACV,8OAAC;gBACC,IAAI;gBACJ,WAAU;gBACV,MAAK;gBACL,cAAW;0BAEV,kBAAkB,MAAM,GAAG,IAC1B,kBAAkB,GAAG,CAAC,CAAC,SAAS,sBAC9B,8OAAC;wBAEC,IAAI,GAAG,UAAU,QAAQ,EAAE,OAAO;wBAClC,WAAW,CAAC,gEAAgE,EAC1E,UAAU,cACN,8EACA,yFACJ;wBACF,SAAS,IAAM,oBAAoB;wBACnC,aAAa,CAAC,IAAM,EAAE,cAAc;wBACpC,cAAc,IAAM,eAAe;wBACnC,MAAK;wBACL,iBAAe,UAAU;kCAExB;uBAbI;;;;8CAiBT,8OAAC;oBAAI,WAAU;oBAAqD,MAAK;oBAAS,iBAAc;oBAAO,iBAAc;8BAAQ;;;;;;;;;;;YAQlI,uBACC,8OAAC;gBAAE,IAAI,GAAG,GAAG,MAAM,CAAC;gBAAE,WAAU;gBAA8C,MAAK;0BAChF;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 1656, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/TextInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  ...props\r\n}, ref) => {\r\n  // Base input styling with proper text visibility for all modes\r\n  const baseInputClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const inputClass = `${baseInputClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <input\r\n        ref={ref}\r\n        className={inputClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextInput.displayName = 'TextInput';\r\n\r\nexport default TextInput;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,+DAA+D;IAC/D,MAAM,iBAAiB,CAAC,kPAAkP,EACxQ,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,aAAa,GAAG,eAAe,CAAC,EACpC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 1733, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/TextArea.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface TextAreaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n}\r\n\r\nconst TextArea = forwardRef<HTMLTextAreaElement, TextAreaProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  rows = 3,\r\n  ...props\r\n}, ref) => {\r\n  // Base textarea styling with proper text visibility for all modes\r\n  const baseTextAreaClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 transition-colors duration-200 resize-y ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const textAreaClass = `${baseTextAreaClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <textarea\r\n        ref={ref}\r\n        className={textAreaClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        rows={rows}\r\n        {...props}\r\n      />\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nTextArea.displayName = 'TextArea';\r\n\r\nexport default TextArea;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAYA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAsC,CAAC,EAC/D,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,CAAC,EACR,GAAG,OACJ,EAAE;IACD,kEAAkE;IAClE,MAAM,oBAAoB,CAAC,2PAA2P,EACpR,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,gBAAgB,GAAG,kBAAkB,CAAC,EAC1C,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,MAAM;gBACL,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,SAAS,WAAW,GAAG;uCAER", "debugId": null}}, {"offset": {"line": 1811, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/Select.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { forwardRef } from 'react';\r\n\r\ninterface SelectOption {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface SelectProps extends React.SelectHTMLAttributes<HTMLSelectElement> {\r\n  label?: React.ReactNode;\r\n  error?: string;\r\n  helperText?: string;\r\n  variant?: 'default' | 'small';\r\n  fullWidth?: boolean;\r\n  options?: SelectOption[];\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\r\n  label,\r\n  error,\r\n  helperText,\r\n  variant = 'default',\r\n  fullWidth = true,\r\n  className = '',\r\n  required,\r\n  disabled,\r\n  options,\r\n  children,\r\n  ...props\r\n}, ref) => {\r\n  // Base select styling with proper text visibility for all modes\r\n  const baseSelectClass = `px-3 border rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 transition-colors duration-200 ${\r\n    fullWidth ? 'w-full' : ''\r\n  } ${variant === 'small' ? 'py-1.5 text-sm' : 'py-2'}`;\r\n\r\n  // Error and disabled states\r\n  const selectClass = `${baseSelectClass} ${\r\n    error \r\n      ? \"border-red-300 dark:border-red-600 focus:ring-red-500 focus:border-red-500\" \r\n      : \"border-gray-300 dark:border-gray-600\"\r\n  } ${\r\n    disabled \r\n      ? \"opacity-50 cursor-not-allowed bg-gray-50 dark:bg-gray-800\" \r\n      : \"\"\r\n  } ${className}`;\r\n\r\n  const labelClass = `block font-medium text-gray-700 dark:text-gray-300 mb-2 ${\r\n    variant === 'small' ? 'text-xs text-gray-600 dark:text-gray-400 mb-1' : 'text-sm'\r\n  }`;\r\n\r\n  return (\r\n    <div className=\"w-full\">\r\n      {label && (\r\n        <label className={labelClass}>\r\n          {label}\r\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\r\n        </label>\r\n      )}\r\n      \r\n      <select\r\n        ref={ref}\r\n        className={selectClass}\r\n        disabled={disabled}\r\n        required={required}\r\n        {...props}\r\n      >\r\n        {options ? (\r\n          options.map((option) => (\r\n            <option key={option.value} value={option.value}>\r\n              {option.label}\r\n            </option>\r\n          ))\r\n        ) : (\r\n          children\r\n        )}\r\n      </select>\r\n      \r\n      {error && (\r\n        <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\r\n          {error}\r\n        </p>\r\n      )}\r\n      \r\n      {helperText && !error && (\r\n        <p className=\"mt-1 text-sm text-gray-500 dark:text-gray-400\">\r\n          {helperText}\r\n        </p>\r\n      )}\r\n    </div>\r\n  );\r\n});\r\n\r\nSelect.displayName = 'Select';\r\n\r\nexport default Select;"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAmBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,gEAAgE;IAChE,MAAM,kBAAkB,CAAC,mMAAmM,EAC1N,YAAY,WAAW,GACxB,CAAC,EAAE,YAAY,UAAU,mBAAmB,QAAQ;IAErD,4BAA4B;IAC5B,MAAM,cAAc,GAAG,gBAAgB,CAAC,EACtC,QACI,+EACA,uCACL,CAAC,EACA,WACI,8DACA,GACL,CAAC,EAAE,WAAW;IAEf,MAAM,aAAa,CAAC,wDAAwD,EAC1E,YAAY,UAAU,kDAAkD,WACxE;IAEF,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAW;;oBACf;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,UAAU;gBACT,GAAG,KAAK;0BAER,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;gCAK3B;;;;;;YAIH,uBACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1896, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/FormInput.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React from 'react';\r\nimport TextInput from './TextInput';\r\nimport TextArea from './TextArea';\r\nimport Select from './Select';\r\n\r\ninterface Option {\r\n  value: string;\r\n  label: string;\r\n}\r\n\r\ninterface FormInputProps {\r\n  type?: 'text' | 'email' | 'password' | 'number' | 'tel' | 'textarea' | 'select';\r\n  label?: string | React.ReactNode;\r\n  id?: string;\r\n  name?: string;\r\n  value?: string;\r\n  onChange?: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => void;\r\n  placeholder?: string;\r\n  required?: boolean;\r\n  disabled?: boolean;\r\n  error?: string;\r\n  helperText?: string;\r\n  options?: Option[];\r\n  rows?: number;\r\n  maxLength?: number;\r\n  variant?: 'default' | 'small';\r\n  className?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nconst FormInput: React.FC<FormInputProps> = ({\r\n  type = 'text',\r\n  children,\r\n  options,\r\n  rows,\r\n  ...props\r\n}) => {\r\n  switch (type) {\r\n    case 'textarea':\r\n      return <TextArea rows={rows} {...props} />;\r\n    \r\n    case 'select':\r\n      return (\r\n        <Select {...props}>\r\n          {options ? (\r\n            options.map((option) => (\r\n              <option key={option.value} value={option.value}>\r\n                {option.label}\r\n              </option>\r\n            ))\r\n          ) : (\r\n            children\r\n          )}\r\n        </Select>\r\n      );\r\n    \r\n    default:\r\n      return <TextInput type={type} {...props} />;\r\n  }\r\n};\r\n\r\nexport default FormInput;"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAgCA,MAAM,YAAsC,CAAC,EAC3C,OAAO,MAAM,EACb,QAAQ,EACR,OAAO,EACP,IAAI,EACJ,GAAG,OACJ;IACC,OAAQ;QACN,KAAK;YACH,qBAAO,8OAAC,uIAAA,CAAA,UAAQ;gBAAC,MAAM;gBAAO,GAAG,KAAK;;;;;;QAExC,KAAK;YACH,qBACE,8OAAC,qIAAA,CAAA,UAAM;gBAAE,GAAG,KAAK;0BACd,UACC,QAAQ,GAAG,CAAC,CAAC,uBACX,8OAAC;wBAA0B,OAAO,OAAO,KAAK;kCAC3C,OAAO,KAAK;uBADF,OAAO,KAAK;;;;gCAK3B;;;;;;QAKR;YACE,qBAAO,8OAAC,wIAAA,CAAA,UAAS;gBAAC,MAAM;gBAAO,GAAG,KAAK;;;;;;IAC3C;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1953, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/forms/index.ts"], "sourcesContent": ["export { default as FileUpload } from './FileUpload';\r\nexport { default as FormField } from './FormField';\r\nexport { default as ProgressIndicator } from './ProgressIndicator';\r\nexport { default as CountryDropdown } from './CountryDropdown';\r\nexport { default as TextInput } from './TextInput';\r\nexport { default as TextArea } from './TextArea';\r\nexport { default as Select } from './Select';\r\nexport { default as FormInput } from './FormInput';"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/lib/customer-api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosResponse, AxiosError } from 'axios';\r\nimport Cookies from 'js-cookie';\r\n\r\n// API Configuration\r\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001';\r\n\r\n// Create axios instance for customer portal (same as staff portal)\r\nconst customerApiClient: AxiosInstance = axios.create({\r\n  baseURL: API_BASE_URL,\r\n  timeout: 15000, // Increased timeout for better reliability\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Create auth-specific client (same as staff portal)\r\nconst customerAuthApiClient: AxiosInstance = axios.create({\r\n  baseURL: `${API_BASE_URL}/auth`,\r\n  timeout: 15000, // Increased timeout for better reliability\r\n  headers: {\r\n    'Content-Type': 'application/json',\r\n    'Accept': 'application/json',\r\n  },\r\n});\r\n\r\n// Add debug logging to auth client (only in development)\r\ncustomerAuthApiClient.interceptors.request.use(\r\n  (config) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Request:', {\r\n        url: `${config.baseURL}${config.url}`,\r\n        method: config.method,\r\n        headers: config.headers,\r\n        data: config.data\r\n      });\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    console.error('Customer Auth API Request Error:', error);\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\ncustomerAuthApiClient.interceptors.response.use(\r\n  (response) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.log('Customer Auth API Response Success:', {\r\n        status: response.status,\r\n        statusText: response.statusText,\r\n        url: response.config.url\r\n      });\r\n    }\r\n    return response;\r\n  },\r\n  (error) => {\r\n    if (process.env.NODE_ENV === 'development') {\r\n      console.error('Customer Auth API Interceptor Error:', {\r\n        message: error?.message || 'Unknown error',\r\n        code: error?.code || 'NO_CODE',\r\n        status: error?.response?.status || 'NO_STATUS',\r\n        statusText: error?.response?.statusText || 'NO_STATUS_TEXT',\r\n        url: error?.config?.url || 'NO_URL',\r\n        method: error?.config?.method || 'NO_METHOD',\r\n        baseURL: error?.config?.baseURL || 'NO_BASE_URL',\r\n        isAxiosError: error?.isAxiosError || false,\r\n        responseData: error?.response?.data || 'NO_RESPONSE_DATA',\r\n        requestData: error?.config?.data || 'NO_REQUEST_DATA',\r\n        headers: error?.config?.headers || 'NO_HEADERS'\r\n      });\r\n    }\r\n\r\n    // Don't handle 401 here, let the login method handle it\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Request interceptor to add auth token\r\ncustomerApiClient.interceptors.request.use(\r\n  (config) => {\r\n    const token = Cookies.get('auth_token');\r\n    if (token) {\r\n      config.headers.Authorization = `Bearer ${token}`;\r\n    }\r\n    return config;\r\n  },\r\n  (error) => {\r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// Response interceptor for error handling with retry logic\r\ncustomerApiClient.interceptors.response.use(\r\n  (response: AxiosResponse) => {\r\n    return response;\r\n  },\r\n  async (error: AxiosError) => {\r\n    const originalRequest = error.config as AxiosError['config'] & {\r\n      _retry?: boolean;\r\n      _retryCount?: number;\r\n    };\r\n\r\n    // Handle 429 Rate Limiting\r\n    if (error.response?.status === 429) {\r\n      if (!originalRequest._retry) {\r\n        originalRequest._retry = true;\r\n        \r\n        // Get retry delay from headers or use exponential backoff\r\n        const retryAfter = error.response.headers['retry-after'];\r\n        const delay = retryAfter ? parseInt(retryAfter) * 1000 : Math.min(1000 * Math.pow(2, originalRequest._retryCount || 0), 10000);\r\n        \r\n        originalRequest._retryCount = (originalRequest._retryCount || 0) + 1;\r\n        \r\n        // Don't retry more than 3 times\r\n        if (originalRequest._retryCount <= 3) {\r\n          console.warn(`Rate limited. Retrying after ${delay}ms (attempt ${originalRequest._retryCount})`);\r\n          \r\n          await new Promise(resolve => setTimeout(resolve, delay));\r\n          return customerApiClient(originalRequest);\r\n        }\r\n      }\r\n    }\r\n\r\n    // Handle 401 Unauthorized\r\n    if (error.response?.status === 401) {\r\n      // Clear auth token and redirect to login\r\n      Cookies.remove('auth_token');\r\n      Cookies.remove('auth_user');\r\n      window.location.href = '/auth/login';\r\n    }\r\n    \r\n    return Promise.reject(error);\r\n  }\r\n);\r\n\r\n// API Service Class\r\nexport class CustomerApiService {\r\n  public api: AxiosInstance;\r\n  private pendingRequests: Map<string, Promise<unknown>> = new Map();\r\n\r\n  constructor() {\r\n    this.api = customerApiClient;\r\n  }\r\n\r\n  // Request deduplication helper\r\n  private async deduplicateRequest<T>(key: string, requestFn: () => Promise<T>): Promise<T> {\r\n    if (this.pendingRequests.has(key)) {\r\n      return this.pendingRequests.get(key) as Promise<T>;\r\n    }\r\n\r\n    const promise = requestFn().finally(() => {\r\n      this.pendingRequests.delete(key);\r\n    });\r\n\r\n    this.pendingRequests.set(key, promise);\r\n    return promise;\r\n  }\r\n\r\n  // Set auth token\r\n  setAuthToken(token: string) {\r\n    this.api.defaults.headers.common['Authorization'] = `Bearer ${token}`;\r\n  }\r\n\r\n  // Remove auth token\r\n  removeAuthToken() {\r\n    delete this.api.defaults.headers.common['Authorization'];\r\n  }\r\n\r\n  // // Authentication endpoints (copied from working staff portal)\r\n  // async login(credentials: { email: string; password: string }) {\r\n  //   try {\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Starting login request with credentials:', { email: credentials.email });\r\n  //       console.log('CustomerAPI: API Base URL:', API_BASE_URL);\r\n  //       console.log('CustomerAPI: Full request URL:', `${API_BASE_URL}/auth/login`);\r\n  //       console.log('CustomerAPI: Request payload:', { email: credentials.email, password: '***' });\r\n  //     }\r\n      \r\n  //     // Make the request\r\n  //     const response = await customerAuthApiClient.post('/login', credentials);\r\n\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Raw response received:', {\r\n  //         status: response.status,\r\n  //         statusText: response.statusText,\r\n  //         headers: response.headers,\r\n  //         data: response.data,\r\n  //         dataType: typeof response.data,\r\n  //         dataKeys: response.data ? Object.keys(response.data) : 'NO_DATA'\r\n  //       });\r\n  //     }\r\n\r\n  //     // Check if the response has the expected structure\r\n  //     // Backend returns data wrapped in a response envelope: { success, message, data, timestamp, path, statusCode }\r\n  //     if (!response.data || !response.data.data) {\r\n  //       console.error('CustomerAPI: Invalid response structure:', response.data);\r\n  //       throw new Error('Invalid response from server');\r\n  //     }\r\n\r\n  //     const authData = response.data.data;\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Extracted auth data:', {\r\n  //         authData: authData,\r\n  //         authDataType: typeof authData,\r\n  //         authDataKeys: authData ? Object.keys(authData) : 'NO_AUTH_DATA',\r\n  //         hasAccessToken: authData ? 'access_token' in authData : false,\r\n  //         hasUser: authData ? 'user' in authData : false,\r\n  //         accessTokenValue: authData?.access_token,\r\n  //         userValue: authData?.user\r\n  //       });\r\n  //     }\r\n\r\n  //     // Check if the auth data is empty (which indicates an error)\r\n  //     if (!authData || Object.keys(authData).length === 0) {\r\n  //       console.error('CustomerAPI: Empty auth data received');\r\n  //       throw new Error('Authentication failed - invalid credentials');\r\n  //     }\r\n\r\n  //     // Validate that we have the required fields\r\n  //     if (!authData.access_token || !authData.user) {\r\n  //       console.error('CustomerAPI: Missing required fields in response:', {\r\n  //         authData: authData,\r\n  //         hasToken: !!authData.access_token,\r\n  //         hasUser: !!authData.user,\r\n  //         tokenValue: authData.access_token,\r\n  //         userValue: authData.user,\r\n  //         allKeys: Object.keys(authData || {})\r\n  //       });\r\n  //       throw new Error('Authentication failed - incomplete response');\r\n  //     }\r\n\r\n  //     // Map backend field names to frontend expected format\r\n  //     // Backend response structure: { access_token, user: { user_id, first_name, last_name, email, roles, ... } }\r\n  //     const mappedAuthData = {\r\n  //       access_token: authData.access_token,\r\n  //       user: {\r\n  //         id: authData.user.user_id,\r\n  //         firstName: authData.user.first_name,\r\n  //         lastName: authData.user.last_name,\r\n  //         email: authData.user.email,\r\n  //         roles: authData.user.roles || [],\r\n  //         isAdmin: (authData.user.roles || []).includes('administrator'),\r\n  //         profileImage: authData.user.profile_image,\r\n  //         createdAt: authData.user.created_at || new Date().toISOString(),\r\n  //         lastLogin: authData.user.last_login,\r\n  //         organizationName: authData.user.organization_name,\r\n  //         two_factor_enabled: authData.user.two_factor_enabled\r\n  //       }\r\n  //     };\r\n\r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.log('CustomerAPI: Mapped auth data:', mappedAuthData);\r\n  //     }\r\n  //     return mappedAuthData;\r\n  //   } catch (error) {\r\n  //     const isAxiosError = error && typeof error === 'object' && 'isAxiosError' in error;\r\n  //     const axiosError = isAxiosError ? error as AxiosError : null;\r\n      \r\n  //     if (process.env.NODE_ENV === 'development') {\r\n  //       console.error('CustomerAPI: Login error details:', {\r\n  //         error: error,\r\n  //         errorType: typeof error,\r\n  //         errorConstructor: error?.constructor?.name,\r\n  //         errorMessage: error instanceof Error ? error.message : String(error),\r\n  //         errorStack: error instanceof Error ? error.stack : undefined,\r\n  //         isAxiosError: isAxiosError,\r\n  //         responseStatus: axiosError?.response?.status,\r\n  //         responseStatusText: axiosError?.response?.statusText,\r\n  //         responseData: axiosError?.response?.data,\r\n  //         requestURL: axiosError?.config?.url,\r\n  //         requestMethod: axiosError?.config?.method,\r\n  //         requestData: axiosError?.config?.data,\r\n  //         requestHeaders: axiosError?.config?.headers\r\n  //       });\r\n  //     }\r\n  //     throw error;\r\n  //   }\r\n  // }\r\n\r\n  // async register(userData: {\r\n  //   firstName: string;\r\n  //   lastName: string;\r\n  //   email: string;\r\n  //   password: string;\r\n  //   organizationName?: string;\r\n  // }) {\r\n  //   // Map frontend field names to backend expected format\r\n  //   const backendUserData = {\r\n  //     first_name: userData.firstName,\r\n  //     last_name: userData.lastName,\r\n  //     email: userData.email,\r\n  //     password: userData.password,\r\n  //     organization_name: userData.organizationName\r\n  //   };\r\n\r\n  //   const response = await customerAuthApiClient.post('/register', backendUserData);\r\n\r\n  //   // Handle response structure consistently with login\r\n  //   if (response.data?.data) {\r\n  //     const authData = response.data.data;\r\n      \r\n  //     // Map backend field names to frontend expected format\r\n  //     const mappedAuthData = {\r\n  //       access_token: authData.access_token,\r\n  //       user: {\r\n  //         id: authData.user.user_id,\r\n  //         firstName: authData.user.first_name,\r\n  //         lastName: authData.user.last_name,\r\n  //         email: authData.user.email,\r\n  //         roles: authData.user.roles || [],\r\n  //         isAdmin: (authData.user.roles || []).includes('administrator'),\r\n  //         profileImage: authData.user.profile_image,\r\n  //         createdAt: authData.user.created_at || new Date().toISOString(),\r\n  //         lastLogin: authData.user.last_login,\r\n  //         organizationName: authData.user.organization_name\r\n  //       }\r\n  //     };\r\n      \r\n  //     return mappedAuthData;\r\n  //   }\r\n\r\n  //   // If no nested data property, return direct response\r\n  //   return response.data;\r\n  // }\r\n\r\n  async logout() {\r\n    const response = await customerAuthApiClient.post('/logout');\r\n    return response.data;\r\n  }\r\n\r\n  async refreshToken() {\r\n    const response = await customerAuthApiClient.post('/refresh');\r\n    return response.data;\r\n  }\r\n\r\n  // 2FA endpoints\r\n  async generateTwoFactorCode(userId: string, action: string) {\r\n    const response = await customerAuthApiClient.post('/generate-2fa', { user_id: userId, action });\r\n    return response.data;\r\n  }\r\n\r\n  async verify2FA(data: { user_id: string; code: string; unique: string }) {\r\n    const response = await customerAuthApiClient.post('/verify-2fa', data);\r\n\r\n    // Handle response structure consistently with login\r\n    if (response.data?.data) {\r\n      const authData = response.data.data;\r\n      \r\n      // Map backend field names to frontend expected format\r\n      const mappedAuthData = {\r\n        access_token: authData.access_token,\r\n        user: {\r\n          id: authData.user.user_id,\r\n          firstName: authData.user.first_name,\r\n          lastName: authData.user.last_name,\r\n          email: authData.user.email,\r\n          roles: authData.user.roles || [],\r\n          isAdmin: (authData.user.roles || []).includes('administrator'),\r\n          profileImage: authData.user.profile_image,\r\n          createdAt: authData.user.created_at || new Date().toISOString(),\r\n          lastLogin: authData.user.last_login,\r\n          organizationName: authData.user.organization_name,\r\n          two_factor_enabled: authData.user.two_factor_enabled\r\n        }\r\n      };\r\n      \r\n      return mappedAuthData;\r\n    }\r\n\r\n    return response.data;\r\n  }\r\n\r\n  async setupTwoFactorAuth(data: { access_token: string; user_id: string }) {\r\n    const response = await customerAuthApiClient.post('/setup-2fa', data);\r\n    return response.data;\r\n  }\r\n\r\n  // User profile endpoints\r\n  async getProfile() {\r\n    return this.deduplicateRequest('getProfile', async () => {\r\n      const response = await this.api.get('/users/profile');\r\n      return response.data;\r\n    });\r\n  }\r\n\r\n  async updateProfile(profileData: ProfileUpdateData) {\r\n    const response = await this.api.put('/users/profile', profileData);\r\n    return response.data;\r\n  }\r\n\r\n  // Addressing endpoints\r\n  async getAddresses() {\r\n    const response = await this.api.get('/address/all');\r\n    return response.data;\r\n  }\r\n\r\n  async createAddress(addressData: CreateAddressData) {\r\n    const response = await this.api.post('/address/create', addressData);\r\n    return response.data;\r\n  }\r\n\r\n  async getAddress(id: string) {\r\n    const response = await this.api.get(`/address/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  async editAddress(addressData: EditAddressData) {\r\n    const response = await this.api.put('/address/edit', addressData);\r\n    return response.data;\r\n  }\r\n\r\n  async deleteAddress(id: string) {\r\n    const response = await this.api.delete(`/address/soft/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  async searchPostcodes(searchParams: SearchPostcodes) {\r\n    const response = await this.api.post('/postal-codes/search', searchParams);\r\n    return response.data;\r\n  }\r\n\r\n  // License endpoints\r\n  async getLicenses(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/licenses', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async getLicense(id: string) {\r\n    const response = await this.api.get(`/licenses/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  async createLicenseApplication(applicationData: LicenseApplicationData) {\r\n    const response = await this.api.post('/license-applications', applicationData);\r\n    return response.data;\r\n  }\r\n\r\n  // License Types endpoints\r\n  async getLicenseTypes(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-types', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async getLicenseType(id: string) {\r\n    const response = await this.api.get(`/license-types/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  // License Categories endpoints\r\n  async getLicenseCategories(params?: { page?: number; limit?: number }) {\r\n    const response = await this.api.get('/license-categories', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async getLicenseCategoriesByType(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/by-license-type/${licenseTypeId}`);\r\n    return response.data;\r\n  }\r\n\r\n  async getLicenseCategoryTree(licenseTypeId: string) {\r\n    const response = await this.api.get(`/license-categories/license-type/${licenseTypeId}/tree`);\r\n    return response.data;\r\n  }\r\n\r\n  async getLicenseCategory(id: string) {\r\n    const response = await this.api.get(`/license-categories/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  // Application endpoints\r\n  async getApplications(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/applications', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async getApplication(id: string) {\r\n    const response = await this.api.get(`/applications/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  async createApplication(applicationData: any) {\r\n    const response = await this.api.post('/applications', applicationData);\r\n    return response.data;\r\n  }\r\n\r\n  async updateApplication(id: string, applicationData: Partial<LicenseApplicationData>) {\r\n    const response = await this.api.put(`/applications/${id}`, applicationData);\r\n    return response.data;\r\n  }\r\n\r\n  // Payment endpoints\r\n  async getPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/payments', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async getPayment(id: string) {\r\n    const response = await this.api.get(`/payments/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  async createPayment(paymentData: PaymentCreateData) {\r\n    const response = await this.api.post('/payments', paymentData);\r\n    return response.data;\r\n  }\r\n\r\n  // Document endpoints\r\n  async getDocuments(params?: { type?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/documents', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async uploadDocument(formData: FormData) {\r\n    const response = await this.api.post('/documents/upload', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return response.data;\r\n  }\r\n\r\n  async downloadDocument(id: string) {\r\n    const response = await this.api.get(`/documents/${id}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return response.data;\r\n  }\r\n\r\n  // Dashboard statistics\r\n  async getDashboardStats() {\r\n    const response = await this.api.get('/dashboard/stats');\r\n    return response.data;\r\n  }\r\n\r\n  // Notifications\r\n  async getNotifications(params?: { read?: boolean; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/notifications', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async markNotificationAsRead(id: string) {\r\n    const response = await this.api.patch(`/notifications/${id}/read`);\r\n    return response.data;\r\n  }\r\n\r\n  // Procurement endpoints\r\n  async getTenders(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/tenders', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async getTender(id: string) {\r\n    const response = await this.api.get(`/procurement/tenders/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  async payForTenderAccess(tenderId: string, paymentData: TenderPaymentData) {\r\n    const response = await this.api.post(`/procurement/tenders/${tenderId}/pay-access`, paymentData);\r\n    return response.data;\r\n  }\r\n\r\n  async downloadTenderDocument(documentId: string) {\r\n    const response = await this.api.get(`/procurement/documents/${documentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return response.data;\r\n  }\r\n\r\n  async getMyBids(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/my-bids', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async getBid(id: string) {\r\n    const response = await this.api.get(`/procurement/bids/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  async submitBid(formData: FormData) {\r\n    const response = await this.api.post('/procurement/bids', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return response.data;\r\n  }\r\n\r\n  async updateBid(id: string, formData: FormData) {\r\n    const response = await this.api.put(`/procurement/bids/${id}`, formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return response.data;\r\n  }\r\n\r\n  async getProcurementPayments(params?: { status?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/procurement/payments', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async getProcurementPayment(id: string) {\r\n    const response = await this.api.get(`/procurement/payments/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  // Consumer Affairs endpoints\r\n  async getComplaints(params?: { status?: string; category?: string; page?: number; limit?: number }) {\r\n    const response = await this.api.get('/consumer-affairs/complaints', { params });\r\n    return response.data;\r\n  }\r\n\r\n  async getComplaint(id: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${id}`);\r\n    return response.data;\r\n  }\r\n\r\n  async submitComplaint(complaintData: ComplaintData) {\r\n    const formData = new FormData();\r\n    formData.append('title', complaintData.title);\r\n    formData.append('description', complaintData.description);\r\n    formData.append('category', complaintData.category);\r\n\r\n    if (complaintData.attachments) {\r\n      complaintData.attachments.forEach((file, index) => {\r\n        formData.append(`attachments[${index}]`, file);\r\n      });\r\n    }\r\n\r\n    const response = await this.api.post('/consumer-affairs/complaints', formData, {\r\n      headers: {\r\n        'Content-Type': 'multipart/form-data',\r\n      },\r\n    });\r\n    return response.data;\r\n  }\r\n\r\n  async updateComplaint(id: string, updates: Partial<ComplaintData>) {\r\n    const response = await this.api.put(`/consumer-affairs/complaints/${id}`, updates);\r\n    return response.data;\r\n  }\r\n\r\n  async downloadComplaintAttachment(complaintId: string, attachmentId: string) {\r\n    const response = await this.api.get(`/consumer-affairs/complaints/${complaintId}/attachments/${attachmentId}/download`, {\r\n      responseType: 'blob',\r\n    });\r\n    return response.data;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const customerApi = new CustomerApiService();\r\n\r\n// Export axios instance for direct use if needed\r\nexport { customerApiClient };\r\n\r\n// Export types\r\nexport interface ApiResponse<T = unknown> {\r\n  success: boolean;\r\n  data: T;\r\n  message?: string;\r\n  errors?: string[] | { [key: string]: string[] } | string;\r\n}\r\n\r\nexport interface PaginatedResponse<T = unknown> {\r\n  data: T[];\r\n  total: number;\r\n  page: number;\r\n  limit: number;\r\n  totalPages: number;\r\n}\r\n\r\nexport interface License {\r\n  id: string;\r\n  licenseNumber: string;\r\n  type: string;\r\n  status: 'active' | 'expired' | 'suspended' | 'pending';\r\n  issueDate: string;\r\n  expirationDate: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Application {\r\n  id: string;\r\n  applicationNumber: string;\r\n  type: string;\r\n  status: 'draft' | 'submitted' | 'under_review' | 'approved' | 'rejected';\r\n  submittedDate: string;\r\n  lastUpdated: string;\r\n  organizationName: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface Payment {\r\n  id: string;\r\n  invoiceNumber: string;\r\n  amount: number;\r\n  currency: string;\r\n  status: 'pending' | 'paid' | 'overdue' | 'cancelled';\r\n  dueDate: string;\r\n  paidDate?: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface User {\r\n  id: string;\r\n  firstName: string;\r\n  lastName: string;\r\n  email: string;\r\n  organizationName?: string;\r\n  roles: string[];\r\n  isAdmin: boolean;\r\n  profileImage?: string;\r\n  createdAt: string;\r\n  lastLogin?: string;\r\n  phone?: string;\r\n  address?: string;\r\n  city?: string;\r\n  country?: string;\r\n  two_factor_enabled?: boolean;\r\n}\r\n\r\nexport interface ProfileUpdateData {\r\n  firstName?: string;\r\n  lastName?: string;\r\n  email?: string;\r\n  organizationName?: string;\r\n  profileImage?: string;\r\n}\r\n\r\nexport interface CreateAddressData {\r\n  address_type: string;\r\n  address_origin: string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n}\r\n\r\nexport interface EditAddressData {\r\n  address_id: string;\r\n  address_type?: string;\r\n  address_line_1?: string;\r\n  address_line_2?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  city?: string;\r\n}\r\n\r\nexport interface SearchPostcodes {\r\n  region?: string;\r\n  district?: string;\r\n  location?: string;\r\n  postal_code?: string;\r\n}\r\n\r\nexport interface PostalCodeLookupResult {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\n\r\nexport interface LicenseApplicationData {\r\n  type: string;\r\n  organizationName: string;\r\n  description?: string;\r\n  contactEmail?: string;\r\n  contactPhone?: string;\r\n  businessAddress?: string;\r\n  businessType?: string;\r\n  requestedStartDate?: string;\r\n  additionalDocuments?: string[];\r\n  notes?: string;\r\n}\r\n\r\nexport interface PaymentCreateData {\r\n  amount: number;\r\n  currency: string;\r\n  dueDate: string;\r\n  issueDate: string;\r\n  description: string;\r\n  paymentType: string;\r\n  clientName: string;\r\n  clientEmail: string;\r\n  paymentMethod?: string;\r\n  notes?: string;\r\n  relatedLicense?: string;\r\n  relatedApplication?: string;\r\n}\r\n\r\nexport interface TenderPaymentData {\r\n  amount: number;\r\n  currency: string;\r\n  paymentMethod: string;\r\n  description?: string;\r\n}\r\n\r\nexport interface ComplaintData {\r\n  title: string;\r\n  description: string;\r\n  category: string;\r\n  attachments?: File[];\r\n}\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;AAEA,oBAAoB;AACpB,MAAM,eAAe,6DAAmC;AAExD,mEAAmE;AACnE,MAAM,oBAAmC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACpD,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,qDAAqD;AACrD,MAAM,wBAAuC,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IACxD,SAAS,GAAG,aAAa,KAAK,CAAC;IAC/B,SAAS;IACT,SAAS;QACP,gBAAgB;QAChB,UAAU;IACZ;AACF;AAEA,yDAAyD;AACzD,sBAAsB,YAAY,CAAC,OAAO,CAAC,GAAG,CAC5C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,8BAA8B;YACxC,KAAK,GAAG,OAAO,OAAO,GAAG,OAAO,GAAG,EAAE;YACrC,QAAQ,OAAO,MAAM;YACrB,SAAS,OAAO,OAAO;YACvB,MAAM,OAAO,IAAI;QACnB;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,QAAQ,KAAK,CAAC,oCAAoC;IAClD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,sBAAsB,YAAY,CAAC,QAAQ,CAAC,GAAG,CAC7C,CAAC;IACC,wCAA4C;QAC1C,QAAQ,GAAG,CAAC,uCAAuC;YACjD,QAAQ,SAAS,MAAM;YACvB,YAAY,SAAS,UAAU;YAC/B,KAAK,SAAS,MAAM,CAAC,GAAG;QAC1B;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,wCAA4C;QAC1C,QAAQ,KAAK,CAAC,wCAAwC;YACpD,SAAS,OAAO,WAAW;YAC3B,MAAM,OAAO,QAAQ;YACrB,QAAQ,OAAO,UAAU,UAAU;YACnC,YAAY,OAAO,UAAU,cAAc;YAC3C,KAAK,OAAO,QAAQ,OAAO;YAC3B,QAAQ,OAAO,QAAQ,UAAU;YACjC,SAAS,OAAO,QAAQ,WAAW;YACnC,cAAc,OAAO,gBAAgB;YACrC,cAAc,OAAO,UAAU,QAAQ;YACvC,aAAa,OAAO,QAAQ,QAAQ;YACpC,SAAS,OAAO,QAAQ,WAAW;QACrC;IACF;IAEA,wDAAwD;IACxD,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,wCAAwC;AACxC,kBAAkB,YAAY,CAAC,OAAO,CAAC,GAAG,CACxC,CAAC;IACC,MAAM,QAAQ,qJAAA,CAAA,UAAO,CAAC,GAAG,CAAC;IAC1B,IAAI,OAAO;QACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;IAClD;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,2DAA2D;AAC3D,kBAAkB,YAAY,CAAC,QAAQ,CAAC,GAAG,CACzC,CAAC;IACC,OAAO;AACT,GACA,OAAO;IACL,MAAM,kBAAkB,MAAM,MAAM;IAKpC,2BAA2B;IAC3B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B,gBAAgB,MAAM,GAAG;YAEzB,0DAA0D;YAC1D,MAAM,aAAa,MAAM,QAAQ,CAAC,OAAO,CAAC,cAAc;YACxD,MAAM,QAAQ,aAAa,SAAS,cAAc,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK,GAAG,CAAC,GAAG,gBAAgB,WAAW,IAAI,IAAI;YAExH,gBAAgB,WAAW,GAAG,CAAC,gBAAgB,WAAW,IAAI,CAAC,IAAI;YAEnE,gCAAgC;YAChC,IAAI,gBAAgB,WAAW,IAAI,GAAG;gBACpC,QAAQ,IAAI,CAAC,CAAC,6BAA6B,EAAE,MAAM,YAAY,EAAE,gBAAgB,WAAW,CAAC,CAAC,CAAC;gBAE/F,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;gBACjD,OAAO,kBAAkB;YAC3B;QACF;IACF;IAEA,0BAA0B;IAC1B,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;QAClC,yCAAyC;QACzC,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,qJAAA,CAAA,UAAO,CAAC,MAAM,CAAC;QACf,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAIK,MAAM;IACJ,IAAmB;IAClB,kBAAiD,IAAI,MAAM;IAEnE,aAAc;QACZ,IAAI,CAAC,GAAG,GAAG;IACb;IAEA,+BAA+B;IAC/B,MAAc,mBAAsB,GAAW,EAAE,SAA2B,EAAc;QACxF,IAAI,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM;YACjC,OAAO,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC;QAClC;QAEA,MAAM,UAAU,YAAY,OAAO,CAAC;YAClC,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAC9B;QAEA,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,KAAK;QAC9B,OAAO;IACT;IAEA,iBAAiB;IACjB,aAAa,KAAa,EAAE;QAC1B,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB,GAAG,CAAC,OAAO,EAAE,OAAO;IACvE;IAEA,oBAAoB;IACpB,kBAAkB;QAChB,OAAO,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,OAAO,CAAC,MAAM,CAAC,gBAAgB;IAC1D;IAEA,iEAAiE;IACjE,kEAAkE;IAClE,UAAU;IACV,oDAAoD;IACpD,4GAA4G;IAC5G,iEAAiE;IACjE,qFAAqF;IACrF,qGAAqG;IACrG,QAAQ;IAER,0BAA0B;IAC1B,gFAAgF;IAEhF,oDAAoD;IACpD,6DAA6D;IAC7D,mCAAmC;IACnC,2CAA2C;IAC3C,qCAAqC;IACrC,+BAA+B;IAC/B,0CAA0C;IAC1C,2EAA2E;IAC3E,YAAY;IACZ,QAAQ;IAER,0DAA0D;IAC1D,sHAAsH;IACtH,mDAAmD;IACnD,kFAAkF;IAClF,yDAAyD;IACzD,QAAQ;IAER,2CAA2C;IAC3C,oDAAoD;IACpD,2DAA2D;IAC3D,8BAA8B;IAC9B,yCAAyC;IACzC,2EAA2E;IAC3E,yEAAyE;IACzE,0DAA0D;IAC1D,oDAAoD;IACpD,oCAAoC;IACpC,YAAY;IACZ,QAAQ;IAER,oEAAoE;IACpE,6DAA6D;IAC7D,gEAAgE;IAChE,wEAAwE;IACxE,QAAQ;IAER,mDAAmD;IACnD,sDAAsD;IACtD,6EAA6E;IAC7E,8BAA8B;IAC9B,6CAA6C;IAC7C,oCAAoC;IACpC,6CAA6C;IAC7C,oCAAoC;IACpC,+CAA+C;IAC/C,YAAY;IACZ,wEAAwE;IACxE,QAAQ;IAER,6DAA6D;IAC7D,mHAAmH;IACnH,+BAA+B;IAC/B,6CAA6C;IAC7C,gBAAgB;IAChB,qCAAqC;IACrC,+CAA+C;IAC/C,6CAA6C;IAC7C,sCAAsC;IACtC,4CAA4C;IAC5C,0EAA0E;IAC1E,qDAAqD;IACrD,2EAA2E;IAC3E,+CAA+C;IAC/C,6DAA6D;IAC7D,+DAA+D;IAC/D,UAAU;IACV,SAAS;IAET,oDAAoD;IACpD,uEAAuE;IACvE,QAAQ;IACR,6BAA6B;IAC7B,sBAAsB;IACtB,0FAA0F;IAC1F,oEAAoE;IAEpE,oDAAoD;IACpD,6DAA6D;IAC7D,wBAAwB;IACxB,mCAAmC;IACnC,sDAAsD;IACtD,gFAAgF;IAChF,wEAAwE;IACxE,sCAAsC;IACtC,wDAAwD;IACxD,gEAAgE;IAChE,oDAAoD;IACpD,+CAA+C;IAC/C,qDAAqD;IACrD,iDAAiD;IACjD,sDAAsD;IACtD,YAAY;IACZ,QAAQ;IACR,mBAAmB;IACnB,MAAM;IACN,IAAI;IAEJ,6BAA6B;IAC7B,uBAAuB;IACvB,sBAAsB;IACtB,mBAAmB;IACnB,sBAAsB;IACtB,+BAA+B;IAC/B,OAAO;IACP,2DAA2D;IAC3D,8BAA8B;IAC9B,sCAAsC;IACtC,oCAAoC;IACpC,6BAA6B;IAC7B,mCAAmC;IACnC,mDAAmD;IACnD,OAAO;IAEP,qFAAqF;IAErF,yDAAyD;IACzD,+BAA+B;IAC/B,2CAA2C;IAE3C,6DAA6D;IAC7D,+BAA+B;IAC/B,6CAA6C;IAC7C,gBAAgB;IAChB,qCAAqC;IACrC,+CAA+C;IAC/C,6CAA6C;IAC7C,sCAAsC;IACtC,4CAA4C;IAC5C,0EAA0E;IAC1E,qDAAqD;IACrD,2EAA2E;IAC3E,+CAA+C;IAC/C,4DAA4D;IAC5D,UAAU;IACV,SAAS;IAET,6BAA6B;IAC7B,MAAM;IAEN,0DAA0D;IAC1D,0BAA0B;IAC1B,IAAI;IAEJ,MAAM,SAAS;QACb,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC;QAClD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,MAAM,sBAAsB,MAAc,EAAE,MAAc,EAAE;QAC1D,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,iBAAiB;YAAE,SAAS;YAAQ;QAAO;QAC7F,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,UAAU,IAAuD,EAAE;QACvE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,eAAe;QAEjE,oDAAoD;QACpD,IAAI,SAAS,IAAI,EAAE,MAAM;YACvB,MAAM,WAAW,SAAS,IAAI,CAAC,IAAI;YAEnC,sDAAsD;YACtD,MAAM,iBAAiB;gBACrB,cAAc,SAAS,YAAY;gBACnC,MAAM;oBACJ,IAAI,SAAS,IAAI,CAAC,OAAO;oBACzB,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,UAAU,SAAS,IAAI,CAAC,SAAS;oBACjC,OAAO,SAAS,IAAI,CAAC,KAAK;oBAC1B,OAAO,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE;oBAChC,SAAS,CAAC,SAAS,IAAI,CAAC,KAAK,IAAI,EAAE,EAAE,QAAQ,CAAC;oBAC9C,cAAc,SAAS,IAAI,CAAC,aAAa;oBACzC,WAAW,SAAS,IAAI,CAAC,UAAU,IAAI,IAAI,OAAO,WAAW;oBAC7D,WAAW,SAAS,IAAI,CAAC,UAAU;oBACnC,kBAAkB,SAAS,IAAI,CAAC,iBAAiB;oBACjD,oBAAoB,SAAS,IAAI,CAAC,kBAAkB;gBACtD;YACF;YAEA,OAAO;QACT;QAEA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAmB,IAA+C,EAAE;QACxE,MAAM,WAAW,MAAM,sBAAsB,IAAI,CAAC,cAAc;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,aAAa;QACjB,OAAO,IAAI,CAAC,kBAAkB,CAAC,cAAc;YAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;YACpC,OAAO,SAAS,IAAI;QACtB;IACF;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,MAAM,eAAe;QACnB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,mBAAmB;QACxD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,SAAS,EAAE,IAAI;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,YAAY,WAA4B,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,gBAAgB,YAA6B,EAAE;QACnD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,wBAAwB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,yBAAyB,eAAuC,EAAE;QACtE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,yBAAyB;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,MAA0C,EAAE;QAChE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,eAAe,EAAE,IAAI;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,MAAM,qBAAqB,MAA0C,EAAE;QACrE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,uBAAuB;YAAE;QAAO;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,2BAA2B,aAAqB,EAAE;QACtD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC1F,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,uBAAuB,aAAqB,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;QAC5F,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAmB,EAAU,EAAE;QACnC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,wBAAwB;IACxB,MAAM,gBAAgB,MAA2D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,iBAAiB;YAAE;QAAO;QAC9D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QACzD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,kBAAkB,eAAoB,EAAE;QAC5C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,iBAAiB;QACtD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,kBAAkB,EAAU,EAAE,eAAgD,EAAE;QACpF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,MAAM,YAAY,MAA2D,EAAE;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,aAAa;YAAE;QAAO;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,WAAW,EAAU,EAAE;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,UAAU,EAAE,IAAI;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,WAA8B,EAAE;QAClD,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,aAAa;QAClD,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,aAAa,MAAyD,EAAE;QAC5E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,cAAc;YAAE;QAAO;QAC3D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,eAAe,QAAkB,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,iBAAiB,EAAU,EAAE;QACjC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,GAAG,SAAS,CAAC,EAAE;YAC/D,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,uBAAuB;IACvB,MAAM,oBAAoB;QACxB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;QACpC,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,MAAM,iBAAiB,MAA0D,EAAE;QACjF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,kBAAkB;YAAE;QAAO;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,uBAAuB,EAAU,EAAE;QACvC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,wBAAwB;IACxB,MAAM,WAAW,MAA8E,EAAE;QAC/F,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,UAAU,EAAU,EAAE;QAC1B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,qBAAqB,EAAE,IAAI;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAmB,QAAgB,EAAE,WAA8B,EAAE;QACzE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,qBAAqB,EAAE,SAAS,WAAW,CAAC,EAAE;QACpF,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,uBAAuB,UAAkB,EAAE;QAC/C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,WAAW,SAAS,CAAC,EAAE;YACnF,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,UAAU,MAA2D,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,wBAAwB;YAAE;QAAO;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,OAAO,EAAU,EAAE;QACvB,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,UAAU,QAAkB,EAAE;QAClC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,qBAAqB,UAAU;YAClE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,UAAU,EAAU,EAAE,QAAkB,EAAE;QAC9C,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,kBAAkB,EAAE,IAAI,EAAE,UAAU;YACvE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,uBAAuB,MAA2D,EAAE;QACxF,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,yBAAyB;YAAE;QAAO;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,sBAAsB,EAAU,EAAE;QACtC,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,sBAAsB,EAAE,IAAI;QACjE,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,cAAc,MAA8E,EAAE;QAClG,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,gCAAgC;YAAE;QAAO;QAC7E,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAE;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,gBAAgB,aAA4B,EAAE;QAClD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS,cAAc,KAAK;QAC5C,SAAS,MAAM,CAAC,eAAe,cAAc,WAAW;QACxD,SAAS,MAAM,CAAC,YAAY,cAAc,QAAQ;QAElD,IAAI,cAAc,WAAW,EAAE;YAC7B,cAAc,WAAW,CAAC,OAAO,CAAC,CAAC,MAAM;gBACvC,SAAS,MAAM,CAAC,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,EAAE;YAC3C;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,gCAAgC,UAAU;YAC7E,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,gBAAgB,EAAU,EAAE,OAA+B,EAAE;QACjE,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,IAAI,EAAE;QAC1E,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,4BAA4B,WAAmB,EAAE,YAAoB,EAAE;QAC3E,MAAM,WAAW,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,aAAa,EAAE,aAAa,SAAS,CAAC,EAAE;YACtH,cAAc;QAChB;QACA,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,cAAc,IAAI", "debugId": null}}, {"offset": {"line": 2592, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/hooks/useAddressing.ts"], "sourcesContent": ["'use client';\r\n\r\nimport { customerApi } from '@/lib/customer-api';\r\nimport { useState, useEffect } from 'react';\r\nimport { toast } from 'react-hot-toast';\r\nimport { useDebouncedCallback } from 'use-debounce';\r\n\r\nexport interface CreateAddressData {\r\n  address_type: string;\r\n  address_origin: string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n}\r\n\r\nexport interface EditAddressData {\r\n  address_id: string;\r\n  address_type?: string;\r\n  address_line_1?: string;\r\n  address_line_2?: string;\r\n  postal_code?: string;\r\n  country?: string;\r\n  city?: string;\r\n}\r\n\r\nexport interface SearchPostcodes {\r\n  region?: string;\r\n  district?: string;\r\n  location?: string;\r\n  postal_code?: string;\r\n}\r\n\r\nexport interface PostalCodeLookupResult {\r\n  postal_code_id: string;\r\n  region: string;\r\n  district: string;\r\n  location: string;\r\n  postal_code: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n  deleted_at?: string | null;\r\n}\r\n\r\nexport const initialAddressData: CreateAddressData = {\r\n  address_type: 'postal',\r\n  address_origin: 'applicant',\r\n  address_line_1: '',\r\n  address_line_2: '',\r\n  postal_code: '',\r\n  country: '',\r\n  city: ''\r\n};\r\n\r\nexport interface Address {\r\n  address_id: string;\r\n  address_type: string;\r\n  address_origin: string;\r\n  address_line_1: string;\r\n  address_line_2?: string;\r\n  postal_code: string;\r\n  country: string;\r\n  city: string;\r\n  created_at: string;\r\n  updated_at: string;\r\n}\r\n\r\n\r\n// Address service using customer API\r\nconst addressService = {\r\n  async createAddress(data: CreateAddressData): Promise<any> {\r\n    const response = await customerApi.createAddress(data);\r\n    return response.data;\r\n  },\r\n\r\n  async getAddress(id: string): Promise<any> {\r\n    const response = await customerApi.getAddress(id);\r\n    return response.data;\r\n  },\r\n\r\n  async editAddress(data: EditAddressData): Promise<any> {\r\n    const response = await customerApi.editAddress(data);\r\n    return response.data;\r\n  },\r\n\r\n  \r\n  async searchPostcodes(searchParams: SearchPostcodes): Promise<any> {\r\n    const response = await customerApi.searchPostcodes(searchParams);\r\n    return response.data;\r\n  },\r\n\r\n}\r\n\r\n\r\n// Actual hook\r\nexport const useAddresses = (initialSearchParams?: SearchPostcodes) => {\r\n  const [addresses, setAddresses] = useState<Address[]>([]);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [searchParams, setSearchParams] = useState<SearchPostcodes>(initialSearchParams || {});\r\n\r\n  const [postcodeSuggestions, setPostcodeSuggestions] = useState<PostalCodeLookupResult[]>([]);\r\n  const [searching, setSearching] = useState(false);\r\n\r\n  // Fetch address list when searchParams change\r\n  useEffect(() => {\r\n    const fetchAddresses = async () => {\r\n      setLoading(true);\r\n      setError(null);\r\n      try {\r\n        const response = await addressService.searchPostcodes(searchParams);\r\n        setAddresses(response.data || []);\r\n      } catch (err: any) {\r\n        console.error('Address fetch error:', err);\r\n        setError(err.message || 'Failed to fetch addresses');\r\n        toast.error('Failed to fetch addresses');\r\n      } finally {\r\n        setLoading(false);\r\n      }\r\n    };\r\n\r\n    if (Object.keys(searchParams).length > 0) {\r\n      fetchAddresses();\r\n    }\r\n  }, [searchParams]);\r\n\r\n  // Postcode suggestions (live lookup, debounced)\r\n  const debouncedSearchPostcodes = useDebouncedCallback(async (params: SearchPostcodes) => {\r\n    setSearching(true);\r\n    try {\r\n      const response = await customerApi.searchPostcodes(params);\r\n      setPostcodeSuggestions(response.data || []);\r\n    } catch (err) {\r\n      console.error('Postcode search failed:', err);\r\n    } finally {\r\n      setSearching(false);\r\n    }\r\n  }, 500); // debounce for 500ms\r\n\r\n  // Manual search trigger to update addresses based on params\r\n  const searchAddresses = (params: SearchPostcodes) => {\r\n    setSearchParams(params);\r\n  };\r\n\r\n  // Create new address\r\n  const createAddress = async (data: CreateAddressData) => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const newAddress = await addressService.createAddress(data);\r\n      setAddresses(prev => [newAddress, ...prev]);\r\n      toast.success('Address created successfully');\r\n      return newAddress;\r\n    } catch (err: any) {\r\n      console.error('Address create error:', err);\r\n      setError(err.message || 'Failed to create address');\r\n      toast.error('Failed to create address');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Edit existing address\r\n  const editAddress = async (data: EditAddressData) => {\r\n    setLoading(true);\r\n    setError(null);\r\n    try {\r\n      const updatedAddress = await addressService.editAddress(data);\r\n      setAddresses(prev =>\r\n        prev.map(addr => (addr.address_id === data.address_id ? updatedAddress : addr))\r\n      );\r\n      toast.success('Address updated successfully');\r\n      return updatedAddress;\r\n    } catch (err: any) {\r\n      console.error('Address edit error:', err);\r\n      setError(err.message || 'Failed to update address');\r\n      toast.error('Failed to update address');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  return {\r\n    // State\r\n    addresses,\r\n    postcodeSuggestions,\r\n    searching,\r\n    loading,\r\n    error,\r\n    searchParams,\r\n\r\n    // Setters / Triggers\r\n    setSearchParams,\r\n    debouncedSearchPostcodes,\r\n    searchAddresses,\r\n\r\n    // CRUD\r\n    createAddress,\r\n    editAddress,\r\n\r\n    // Raw service (if needed)\r\n    addressService,\r\n  };\r\n};"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;AA6CO,MAAM,qBAAwC;IACnD,cAAc;IACd,gBAAgB;IAChB,gBAAgB;IAChB,gBAAgB;IAChB,aAAa;IACb,SAAS;IACT,MAAM;AACR;AAgBA,qCAAqC;AACrC,MAAM,iBAAiB;IACrB,MAAM,eAAc,IAAuB;QACzC,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,aAAa,CAAC;QACjD,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,YAAW,EAAU;QACzB,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,UAAU,CAAC;QAC9C,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAY,IAAqB;QACrC,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,WAAW,CAAC;QAC/C,OAAO,SAAS,IAAI;IACtB;IAGA,MAAM,iBAAgB,YAA6B;QACjD,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,eAAe,CAAC;QACnD,OAAO,SAAS,IAAI;IACtB;AAEF;AAIO,MAAM,eAAe,CAAC;IAC3B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACxD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,uBAAuB,CAAC;IAE1F,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA4B,EAAE;IAC3F,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,8CAA8C;IAC9C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,iBAAiB;YACrB,WAAW;YACX,SAAS;YACT,IAAI;gBACF,MAAM,WAAW,MAAM,eAAe,eAAe,CAAC;gBACtD,aAAa,SAAS,IAAI,IAAI,EAAE;YAClC,EAAE,OAAO,KAAU;gBACjB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,SAAS,IAAI,OAAO,IAAI;gBACxB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,OAAO,IAAI,CAAC,cAAc,MAAM,GAAG,GAAG;YACxC;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,gDAAgD;IAChD,MAAM,2BAA2B,CAAA,GAAA,0JAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO;QAC3D,aAAa;QACb,IAAI;YACF,MAAM,WAAW,MAAM,6HAAA,CAAA,cAAW,CAAC,eAAe,CAAC;YACnD,uBAAuB,SAAS,IAAI,IAAI,EAAE;QAC5C,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,2BAA2B;QAC3C,SAAU;YACR,aAAa;QACf;IACF,GAAG,MAAM,qBAAqB;IAE9B,4DAA4D;IAC5D,MAAM,kBAAkB,CAAC;QACvB,gBAAgB;IAClB;IAEA,qBAAqB;IACrB,MAAM,gBAAgB,OAAO;QAC3B,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,aAAa,MAAM,eAAe,aAAa,CAAC;YACtD,aAAa,CAAA,OAAQ;oBAAC;uBAAe;iBAAK;YAC1C,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,yBAAyB;YACvC,SAAS,IAAI,OAAO,IAAI;YACxB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,wBAAwB;IACxB,MAAM,cAAc,OAAO;QACzB,WAAW;QACX,SAAS;QACT,IAAI;YACF,MAAM,iBAAiB,MAAM,eAAe,WAAW,CAAC;YACxD,aAAa,CAAA,OACX,KAAK,GAAG,CAAC,CAAA,OAAS,KAAK,UAAU,KAAK,KAAK,UAAU,GAAG,iBAAiB;YAE3E,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,OAAO;QACT,EAAE,OAAO,KAAU;YACjB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS,IAAI,OAAO,IAAI;YACxB,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QACA;QAEA,qBAAqB;QACrB;QACA;QACA;QAEA,OAAO;QACP;QACA;QAEA,0BAA0B;QAC1B;IACF;AACF", "debugId": null}}, {"offset": {"line": 2742, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/applications/ApplicantInfo.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useEffect, useState } from 'react';\r\nimport { TextInput, CountryDropdown } from '@/components/forms';\r\nimport { ApplicantInfoData, ApplicationFormComponentProps } from './index';\r\nimport { useAddresses, initialAddressData, CreateAddressData } from '@/hooks/useAddressing';\r\n\r\ninterface ApplicantInfoProps extends ApplicationFormComponentProps {\r\n  data: ApplicantInfoData;\r\n  onChange: (data: ApplicantInfoData) => void;\r\n}\r\n\r\n\r\nconst ApplicantInfo: React.FC<ApplicantInfoProps> = ({\r\n  data,\r\n  onChange,\r\n  errors = {},\r\n  disabled = false\r\n}) => {\r\n  const handleInputChange = (field: keyof ApplicantInfoData, value: string) => {\r\n    onChange({\r\n      ...data,\r\n      [field]: value\r\n    });\r\n  };\r\n  \r\n  const { postcodeSuggestions, debouncedSearchPostcodes, createAddress } = useAddresses();\r\n  const [region, setRegion] = useState('');\r\n  const [addressData, setAddressData] = useState<CreateAddressData>(initialAddressData);\r\n  const [canAddNewEntry, setCanAddNewEntry] = useState(true);\r\n  const [submitting, setSubmitting] = useState(false);\r\n\r\n  // validate address completeness\r\n  useEffect(() => {\r\n    const isComplete =\r\n      addressData.address_line_1 &&\r\n      addressData.country &&\r\n      addressData.city &&\r\n      addressData.postal_code &&\r\n      addressData.address_origin &&\r\n      addressData.address_type;\r\n\r\n    setCanAddNewEntry(Boolean(isComplete));\r\n  }, [addressData]);\r\n\r\n  // ✅ Define save handler properly\r\n  const handleSavePostalAddress = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      console.log('Saving Postal Address:', addressData);\r\n      await createAddress(addressData); // You can enhance: pass origin/type here\r\n      setSubmitting(false);\r\n    } catch (err) {\r\n      console.error('Failed to save postal address:', err);\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  const handleSavePhysicalAddress = async () => {\r\n    try {\r\n      setSubmitting(true);\r\n      console.log('Saving Postal Address:', addressData);\r\n      await createAddress(addressData); // You can enhance: pass origin/type here\r\n      setSubmitting(false);\r\n    } catch (err) {\r\n      console.error('Failed to save postal address:', err);\r\n      setSubmitting(false);\r\n    }\r\n  };\r\n\r\n  // rest of your code, including JSX return, is already correct\r\n\r\n\r\n  return (\r\n    <div className=\"space-y-8\">\r\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\r\n        <h2 className=\"text-xl font-semibold text-gray-900 dark:text-gray-100\">\r\n          Applicant Information\r\n        </h2>\r\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\r\n          Please provide your personal and contact information\r\n        </p>\r\n      </div>\r\n\r\n      {/* Personal Information */}\r\n      <div className=\"space-y-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n          Personal Information\r\n        </h3>\r\n        \r\n        <div className=\"grid grid-cols-1 gap-6\">\r\n          <TextInput\r\n            label=\"Applicant Name\"\r\n            value={data.applicantName}\r\n            onChange={(e) => handleInputChange('applicantName', e.target.value)}\r\n            placeholder=\"Enter full name or company name\"\r\n            required\r\n            disabled={disabled}\r\n            error={errors.applicantName}\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Address Information */}\r\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\r\n        {/* Postal Address */}\r\n        <div className=\"bg-gray-50 dark:bg-gray-700/50 rounded-lg p-6\">\r\n          <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-4\">\r\n            <i className=\"ri-mail-line mr-2\"></i>\r\n            Postal Address\r\n          </h3>\r\n          <div className=\"space-y-4\">\r\n            {/* Country Dropdown (Always Visible) */}\r\n            <div>\r\n              <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Country *</label>\r\n              <CountryDropdown\r\n                value={addressData.country}\r\n                onChange={(value) => {\r\n                  setAddressData(prev => ({ ...prev, country: value }));\r\n                  if (value === 'Malawi') {\r\n                    setRegion('');\r\n                    setAddressData(prev => ({ ...prev, city: '', postal_code: '', address_line_1: '', address_line_2: '' }));\r\n                  }\r\n                }}\r\n                className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                required\r\n              />\r\n            </div>\r\n            {addressData.country === 'Malawi' ? (\r\n              <>\r\n                {/* Region Dropdown */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Region *</label>\r\n                  <select\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                    value={region || ''}\r\n                    onChange={(e) => {\r\n                      const selectedRegion = e.target.value;\r\n                      setRegion(selectedRegion);\r\n                      setAddressData(prev => ({\r\n                        ...prev,\r\n                        country: 'Malawi',\r\n                        city: '',\r\n                        postal_code: '',\r\n                        address_line_1: '',\r\n                        address_line_2: ''\r\n                      }));\r\n                      debouncedSearchPostcodes({ region: selectedRegion });\r\n                    }}\r\n                    required\r\n                  >\r\n                    <option value=\"\">Select a region</option>\r\n                    {['Northern', 'Central', 'Southern'].map(region => (\r\n                      <option key={region} value={region}>{region}</option>\r\n                    ))}\r\n                  </select>\r\n                </div>\r\n\r\n                {/* District Dropdown */}\r\n                {region && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">District *</label>\r\n                    <select\r\n                      className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                      value={addressData.city || ''}\r\n                      onChange={(e) => {\r\n                        const selectedDistrict = e.target.value;\r\n                        setAddressData(prev => ({\r\n                          ...prev,\r\n                          city: selectedDistrict,\r\n                          postal_code: '',\r\n                          address_line_1: '',\r\n                          address_line_2: ''\r\n                        }));\r\n                        debouncedSearchPostcodes({ region, district: selectedDistrict });\r\n                      }}\r\n                      required\r\n                    >\r\n                      <option value=\"\">Select a district</option>\r\n                      {[...new Set(postcodeSuggestions\r\n                        .filter(p => p.region === region)\r\n                        .map(p => p.district))].map(d => (\r\n                        <option key={d} value={d}>{d}</option>\r\n                      ))}\r\n                    </select>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Location Dropdown */}\r\n                {addressData.city && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Location *</label>\r\n                    <select\r\n                      className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                      value={addressData.postal_code || ''}\r\n                      onChange={(e) => {\r\n                        const selectedLocation = e.target.value;\r\n                        const match = postcodeSuggestions.find(p =>\r\n                          p.region === region &&\r\n                          p.district === addressData.city &&\r\n                          p.postal_code === selectedLocation\r\n                        );\r\n                        if (match) {\r\n                          setAddressData(prev => ({ ...prev, postal_code: match.postal_code }));\r\n                        }\r\n                      }}\r\n                      required\r\n                      disabled={!!addressData.postal_code}\r\n                    >\r\n                      <option value=\"\">Select a location</option>\r\n                      {postcodeSuggestions\r\n                        .filter(p =>\r\n                          p.region === region &&\r\n                          p.district === addressData.city)\r\n                        .map(loc => (\r\n                          <option key={loc.postal_code_id} value={loc.postal_code}>{loc.location}</option>\r\n                        ))}\r\n                    </select>\r\n                  </div>\r\n                )}\r\n\r\n                {/* Postal Code Display */}\r\n                {addressData.postal_code && (\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Postal Code *</label>\r\n                    <TextInput\r\n                      value={addressData.postal_code}\r\n                      readOnly\r\n                      disabled\r\n                      className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                    />\r\n                  </div>\r\n                )}\r\n\r\n                {/* Address Line 1 & 2 */}\r\n                {addressData.postal_code && (\r\n                  <>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Address Line 1 *</label>\r\n                      <TextInput\r\n                        placeholder=\"e.g., P.O. Box or P/Bag\"\r\n                        value={addressData.address_line_1}\r\n                        onChange={(e) => setAddressData(prev => ({ ...prev, address_line_1: e.target.value }))}\r\n                        required\r\n                        className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                      />\r\n                    </div>\r\n                    <div>\r\n                      <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Address Line 2</label>\r\n                      <TextInput\r\n                        placeholder=\"Optional details like Building Number, Block, etc.\"\r\n                        value={addressData.address_line_2}\r\n                        onChange={(e) => setAddressData(prev => ({ ...prev, address_line_2: e.target.value }))}\r\n                        className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                      />\r\n                    </div>\r\n                  </>\r\n                )}\r\n              </>\r\n            ) : (\r\n              <>\r\n                {/* Manual entry for non-Malawi countries */}\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Postal Code *</label>\r\n                  <TextInput\r\n                    value={addressData.postal_code}\r\n                    onChange={(e) => setAddressData(prev => ({ ...prev, postal_code: e.target.value }))}\r\n                    required\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">City *</label>\r\n                  <TextInput\r\n                    value={addressData.city}\r\n                    onChange={(e) => setAddressData(prev => ({ ...prev, city: e.target.value }))}\r\n                    required\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Address Line 1 *</label>\r\n                  <TextInput\r\n                    value={addressData.address_line_1}\r\n                    onChange={(e) => setAddressData(prev => ({ ...prev, address_line_1: e.target.value }))}\r\n                    required\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                  />\r\n                </div>\r\n                <div>\r\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Address Line 2</label>\r\n                  <TextInput\r\n                    value={addressData.address_line_2}\r\n                    onChange={(e) => setAddressData(prev => ({ ...prev, address_line_2: e.target.value }))}\r\n                    className=\"w-full border border-gray-300 dark:border-gray-600 dark:bg-gray-800 text-gray-900 dark:text-white rounded-md p-2 text-sm\"\r\n                  />\r\n                </div>\r\n              </>\r\n            )}\r\n            <div className=\"pt-4\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"px-4 py-2 bg-primary text-white rounded disabled:opacity-50 disabled:cursor-not-allowed\"\r\n                disabled={!canAddNewEntry || submitting}\r\n                onClick={() => {\r\n                  console.log('Saving Postal Address...', addressData);\r\n                  handleSavePostalAddress();\r\n                }}\r\n              >\r\n                {submitting ? 'Saving...' : 'Save Postal Address'}\r\n              </button>\r\n            </div>\r\n          </div>\r\n\r\n        </div>\r\n\r\n      </div>\r\n      {/* Contact Information */}\r\n      <div className=\"space-y-6\">\r\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\r\n          Contact Information\r\n        </h3>\r\n        \r\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\r\n          <TextInput\r\n            type=\"tel\"\r\n            label=\"Telephone\"\r\n            value={data.telephone}\r\n            onChange={(e) => handleInputChange('telephone', e.target.value)}\r\n            placeholder=\"+265 123 456 789\"\r\n            required\r\n            disabled={disabled}\r\n            error={errors.telephone}\r\n            helperText=\"Include country code (e.g., +265)\"\r\n          />\r\n          \r\n          <TextInput\r\n            type=\"tel\"\r\n            label=\"Fax\"\r\n            value={data.fax}\r\n            onChange={(e) => handleInputChange('fax', e.target.value)}\r\n            placeholder=\"+265 123 456 789\"\r\n            disabled={disabled}\r\n            error={errors.fax}\r\n            helperText=\"Include country code (optional)\"\r\n          />\r\n        </div>\r\n        \r\n        <div className=\"grid grid-cols-1 gap-6\">\r\n          <TextInput\r\n            type=\"email\"\r\n            label=\"Email Address\"\r\n            value={data.email}\r\n            onChange={(e) => handleInputChange('email', e.target.value)}\r\n            placeholder=\"<EMAIL>\"\r\n            required\r\n            disabled={disabled}\r\n            error={errors.email}\r\n            helperText=\"We'll use this email for all communications\"\r\n          />\r\n        </div>\r\n      </div>\r\n\r\n      {/* Information Notice */}\r\n      <div className=\"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800\">\r\n        <div className=\"flex items-start\">\r\n          <i className=\"ri-information-line text-blue-600 dark:text-blue-400 text-lg mr-3 mt-0.5\"></i>\r\n          <div>\r\n            <h4 className=\"text-sm font-medium text-blue-900 dark:text-blue-100 mb-1\">\r\n              Important Information\r\n            </h4>\r\n            <p className=\"text-blue-700 dark:text-blue-300 text-sm\">\r\n              Please ensure all information is accurate and up-to-date. This information will be used \r\n              for official correspondence and license documentation.\r\n            </p>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ApplicantInfo;\r\nfunction handleSubmit(arg0: () => void) {\r\n  throw new Error('Function not implemented.');\r\n}\r\n\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAEA;AALA;;;;;AAaA,MAAM,gBAA8C,CAAC,EACnD,IAAI,EACJ,QAAQ,EACR,SAAS,CAAC,CAAC,EACX,WAAW,KAAK,EACjB;IACC,MAAM,oBAAoB,CAAC,OAAgC;QACzD,SAAS;YACP,GAAG,IAAI;YACP,CAAC,MAAM,EAAE;QACX;IACF;IAEA,MAAM,EAAE,mBAAmB,EAAE,wBAAwB,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,6HAAA,CAAA,eAAY,AAAD;IACpF,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB,6HAAA,CAAA,qBAAkB;IACpF,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aACJ,YAAY,cAAc,IAC1B,YAAY,OAAO,IACnB,YAAY,IAAI,IAChB,YAAY,WAAW,IACvB,YAAY,cAAc,IAC1B,YAAY,YAAY;QAE1B,kBAAkB,QAAQ;IAC5B,GAAG;QAAC;KAAY;IAEhB,iCAAiC;IACjC,MAAM,0BAA0B;QAC9B,IAAI;YACF,cAAc;YACd,QAAQ,GAAG,CAAC,0BAA0B;YACtC,MAAM,cAAc,cAAc,yCAAyC;YAC3E,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,cAAc;QAChB;IACF;IAEA,MAAM,4BAA4B;QAChC,IAAI;YACF,cAAc;YACd,QAAQ,GAAG,CAAC,0BAA0B;YACtC,MAAM,cAAc,cAAc,yCAAyC;YAC3E,cAAc;QAChB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;YAChD,cAAc;QAChB;IACF;IAEA,8DAA8D;IAG9D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAyD;;;;;;kCAGvE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gLAAA,CAAA,YAAS;4BACR,OAAM;4BACN,OAAO,KAAK,aAAa;4BACzB,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;4BAClE,aAAY;4BACZ,QAAQ;4BACR,UAAU;4BACV,OAAO,OAAO,aAAa;;;;;;;;;;;;;;;;;0BAMjC,8OAAC;gBAAI,WAAU;0BAEb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAE,WAAU;;;;;;gCAAwB;;;;;;;sCAGvC,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;;sDACC,8OAAC;4CAAM,WAAU;sDAAkE;;;;;;sDACnF,8OAAC,4LAAA,CAAA,kBAAe;4CACd,OAAO,YAAY,OAAO;4CAC1B,UAAU,CAAC;gDACT,eAAe,CAAA,OAAQ,CAAC;wDAAE,GAAG,IAAI;wDAAE,SAAS;oDAAM,CAAC;gDACnD,IAAI,UAAU,UAAU;oDACtB,UAAU;oDACV,eAAe,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,MAAM;4DAAI,aAAa;4DAAI,gBAAgB;4DAAI,gBAAgB;wDAAG,CAAC;gDACxG;4CACF;4CACA,WAAU;4CACV,QAAQ;;;;;;;;;;;;gCAGX,YAAY,OAAO,KAAK,yBACvB;;sDAEE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC;oDACC,WAAU;oDACV,OAAO,UAAU;oDACjB,UAAU,CAAC;wDACT,MAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;wDACrC,UAAU;wDACV,eAAe,CAAA,OAAQ,CAAC;gEACtB,GAAG,IAAI;gEACP,SAAS;gEACT,MAAM;gEACN,aAAa;gEACb,gBAAgB;gEAChB,gBAAgB;4DAClB,CAAC;wDACD,yBAAyB;4DAAE,QAAQ;wDAAe;oDACpD;oDACA,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB;4DAAC;4DAAY;4DAAW;yDAAW,CAAC,GAAG,CAAC,CAAA,uBACvC,8OAAC;gEAAoB,OAAO;0EAAS;+DAAxB;;;;;;;;;;;;;;;;;wCAMlB,wBACC,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC;oDACC,WAAU;oDACV,OAAO,YAAY,IAAI,IAAI;oDAC3B,UAAU,CAAC;wDACT,MAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDACvC,eAAe,CAAA,OAAQ,CAAC;gEACtB,GAAG,IAAI;gEACP,MAAM;gEACN,aAAa;gEACb,gBAAgB;gEAChB,gBAAgB;4DAClB,CAAC;wDACD,yBAAyB;4DAAE;4DAAQ,UAAU;wDAAiB;oDAChE;oDACA,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB;+DAAI,IAAI,IAAI,oBACV,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,QACzB,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;yDAAG,CAAC,GAAG,CAAC,CAAA,kBAC5B,8OAAC;gEAAe,OAAO;0EAAI;+DAAd;;;;;;;;;;;;;;;;;wCAOpB,YAAY,IAAI,kBACf,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC;oDACC,WAAU;oDACV,OAAO,YAAY,WAAW,IAAI;oDAClC,UAAU,CAAC;wDACT,MAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wDACvC,MAAM,QAAQ,oBAAoB,IAAI,CAAC,CAAA,IACrC,EAAE,MAAM,KAAK,UACb,EAAE,QAAQ,KAAK,YAAY,IAAI,IAC/B,EAAE,WAAW,KAAK;wDAEpB,IAAI,OAAO;4DACT,eAAe,CAAA,OAAQ,CAAC;oEAAE,GAAG,IAAI;oEAAE,aAAa,MAAM,WAAW;gEAAC,CAAC;wDACrE;oDACF;oDACA,QAAQ;oDACR,UAAU,CAAC,CAAC,YAAY,WAAW;;sEAEnC,8OAAC;4DAAO,OAAM;sEAAG;;;;;;wDAChB,oBACE,MAAM,CAAC,CAAA,IACN,EAAE,MAAM,KAAK,UACb,EAAE,QAAQ,KAAK,YAAY,IAAI,EAChC,GAAG,CAAC,CAAA,oBACH,8OAAC;gEAAgC,OAAO,IAAI,WAAW;0EAAG,IAAI,QAAQ;+DAAzD,IAAI,cAAc;;;;;;;;;;;;;;;;;wCAOxC,YAAY,WAAW,kBACtB,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,WAAW;oDAC9B,QAAQ;oDACR,QAAQ;oDACR,WAAU;;;;;;;;;;;;wCAMf,YAAY,WAAW,kBACtB;;8DACE,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAkE;;;;;;sEACnF,8OAAC,gLAAA,CAAA,YAAS;4DACR,aAAY;4DACZ,OAAO,YAAY,cAAc;4DACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACpF,QAAQ;4DACR,WAAU;;;;;;;;;;;;8DAGd,8OAAC;;sEACC,8OAAC;4DAAM,WAAU;sEAAkE;;;;;;sEACnF,8OAAC,gLAAA,CAAA,YAAS;4DACR,aAAY;4DACZ,OAAO,YAAY,cAAc;4DACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;wEAAE,GAAG,IAAI;wEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;oEAAC,CAAC;4DACpF,WAAU;;;;;;;;;;;;;;;iEAOpB;;sDAEE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,WAAW;oDAC9B,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,aAAa,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACjF,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,IAAI;oDACvB,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,MAAM,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDAC1E,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,cAAc;oDACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACpF,QAAQ;oDACR,WAAU;;;;;;;;;;;;sDAGd,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DACnF,8OAAC,gLAAA,CAAA,YAAS;oDACR,OAAO,YAAY,cAAc;oDACjC,UAAU,CAAC,IAAM,eAAe,CAAA,OAAQ,CAAC;gEAAE,GAAG,IAAI;gEAAE,gBAAgB,EAAE,MAAM,CAAC,KAAK;4DAAC,CAAC;oDACpF,WAAU;;;;;;;;;;;;;;8CAKlB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,UAAU,CAAC,kBAAkB;wCAC7B,SAAS;4CACP,QAAQ,GAAG,CAAC,4BAA4B;4CACxC;wCACF;kDAEC,aAAa,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAIrE,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gLAAA,CAAA,YAAS;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO,KAAK,SAAS;gCACrB,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC9D,aAAY;gCACZ,QAAQ;gCACR,UAAU;gCACV,OAAO,OAAO,SAAS;gCACvB,YAAW;;;;;;0CAGb,8OAAC,gLAAA,CAAA,YAAS;gCACR,MAAK;gCACL,OAAM;gCACN,OAAO,KAAK,GAAG;gCACf,UAAU,CAAC,IAAM,kBAAkB,OAAO,EAAE,MAAM,CAAC,KAAK;gCACxD,aAAY;gCACZ,UAAU;gCACV,OAAO,OAAO,GAAG;gCACjB,YAAW;;;;;;;;;;;;kCAIf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,gLAAA,CAAA,YAAS;4BACR,MAAK;4BACL,OAAM;4BACN,OAAO,KAAK,KAAK;4BACjB,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;4BAC1D,aAAY;4BACZ,QAAQ;4BACR,UAAU;4BACV,OAAO,OAAO,KAAK;4BACnB,YAAW;;;;;;;;;;;;;;;;;0BAMjB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,8OAAC;oCAAE,WAAU;8CAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASpE;uCAEe;AACf,SAAS,aAAa,IAAgB;IACpC,MAAM,IAAI,MAAM;AAClB", "debugId": null}}, {"offset": {"line": 3495, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/applicant-info/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport CustomerLayout from '@/components/customer/CustomerLayout';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { licenseCategoryService, LicenseCategory } from '@/services/licenseCategoryService';\nimport { applicationService } from '@/services/applicationService';\nimport ApplicantInfo from '@/components/applications/ApplicantInfo';\nimport { ApplicantInfoData } from '@/components/applications';\n\n// Define application steps for navigation\nconst APPLICATION_STEPS = [\n  { id: 'applicant-info', name: 'Applicant Information' },\n  { id: 'company-profile', name: 'Company Profile' },\n  { id: 'management', name: 'Management Team' },\n  { id: 'professional-services', name: 'Professional Services' },\n  { id: 'business-info', name: 'Business Information' },\n  { id: 'service-scope', name: 'Service Scope' },\n  { id: 'business-plan', name: 'Business Plan' },\n  { id: 'legal-history', name: 'Legal History' },\n  { id: 'review-submit', name: 'Review & Submit' },\n];\n\nconst ApplicantInfoPage: React.FC = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { isAuthenticated, loading: authLoading } = useAuth();\n\n  // Get query parameters\n  const licenseCategoryId = searchParams.get('license_category_id');\n  const applicationId = searchParams.get('application_id');\n\n  // State\n  const [licenseCategory, setLicenseCategory] = useState<LicenseCategory | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [formData, setFormData] = useState<ApplicantInfoData>({\n    applicantName: '',\n    postalPoBox: '',\n    postalCity: '',\n    postalCountry: '',\n    physicalStreet: '',\n    physicalCity: '',\n    physicalCountry: '',\n    telephone: '',\n    fax: '',\n    email: ''\n  });\n  const [formErrors, setFormErrors] = useState<Record<string, string>>({});\n\n  const currentStepIndex = APPLICATION_STEPS.findIndex(step => step.id === 'applicant-info');\n\n  // Redirect if not authenticated\n  useEffect(() => {\n    if (!authLoading && !isAuthenticated) {\n      router.push('/customer/auth/login');\n      return;\n    }\n  }, [isAuthenticated, authLoading, router]);\n\n  // Fetch license category and determine license type\n  useEffect(() => {\n    const fetchLicenseCategory = async () => {\n      try {\n        // Use the license data hook to get category info\n        const category = licenseCategories.find(cat => cat.id === licenseCategoryId);\n\n        if (category && category.license_type_id) {\n          setLicenseTypeId(category.license_type_id);\n        } else {\n          // Fallback to API call if not found in hook data\n          const response = await fetch(`/api/license-categories/${licenseCategoryId}`);\n          if (!response.ok) {\n            throw new Error('Failed to fetch license category');\n          }\n          const categoryData = await response.json();\n\n          if (categoryData.license_type_id) {\n            setLicenseTypeId(categoryData.license_type_id);\n          } else {\n            throw new Error('License category does not have a license type ID');\n          }\n        }\n      } catch (error) {\n        console.error('Error fetching license category:', error);\n        setError('Failed to load license category information');\n      }\n    };\n\n    if (licenseCategoryId && !licenseDataLoading) {\n      fetchLicenseCategory();\n    }\n  }, [licenseCategoryId, licenseDataLoading, licenseCategories]);\n\n  // Load step configuration once we have license type ID\n  useEffect(() => {\n    if (licenseTypeId) {\n      console.log('ApplicantInfo page - License Type ID:', licenseTypeId);\n      const config = getLicenseTypeStepConfig(licenseTypeId);\n      console.log('ApplicantInfo page - License Config found:', !!config, config?.name);\n      \n      setLicenseConfig(config);\n      setCurrentStep(getStepByRoute(licenseTypeId, stepName));\n      setCurrentStepIndex(getStepIndex(licenseTypeId, stepName));\n      setTotalSteps(getTotalSteps(licenseTypeId));\n      setNextStep(getNextStep(licenseTypeId, stepName));\n      setPreviousStep(getPreviousStep(licenseTypeId, stepName));\n    }\n  }, [licenseTypeId, stepName]);\n\n  // Load progress and validate step configuration\n  useEffect(() => {\n    const initializeStepPage = async () => {\n      // Wait for license data to load before proceeding\n      if (licenseDataLoading || !licenseTypeId) {\n        return;\n      }\n\n      if (!licenseConfig) {\n        console.error('No license config found for:', licenseTypeId);\n        setError(`Invalid license type: ${licenseTypeId}. Please check the license type configuration.`);\n        return;\n      }\n\n      if (!currentStep) {\n        setError(`Invalid step: ${stepName} for license type ${licenseTypeId}`);\n        return;\n      }\n\n      // Load or initialize progress\n      try {\n        console.log('Loading progress for application:', applicationId, 'isEditMode:', isEditMode);\n        let progress = await applicationProgressService.getProgress(applicationId);\n        console.log('Progress loaded:', progress);\n\n        if (!progress && isEditMode) {\n          console.log('No progress found for existing application, initializing...');\n          // Initialize progress for existing application\n          progress = await applicationProgressService.initializeProgress(applicationId, licenseTypeId);\n          console.log('Progress initialized:', progress);\n        }\n\n        if (progress) {\n          const completedStepIds = await applicationProgressService.getCompletedStepIds(applicationId);\n          console.log('Completed step IDs:', completedStepIds);\n          setCompletedSteps(completedStepIds);\n          setProgressPercentage(progress.progressPercentage);\n\n          // Validate navigation permissions\n          console.log('Validating navigation for step:', currentStep.id);\n          const nextValidation = await stepValidationService.validateNextStepNavigation(\n            applicationId,\n            licenseTypeId,\n            currentStep.id\n          );\n          console.log('Navigation validation result:', nextValidation);\n          setCanNavigateNext(nextValidation.canNavigateToStep);\n\n          const prevValidation = await stepValidationService.validatePreviousStepNavigation(\n            applicationId, \n            licenseTypeId, \n            currentStep.id\n          );\n          setCanNavigatePrevious(prevValidation.canNavigateToStep);\n        }\n      } catch (error) {\n        console.error('Error loading application progress:', error);\n        // Don't fail completely - allow the form to load even if progress fails\n        console.log('Continuing with default progress state due to error');\n        setProgressPercentage(0);\n        setCompletedSteps([]);\n        setCanNavigateNext(false);\n      }\n\n      setIsLoading(false);\n    };\n\n    initializeStepPage();\n  }, [licenseConfig, currentStep, currentStepIndex, stepName, licenseTypeId, licenseCategoryId, applicationId, isEditMode, router, licenseDataLoading]);\n\n  // Handle step completion\n  const handleStepComplete = async (stepId: string, data?: any) => {\n    try {\n      let actualApplicationId = applicationId;\n\n      // If this is the ApplicantInfo step and we got an application ID, update it\n      if (stepId === 'applicant-info' && data?.applicationId) {\n        actualApplicationId = data.applicationId;\n        console.log('New application created, updating URL with application ID:', actualApplicationId);\n\n        // Update URL to include the new application ID\n        router.replace(`/customer/applications/apply/${licenseCategoryId}/applicant-info?app=${actualApplicationId}`);\n      }\n\n      if (actualApplicationId !== 'new') {\n        const progress = await applicationProgressService.markStepCompleted(actualApplicationId, stepId, data);\n        setCompletedSteps(progress.steps.filter(s => s.completed).map(s => s.stepId));\n        setProgressPercentage(progress.progressPercentage);\n\n        // Refresh navigation permissions after step completion\n        if (currentStep) {\n          const nextValidation = await stepValidationService.validateNextStepNavigation(\n            actualApplicationId,\n            licenseTypeId,\n            currentStep.id\n          );\n          setCanNavigateNext(nextValidation.canNavigateToStep);\n        }\n      }\n    } catch (error) {\n      console.error('Error marking step as completed:', error);\n    }\n  };\n\n  // Handle step error\n  const handleStepError = (stepId: string, error: string) => {\n    console.error(`Error in step ${stepId}:`, error);\n    setError(error);\n  };\n\n  // Handle navigation\n  const handleNavigate = (direction: 'next' | 'previous') => {\n    if (direction === 'next' && nextStep && canNavigateNext) {\n      const appParam = applicationId !== 'new' ? `?app=${applicationId}` : '';\n      router.push(`/customer/applications/apply/${licenseCategoryId}/${nextStep.route}${appParam}`);\n    } else if (direction === 'previous' && previousStep && canNavigatePrevious) {\n      const appParam = applicationId !== 'new' ? `?app=${applicationId}` : '';\n      router.push(`/customer/applications/apply/${licenseCategoryId}/${previousStep.route}${appParam}`);\n    }\n  };\n\n  if (authLoading || isLoading || !licenseTypeId) {\n    return (\n      <CustomerLayout>\n        <div className=\"flex items-center justify-center min-h-96\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading application step...</p>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  if (error) {\n    return (\n      <CustomerLayout>\n        <div className=\"max-w-4xl mx-auto p-6\">\n          <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6\">\n            <div className=\"flex items-center\">\n              <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-2xl mr-4\"></i>\n              <div>\n                <h3 className=\"text-lg font-medium text-red-800 dark:text-red-200\">\n                  Error Loading Step\n                </h3>\n                <p className=\"text-red-700 dark:text-red-300 mt-1\">{error}</p>\n                <button\n                  onClick={() => router.back()}\n                  className=\"mt-4 inline-flex items-center px-4 py-2 border border-red-300 dark:border-red-600 rounded-md text-sm font-medium text-red-700 dark:text-red-300 bg-white dark:bg-red-900/20 hover:bg-red-50 dark:hover:bg-red-900/40\"\n                >\n                  <i className=\"ri-arrow-left-line mr-2\"></i>\n                  Go Back\n                </button>\n              </div>\n            </div>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  // Use tracked progress instead of calculating it\n  const displayProgress = progressPercentage;\n\n  // Step props for the component\n  const stepProps = {\n    applicationId: applicationId || 'new',\n    licenseTypeId,\n    licenseCategoryId,\n    isEditMode,\n    onStepComplete: handleStepComplete,\n    onStepError: handleStepError,\n    onNavigate: handleNavigate\n  };\n\n  return (\n    <CustomerLayout>\n      <div className=\"max-w-4xl mx-auto p-6\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <h1 className=\"text-2xl font-bold text-gray-900 dark:text-gray-100 mb-2\">\n            {licenseConfig?.name} License Application\n          </h1>\n          <p className=\"text-gray-600 dark:text-gray-400\">\n            Step {currentStepIndex + 1} of {totalSteps}: {currentStep?.name}\n          </p>\n          {isEditMode && (\n            <div className=\"mt-2 p-2 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg\">\n              <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n                <i className=\"ri-edit-line mr-1\"></i>\n                Editing existing application\n              </p>\n            </div>\n          )}\n        </div>\n\n        {/* Progress Bar */}\n        <div className=\"mb-8\">\n          <div className=\"flex items-center justify-between mb-2\">\n            <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">\n              Step {currentStepIndex + 1} of {totalSteps}\n            </span>\n            <span className=\"text-sm text-gray-500 dark:text-gray-400\">\n              {displayProgress}% Complete\n            </span>\n          </div>\n          <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n            <div\n              className=\"bg-primary h-2 rounded-full transition-all duration-300\"\n              style={{ width: `${displayProgress}%` }}\n            ></div>\n          </div>\n        </div>\n\n        {/* Step Navigation Breadcrumb */}\n        <div className=\"mb-8\">\n          <div className=\"flex flex-wrap gap-2\">\n            {licenseConfig?.steps.map((step: any, index: number) => {\n              const isCompleted = completedSteps.includes(step.id);\n              const isCurrent = index === currentStepIndex;\n              \n              return (\n                <div\n                  key={step.id}\n                  className={`px-3 py-1 rounded-full text-xs font-medium ${\n                    isCurrent\n                      ? 'bg-primary text-white'\n                      : isCompleted\n                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'\n                      : 'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-400'\n                  }`}\n                >\n                  {isCompleted && !isCurrent && (\n                    <i className=\"ri-check-line mr-1\"></i>\n                  )}\n                  {index + 1}. {step.name}\n                </div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Navigation Error */}\n        {navigationError && (\n          <div className=\"mb-6 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg\">\n            <div className=\"flex items-center\">\n              <i className=\"ri-warning-line text-yellow-600 dark:text-yellow-400 text-lg mr-3\"></i>\n              <div>\n                <h3 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">\n                  Navigation Restriction\n                </h3>\n                <p className=\"text-yellow-700 dark:text-yellow-300 text-sm mt-1\">\n                  {navigationError}\n                </p>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Step Content */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6\">\n          <ApplicantInfo {...stepProps} />\n        </div>\n      </div>\n    </CustomerLayout>\n  );\n};\n\nexport default ApplicantInfoPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAGA;AARA;;;;;;;AAWA,0CAA0C;AAC1C,MAAM,oBAAoB;IACxB;QAAE,IAAI;QAAkB,MAAM;IAAwB;IACtD;QAAE,IAAI;QAAmB,MAAM;IAAkB;IACjD;QAAE,IAAI;QAAc,MAAM;IAAkB;IAC5C;QAAE,IAAI;QAAyB,MAAM;IAAwB;IAC7D;QAAE,IAAI;QAAiB,MAAM;IAAuB;IACpD;QAAE,IAAI;QAAiB,MAAM;IAAgB;IAC7C;QAAE,IAAI;QAAiB,MAAM;IAAgB;IAC7C;QAAE,IAAI;QAAiB,MAAM;IAAgB;IAC7C;QAAE,IAAI;QAAiB,MAAM;IAAkB;CAChD;AAED,MAAM,oBAA8B;IAClC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExD,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IAEvC,QAAQ;IACR,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;QAC1D,eAAe;QACf,aAAa;QACb,YAAY;QACZ,eAAe;QACf,gBAAgB;QAChB,cAAc;QACd,iBAAiB;QACjB,WAAW;QACX,KAAK;QACL,OAAO;IACT;IACA,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAEtE,MAAM,mBAAmB,kBAAkB,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;IAEzE,gCAAgC;IAChC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;YACpC,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,oDAAoD;IACpD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,uBAAuB;YAC3B,IAAI;gBACF,iDAAiD;gBACjD,MAAM,WAAW,kBAAkB,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;gBAE1D,IAAI,YAAY,SAAS,eAAe,EAAE;oBACxC,iBAAiB,SAAS,eAAe;gBAC3C,OAAO;oBACL,iDAAiD;oBACjD,MAAM,WAAW,MAAM,MAAM,CAAC,wBAAwB,EAAE,mBAAmB;oBAC3E,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,IAAI,MAAM;oBAClB;oBACA,MAAM,eAAe,MAAM,SAAS,IAAI;oBAExC,IAAI,aAAa,eAAe,EAAE;wBAChC,iBAAiB,aAAa,eAAe;oBAC/C,OAAO;wBACL,MAAM,IAAI,MAAM;oBAClB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,SAAS;YACX;QACF;QAEA,IAAI,qBAAqB,CAAC,oBAAoB;YAC5C;QACF;IACF,GAAG;QAAC;QAAmB;QAAoB;KAAkB;IAE7D,uDAAuD;IACvD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe;YACjB,QAAQ,GAAG,CAAC,yCAAyC;YACrD,MAAM,SAAS,yBAAyB;YACxC,QAAQ,GAAG,CAAC,8CAA8C,CAAC,CAAC,QAAQ,QAAQ;YAE5E,iBAAiB;YACjB,eAAe,eAAe,eAAe;YAC7C,oBAAoB,aAAa,eAAe;YAChD,cAAc,cAAc;YAC5B,YAAY,YAAY,eAAe;YACvC,gBAAgB,gBAAgB,eAAe;QACjD;IACF,GAAG;QAAC;QAAe;KAAS;IAE5B,gDAAgD;IAChD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB;YACzB,kDAAkD;YAClD,IAAI,sBAAsB,CAAC,eAAe;gBACxC;YACF;YAEA,IAAI,CAAC,eAAe;gBAClB,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,SAAS,CAAC,sBAAsB,EAAE,cAAc,8CAA8C,CAAC;gBAC/F;YACF;YAEA,IAAI,CAAC,aAAa;gBAChB,SAAS,CAAC,cAAc,EAAE,SAAS,kBAAkB,EAAE,eAAe;gBACtE;YACF;YAEA,8BAA8B;YAC9B,IAAI;gBACF,QAAQ,GAAG,CAAC,qCAAqC,eAAe,eAAe;gBAC/E,IAAI,WAAW,MAAM,2BAA2B,WAAW,CAAC;gBAC5D,QAAQ,GAAG,CAAC,oBAAoB;gBAEhC,IAAI,CAAC,YAAY,YAAY;oBAC3B,QAAQ,GAAG,CAAC;oBACZ,+CAA+C;oBAC/C,WAAW,MAAM,2BAA2B,kBAAkB,CAAC,eAAe;oBAC9E,QAAQ,GAAG,CAAC,yBAAyB;gBACvC;gBAEA,IAAI,UAAU;oBACZ,MAAM,mBAAmB,MAAM,2BAA2B,mBAAmB,CAAC;oBAC9E,QAAQ,GAAG,CAAC,uBAAuB;oBACnC,kBAAkB;oBAClB,sBAAsB,SAAS,kBAAkB;oBAEjD,kCAAkC;oBAClC,QAAQ,GAAG,CAAC,mCAAmC,YAAY,EAAE;oBAC7D,MAAM,iBAAiB,MAAM,sBAAsB,0BAA0B,CAC3E,eACA,eACA,YAAY,EAAE;oBAEhB,QAAQ,GAAG,CAAC,iCAAiC;oBAC7C,mBAAmB,eAAe,iBAAiB;oBAEnD,MAAM,iBAAiB,MAAM,sBAAsB,8BAA8B,CAC/E,eACA,eACA,YAAY,EAAE;oBAEhB,uBAAuB,eAAe,iBAAiB;gBACzD;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uCAAuC;gBACrD,wEAAwE;gBACxE,QAAQ,GAAG,CAAC;gBACZ,sBAAsB;gBACtB,kBAAkB,EAAE;gBACpB,mBAAmB;YACrB;YAEA,aAAa;QACf;QAEA;IACF,GAAG;QAAC;QAAe;QAAa;QAAkB;QAAU;QAAe;QAAmB;QAAe;QAAY;QAAQ;KAAmB;IAEpJ,yBAAyB;IACzB,MAAM,qBAAqB,OAAO,QAAgB;QAChD,IAAI;YACF,IAAI,sBAAsB;YAE1B,4EAA4E;YAC5E,IAAI,WAAW,oBAAoB,MAAM,eAAe;gBACtD,sBAAsB,KAAK,aAAa;gBACxC,QAAQ,GAAG,CAAC,8DAA8D;gBAE1E,+CAA+C;gBAC/C,OAAO,OAAO,CAAC,CAAC,6BAA6B,EAAE,kBAAkB,oBAAoB,EAAE,qBAAqB;YAC9G;YAEA,IAAI,wBAAwB,OAAO;gBACjC,MAAM,WAAW,MAAM,2BAA2B,iBAAiB,CAAC,qBAAqB,QAAQ;gBACjG,kBAAkB,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,SAAS,EAAE,GAAG,CAAC,CAAA,IAAK,EAAE,MAAM;gBAC3E,sBAAsB,SAAS,kBAAkB;gBAEjD,uDAAuD;gBACvD,IAAI,aAAa;oBACf,MAAM,iBAAiB,MAAM,sBAAsB,0BAA0B,CAC3E,qBACA,eACA,YAAY,EAAE;oBAEhB,mBAAmB,eAAe,iBAAiB;gBACrD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD;IACF;IAEA,oBAAoB;IACpB,MAAM,kBAAkB,CAAC,QAAgB;QACvC,QAAQ,KAAK,CAAC,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,EAAE;QAC1C,SAAS;IACX;IAEA,oBAAoB;IACpB,MAAM,iBAAiB,CAAC;QACtB,IAAI,cAAc,UAAU,YAAY,iBAAiB;YACvD,MAAM,WAAW,kBAAkB,QAAQ,CAAC,KAAK,EAAE,eAAe,GAAG;YACrE,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,EAAE,SAAS,KAAK,GAAG,UAAU;QAC9F,OAAO,IAAI,cAAc,cAAc,gBAAgB,qBAAqB;YAC1E,MAAM,WAAW,kBAAkB,QAAQ,CAAC,KAAK,EAAE,eAAe,GAAG;YACrE,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,kBAAkB,CAAC,EAAE,aAAa,KAAK,GAAG,UAAU;QAClG;IACF;IAEA,IAAI,eAAe,aAAa,CAAC,eAAe;QAC9C,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAqD;;;;;;kDAGnE,8OAAC;wCAAE,WAAU;kDAAuC;;;;;;kDACpD,8OAAC;wCACC,SAAS,IAAM,OAAO,IAAI;wCAC1B,WAAU;;0DAEV,8OAAC;gDAAE,WAAU;;;;;;4CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAS3D;IAEA,iDAAiD;IACjD,MAAM,kBAAkB;IAExB,+BAA+B;IAC/B,MAAM,YAAY;QAChB,eAAe,iBAAiB;QAChC;QACA;QACA;QACA,gBAAgB;QAChB,aAAa;QACb,YAAY;IACd;IAEA,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;gCACX,eAAe;gCAAK;;;;;;;sCAEvB,8OAAC;4BAAE,WAAU;;gCAAmC;gCACxC,mBAAmB;gCAAE;gCAAK;gCAAW;gCAAG,aAAa;;;;;;;wBAE5D,4BACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;kDACX,8OAAC;wCAAE,WAAU;;;;;;oCAAwB;;;;;;;;;;;;;;;;;;8BAQ7C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAU;;wCAAuD;wCAC/D,mBAAmB;wCAAE;wCAAK;;;;;;;8CAElC,8OAAC;oCAAK,WAAU;;wCACb;wCAAgB;;;;;;;;;;;;;sCAGrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,WAAU;gCACV,OAAO;oCAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;gCAAC;;;;;;;;;;;;;;;;;8BAM5C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ,eAAe,MAAM,IAAI,CAAC,MAAW;4BACpC,MAAM,cAAc,eAAe,QAAQ,CAAC,KAAK,EAAE;4BACnD,MAAM,YAAY,UAAU;4BAE5B,qBACE,8OAAC;gCAEC,WAAW,CAAC,2CAA2C,EACrD,YACI,0BACA,cACA,sEACA,iEACJ;;oCAED,eAAe,CAAC,2BACf,8OAAC;wCAAE,WAAU;;;;;;oCAEd,QAAQ;oCAAE;oCAAG,KAAK,IAAI;;+BAZlB,KAAK,EAAE;;;;;wBAelB;;;;;;;;;;;gBAKH,iCACC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;;;;;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,8OAAC;wCAAE,WAAU;kDACV;;;;;;;;;;;;;;;;;;;;;;;8BAQX,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,mJAAA,CAAA,UAAa;wBAAE,GAAG,SAAS;;;;;;;;;;;;;;;;;;;;;;AAKtC;uCAEe", "debugId": null}}]}