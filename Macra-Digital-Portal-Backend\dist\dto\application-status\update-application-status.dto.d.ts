export declare class UpdateApplicationStatusDto {
    status: string;
    comments?: string;
    reason?: string;
    estimated_completion_date?: string;
    changed_by?: string;
}
export declare class ApplicationStatusHistoryResponseDto {
    history_id: string;
    application_id: string;
    status: string;
    previous_status?: string;
    comments?: string;
    reason?: string;
    changed_by_name: string;
    changed_at: Date;
    estimated_completion_date?: Date;
}
export declare class ApplicationStatusTrackingResponseDto {
    application_id: string;
    application_number: string;
    current_status: string;
    current_step: number;
    progress_percentage: number;
    submitted_at?: Date;
    created_at: Date;
    updated_at: Date;
    status_history: ApplicationStatusHistoryResponseDto[];
    applicant: {
        name: string;
        email: string;
        business_registration_number: string;
    };
    license_category: {
        name: string;
        description: string;
    };
}
