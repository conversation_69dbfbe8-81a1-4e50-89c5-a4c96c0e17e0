'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { getLicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';

/**
 * ApplicationForm Coordinator Component
 * This component now acts as a coordinator that redirects to the new step-based routing system
 * It maintains backward compatibility while leveraging the new independent step architecture
 */

interface ApplicationFormProps {
  licenseType: string;
  licenseCategory?: string;
  licenseTypeId?: string;
  licenseCategoryId?: string;
  applicationId?: string;
  onSubmit?: (formData: any) => Promise<void>;
}

const ApplicationForm: React.FC<ApplicationFormProps> = ({
  licenseTypeId,
  licenseCategoryId,
  applicationId,
  onSubmit
}) => {
  const router = useRouter();

  // Redirect to step-based routing system
  useEffect(() => {
    // Validate required parameters
    if (!licenseTypeId || !licenseCategoryId) {
      console.error('ApplicationForm: Missing required licenseTypeId or licenseCategoryId');
      router.push('/customer/applications/new');
      return;
    }

    // Get license type configuration to validate
    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);
    if (!licenseConfig) {
      console.error('ApplicationForm: Invalid license type:', licenseTypeId);
      router.push('/customer/applications/new');
      return;
    }

    // Determine the target route based on whether we have an application ID
    const firstStep = licenseConfig.steps[0];
    const targetApplicationId = applicationId || 'new';
    const targetUrl = `/customer/applications/apply/${targetApplicationId}?${firstStep.route}?licenseType=${licenseTypeId}&licenseCategory=${licenseCategoryId}`;

    
    console.log('ApplicationForm: Redirecting to step-based routing:', targetUrl);
    router.replace(targetUrl);
  }, [licenseTypeId, licenseCategoryId, applicationId, router]);

  // Show loading while redirecting
  return (
    <div className="flex items-center justify-center min-h-96">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">Redirecting to application form...</p>
      </div>
    </div>
  );
};

export default ApplicationForm;
