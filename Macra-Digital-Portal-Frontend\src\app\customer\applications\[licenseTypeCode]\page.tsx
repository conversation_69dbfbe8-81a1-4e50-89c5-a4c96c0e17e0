'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { useLicenseData } from '@/hooks/useLicenseData';
import { LicenseType } from '@/services/licenseTypeService';
import { LicenseCategory } from '@/services/licenseCategoryService';
import { findLicenseTypeByCode, generateApplicationUrl } from '@/services/routingService';

const LicenseTypePage: React.FC = () => {
  const router = useRouter();
  const params = useParams();
  const { isAuthenticated, loading: authLoading } = useAuth();
  const { licenseTypes, loading: licenseLoading, getCategoriesByType } = useLicenseData();

  const licenseTypeCode = params.licenseTypeCode as string;
  const [selectedLicenseType, setSelectedLicenseType] = useState<LicenseType | null>(null);
  const [availableCategories, setAvailableCategories] = useState<LicenseCategory[]>([]);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }

    // Debug logging
    console.log('License Type Code Page Debug:', {
      licenseTypeCode,
      licenseTypesCount: licenseTypes?.length,
      licenseLoading,
      authLoading,
      isAuthenticated
    });

    // Only process if we have license types data and not loading
    if (licenseTypes && licenseTypes.length > 0 && licenseTypeCode && !licenseLoading) {
      console.log('Looking for license type with code:', licenseTypeCode);
      console.log('Available license types:', licenseTypes.map(lt => ({ 
        id: lt.license_type_id, 
        name: lt.name, 
        code: lt.code 
      })));

      const licenseType = findLicenseTypeByCode(licenseTypes, licenseTypeCode);
      if (licenseType) {
        console.log('Found license type:', licenseType);
        setSelectedLicenseType(licenseType);
        // Get categories for this license type
        const typeCategories = getCategoriesByType(licenseType.license_type_id);
        console.log('Categories for type:', typeCategories);
        setAvailableCategories(typeCategories);
      } else {
        console.warn('License type not found for code:', licenseTypeCode);
        // Only redirect if we're sure the data has loaded and the type doesn't exist
        if (!licenseLoading) {
          console.log('Redirecting back to applications');
          router.push('/customer/applications');
        }
      }
    } else if (licenseTypes && licenseTypes.length === 0 && !licenseLoading) {
      // No license types available
      console.warn('No license types available');
      router.push('/customer/applications');
    }
  }, [isAuthenticated, authLoading, router, licenseTypes, licenseTypeCode, licenseLoading, getCategoriesByType]);

  const handleCategorySelect = (category: LicenseCategory) => {
    if (!selectedLicenseType) return;
    
    setLoading(true);
    
    // Generate the new code-based URL
    const applicationUrl = generateApplicationUrl(selectedLicenseType, category, 'applicant-info');
    console.log('Navigating to application form:', applicationUrl);
    
    router.push(applicationUrl);
  };

  const handleBackToApplications = () => {
    router.push('/customer/applications');
  };

  if (authLoading || licenseLoading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading license information...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  if (!selectedLicenseType) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="mb-4">
              <i className="ri-error-warning-line text-4xl text-red-500"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              License Type Not Found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              The license type "{licenseTypeCode}" could not be found.
            </p>
            <button
              onClick={handleBackToApplications}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Back to Applications
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <button
                onClick={handleBackToApplications}
                className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mb-4"
              >
                <i className="ri-arrow-left-line mr-1"></i>
                Back to Applications
              </button>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
                {selectedLicenseType.name}
              </h1>
              <p className="mt-2 text-gray-600 dark:text-gray-400">
                {selectedLicenseType.description}
              </p>
            </div>
          </div>
        </div>

        {/* License Categories */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              Select License Category
            </h2>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Choose the specific license category you want to apply for.
            </p>
          </div>

          <div className="p-6">
            {availableCategories.length === 0 ? (
              <div className="text-center py-12">
                <div className="mb-4">
                  <i className="ri-file-list-line text-4xl text-gray-400"></i>
                </div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                  No Categories Available
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  There are no license categories available for this license type.
                </p>
              </div>
            ) : (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {availableCategories.map((category) => (
                  <div
                    key={category.license_category_id}
                    className="border border-gray-200 dark:border-gray-700 rounded-lg p-6 hover:border-primary hover:shadow-md transition-all duration-200 cursor-pointer"
                    onClick={() => handleCategorySelect(category)}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                        {category.name}
                      </h3>
                      <span className="text-sm font-medium text-primary bg-primary/10 px-2 py-1 rounded">
                        MWK {parseFloat(category.fee).toLocaleString()}
                      </span>
                    </div>
                    <p className="text-gray-600 dark:text-gray-400 text-sm mb-4">
                      {category.description}
                    </p>
                    <div className="text-xs text-gray-500 dark:text-gray-500">
                      <strong>Authorizes:</strong> {category.authorizes}
                    </div>
                    <div className="mt-4 flex items-center text-primary text-sm font-medium">
                      Apply Now
                      <i className="ri-arrow-right-line ml-1"></i>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default LicenseTypePage;
