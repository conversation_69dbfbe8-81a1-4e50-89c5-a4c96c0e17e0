'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { applicationService } from '@/services/applicationService';

// Types
interface Application {
  application_id: string;
  application_number: string;
  status: string;
  progress_percentage: number;
  current_step: number;
  submitted_at?: string;
  created_at: string;
  license_category_id: string;
  license_category?: {
    license_category_id: string;
    name: string;
    description: string;
    license_type_id: string;
    license_type?: {
      license_type_id: string;
      name: string;
      description?: string;
    };
  };
}


// Status Modal Component
interface StatusModalProps {
  isOpen: boolean;
  onClose: () => void;
  application: Application | null;
  onContinueApplication?: (application: Application) => void;
  canContinueApplication?: (application: Application) => boolean;
}

const StatusModal: React.FC<StatusModalProps> = ({
  isOpen,
  onClose,
  application,
  onContinueApplication,
  canContinueApplication
}) => {
  if (!isOpen || !application) return null;

  const steps = [
    { id: 1, name: 'Draft', description: 'Application incomplete', icon: 'ri-file-text-line' },
    { id: 2, name: 'Submitted', description: 'Application received and logged', icon: 'ri-file-text-line' },
    { id: 3, name: 'Under Review', description: 'Being reviewed by MACRA team', icon: 'ri-search-line' },
    { id: 4, name: 'Evaluation', description: 'Technical evaluation in progress', icon: 'ri-clipboard-line' },
    { id: 5, name: 'Approved', description: 'License approved and issued', icon: 'ri-check-line' }
  ];

  const getStepStatus = (stepId: number) => {
    const currentStep = application.current_step;
    if (application.status === 'rejected') return stepId === 1 ? 'complete' : 'error';
    if (application.status === 'approved') return 'complete';
    return stepId <= currentStep ? (stepId === currentStep ? 'current' : 'complete') : 'upcoming';
  };

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        {/* Background overlay */}
        <div
          className="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          onClick={onClose}
        ></div>

        {/* Modal panel */}
        <div className="inline-block align-bottom bg-white dark:bg-gray-800 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
          <div className="bg-white dark:bg-gray-800 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            {/* Modal header */}
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Application Status
              </h3>
              <button
                type="button"
                title="Close modal"
                onClick={onClose}
                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              >
                <i className="ri-close-line text-xl"></i>
              </button>
            </div>

            {/* Application info */}
            <div className="mb-6 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h4 className="font-medium text-gray-900 dark:text-gray-100">
                {application.application_number}
              </h4>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {application.license_category?.name || 'License Category'}
              </p>
            </div>

            {/* Status tracker */}
            <div className="relative mb-6">
              {/* Progress line */}
              <div className="absolute top-6 left-6 right-6 h-0.5 bg-gray-200 dark:bg-gray-600">
                <div
                  className={`h-full bg-primary transition-all duration-500 ${
                    application.status === 'approved' ? 'w-full' :
                    application.current_step === 4 ? 'w-full' :
                    application.current_step === 3 ? 'w-2/3' :
                    application.current_step === 2 ? 'w-1/3' :
                    application.current_step === 1 ? 'w-0' : 'w-0'
                  }`}
                />
              </div>

              {/* Steps */}
              <div className="relative flex justify-between">
                {steps.map((step) => {
                  const stepStatus = getStepStatus(step.id);
                  return (
                    <div key={step.id} className="flex flex-col items-center">
                      <div className={`
                        w-12 h-12 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-all duration-300
                        ${stepStatus === 'complete' ? 'bg-primary border-primary text-white' :
                          stepStatus === 'current' ? 'bg-primary border-primary text-white animate-pulse' :
                          stepStatus === 'error' ? 'bg-red-500 border-red-500 text-white' :
                          'bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600 text-gray-400'}
                      `}>
                        <i className={step.icon}></i>
                      </div>
                      <div className="mt-3 text-center">
                        <div className={`text-sm font-medium ${
                          stepStatus === 'complete' || stepStatus === 'current' ? 'text-gray-900 dark:text-gray-100' : 'text-gray-500 dark:text-gray-400'
                        }`}>
                          {step.name}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 max-w-20">
                          {step.description}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Status details */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Current Status</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {application.status.replace('_', ' ').toUpperCase()}
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Progress</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {application.progress_percentage || 0}% Complete
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Submitted</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    {application.submitted_at
                      ? new Date(application.submitted_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                      : new Date(application.created_at).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })
                    }
                  </p>
                </div>
                <div>
                  <p className="text-gray-500 dark:text-gray-400">Estimated Time</p>
                  <p className="font-medium text-gray-900 dark:text-gray-100">
                    30-45 business days
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Modal footer */}
          <div className="bg-gray-50 dark:bg-gray-700 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button
              type="button"
              onClick={onClose}
              className="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm"
            >
              Close
            </button>

            {/* Continue Application Button */}
            {application && canContinueApplication && onContinueApplication && canContinueApplication(application) && (
              <button
                type="button"
                onClick={() => {
                  onContinueApplication(application);
                  onClose();
                }}
                className="w-full inline-flex justify-center rounded-md border border-blue-300 shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mr-3 sm:w-auto sm:text-sm"
              >
                <i className="ri-edit-line mr-2"></i>
                Continue Application
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

const MyLicensesPage: React.FC = () => {
  const { isAuthenticated} = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [applications, setApplications] = useState<Application[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [selectedApplication, setSelectedApplication] = useState<Application | null>(null);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [showSuccessMessage, setShowSuccessMessage] = useState<boolean>(false);

  // Check for success message from URL params
  useEffect(() => {
    if (searchParams.get('submitted') === 'true') {
      setShowSuccessMessage(true);
      // Remove the parameter from URL
      const newUrl = new URL(window.location.href);
      newUrl.searchParams.delete('submitted');
      window.history.replaceState({}, '', newUrl.toString());

      // Hide success message after 5 seconds
      setTimeout(() => setShowSuccessMessage(false), 5000);
    }
  }, [searchParams]);

  const breadcrumbs = [
    { label: 'Dashboard', href: '/customer' },
    { label: 'My Licenses', href: '/customer/my-licenses' }
  ];

  // Modal functions
  const openStatusModal = (application: Application) => {
    setSelectedApplication(application);
    setIsModalOpen(true);
  };

  const closeStatusModal = () => {
    setSelectedApplication(null);
    setIsModalOpen(false);
  };

  // Function to continue working on an application
  const handleContinueApplication = async (application: Application) => {
    console.log('Continuing application:', application);

    // Clear any existing errors
    setError(null);

    // Get the license type ID from the license category
    const licenseTypeId = application.license_category?.license_type_id ||
                         application.license_category?.license_type?.license_type_id;

    console.log('License type ID found:', licenseTypeId);
    console.log('License category data:', application.license_category);

    if (!licenseTypeId) {
      console.error('License type ID not found in application data:', application);
      setError('Unable to continue application: License type information is missing. Please contact support.');
      return;
    }

    try {
      // Import step validation service dynamically
      const { stepValidationService } = await import('@/services/stepValidationService');
      const { getLicenseTypeStepConfig, getStepByIndex } = await import('@/config/licenseTypeStepConfig');

      // Get license configuration to determine steps
      const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);
      if (!licenseConfig) {
        console.error('No license configuration found for license type:', licenseTypeId);
        setError('Unable to continue application: License type configuration not found.');
        return;
      }

      // Use the license type code from the config for the URL instead of the UUID
      const licenseTypeCode = licenseConfig.licenseTypeId;

      // Determine the appropriate step to continue from
      let targetStepRoute: string;

      try {
        // Try to get the next available step based on current progress
        const nextAvailableStepId = await stepValidationService.getNextAvailableStep(
          application.application_id,
          licenseTypeCode
        );

        if (nextAvailableStepId) {
          // Find the step configuration for this step ID
          const stepConfig = licenseConfig.steps.find(step => step.id === nextAvailableStepId);
          if (stepConfig) {
            targetStepRoute = stepConfig.route;
            console.log('Found next available step:', nextAvailableStepId, 'route:', targetStepRoute);
          } else {
            throw new Error('Step configuration not found for step ID: ' + nextAvailableStepId);
          }
        } else {
          // Fallback: use current step from application
          const currentStepIndex = Math.max(0, (application.current_step || 1) - 1); // Convert 1-based to 0-based
          const currentStepConfig = getStepByIndex(licenseTypeCode, currentStepIndex);

          if (currentStepConfig) {
            targetStepRoute = currentStepConfig.route;
            console.log('Using current step from application:', application.current_step, 'route:', targetStepRoute);
          } else {
            // Final fallback: use first step
            targetStepRoute = licenseConfig.steps[0].route;
            console.log('Fallback to first step:', targetStepRoute);
          }
        }
      } catch (stepError) {
        console.warn('Error determining next step, falling back to current step:', stepError);

        // Fallback: use current step from application
        const currentStepIndex = Math.max(0, (application.current_step || 1) - 1); // Convert 1-based to 0-based
        const currentStepConfig = getStepByIndex(licenseTypeCode, currentStepIndex);

        if (currentStepConfig) {
          targetStepRoute = currentStepConfig.route;
          console.log('Fallback: using current step from application:', application.current_step, 'route:', targetStepRoute);
        } else {
          // Final fallback: use first step
          targetStepRoute = licenseConfig.steps[0].route;
          console.log('Final fallback to first step:', targetStepRoute);
        }
      }

      // Build the continue URL using the license type code instead of UUID
      const continueUrl = `/customer/applications/apply/${licenseTypeCode}/${application.license_category_id}/${targetStepRoute}?app=${application.application_id}`;

      console.log('Continue URL:', continueUrl);
      console.log('Application data:', {
        applicationId: application.application_id,
        licenseTypeId,
        licenseTypeCode,
        licenseCategoryId: application.license_category_id,
        currentStep: application.current_step,
        targetStep: targetStepRoute
      });

      router.push(continueUrl);
    } catch (error) {
      console.error('Error determining continue step:', error);
      setError('Unable to continue application: Error determining next step. Please try again.');
    }
  };

  // Check if application can be continued (not submitted and incomplete)
  const canContinueApplication = (application: Application) => {
    // Only allow continuation if:
    // 1. Progress is less than 100% (not completed)
    // 2. Status is not 'submitted', 'approved', or 'rejected'
    // 3. Application is still in draft/progress state
    const completionRate = application.progress_percentage || 0;
    const excludedStatuses = ['submitted', 'approved', 'rejected', 'under_review', 'evaluation'];

    return completionRate < 100 && !excludedStatuses.includes(application.status);
  };

  // Function to fetch user applications
  const fetchUserApplications = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      console.log('Fetching user applications...');

      const applicationsData = await applicationService.getUserApplications();
      console.log('User applications response:', applicationsData);

      // Ensure we have an array
      const applications = Array.isArray(applicationsData) ? applicationsData : [];

      // Fix empty status values and add default values
      const processedApplications = applications.map((app: any) => ({
        ...app,
        status: app.status || 'draft', // Default empty status to 'draft'
        progress_percentage: app.progress_percentage || 0,
        current_step: app.current_step || 1,
        application_number: app.application_number || `APP-${app.application_id?.slice(0, 8)}`,
        license_category: app.license_category ? {
          ...app.license_category, // Preserve all license_category properties including license_type_id
          name: app.license_category.name || 'License Category',
          description: app.license_category.description || 'Category description'
        } : {
          name: 'License Category',
          description: 'Category description'
        }
      }));

      console.log('Processed applications:', processedApplications);

      // Log status distribution for debugging
      if (processedApplications.length > 0) {
        const statusCounts = processedApplications.reduce((acc: Record<string, number>, app: Application) => {
          acc[app.status] = (acc[app.status] || 0) + 1;
          return acc;
        }, {});
        console.log('Status distribution:', statusCounts);
      }

      setApplications(processedApplications);

    } catch (err: unknown) {
      console.error('Applications fetch error:', err);
      const error = err as { response?: { status?: number; data?: { message?: string } }; message?: string };

      if (error.response?.status === 404) {
        // No applications found - this is okay
        setApplications([]);
        setError(null);
      } else if (error.response?.status === 401) {
        setError('Authentication required. Please log in again.');
        router.push('/customer/auth/login');
        return;
      } else {
        setError(error.response?.data?.message || error.message || 'Failed to fetch applications');
      }
    } finally {
      setLoading(false);
    }
  }, [router]);



  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }

    if (isAuthenticated) {
      // Add a small delay to avoid rate limiting
      const timer = setTimeout(() => {
        fetchUserApplications(); // Load user applications
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [isAuthenticated, router, fetchUserApplications]);

  // Show loading state
  if (loading) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading your licenses...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Show error state
  if (error) {
    return (
      <CustomerLayout breadcrumbs={breadcrumbs}>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="text-red-600 dark:text-red-400 mb-4">
              <i className="ri-error-warning-line text-4xl"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Failed to load applications
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <button
              type="button"
              onClick={() => {
                // Add delay to avoid rate limiting
                setTimeout(() => fetchUserApplications(), 500);
              }}
              className="bg-primary text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
            >
              Try Again
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Client-side filtering with proper debugging
  const filteredApplications = applications.filter((app: Application) => {
    if (activeFilter === 'all') return true;

    // Special handling for "in_progress" filter
    if (activeFilter === 'in_progress') {
      return canContinueApplication(app);
    }

    const matches = app.status === activeFilter;

    // Debug logging for submitted filter
    if (activeFilter === 'submitted') {
      console.log(`App ${app.application_number}: status="${app.status}", matches=${matches}`);
    }

    return matches;
  });

  // Debug: Log filter results
  console.log(`Active filter: ${activeFilter}, Total apps: ${applications.length}, Filtered: ${filteredApplications.length}`);

  // Debug: Show all unique statuses in the data
  const uniqueStatuses = [...new Set(applications.map(app => app.status))];
  console.log('Unique statuses in data:', uniqueStatuses);

  const getStatusBadge = (status: string): React.ReactElement => {
    const statusConfig = {
      'draft': { color: 'bg-blue-100 text-blue-800', label: 'Draft' },
      'submitted': { color: 'bg-blue-100 text-blue-800', label: 'Submitted' },
      'under_review': { color: 'bg-yellow-100 text-yellow-800', label: 'Under Review' },
      'evaluation': { color: 'bg-purple-100 text-purple-800', label: 'Evaluation' },
      'approved': { color: 'bg-green-100 text-green-800', label: 'Approved' },
      'rejected': { color: 'bg-red-100 text-red-800', label: 'Rejected' }
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.submitted;
    return (
      <span className={`px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${config.color}`}>
        {config.label}
      </span>
    );
  };

  return (
    <CustomerLayout breadcrumbs={breadcrumbs}>
      <div className="max-w-7xl mx-auto">
        {/* Page header */}
        <div className="mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">My Licenses</h1>
            <p className="mt-2 text-gray-600 dark:text-gray-400">
              Track your license applications and manage your approved licenses.
            </p>
          </div>
        </div>

        {/* Success Message */}
        {showSuccessMessage && (
          <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <i className="ri-check-circle-line text-green-600 dark:text-green-400 text-xl mr-3"></i>
              <div>
                <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                  Application Submitted Successfully!
                </h3>
                <p className="text-sm text-green-700 dark:text-green-300 mt-1">
                  Your license application has been submitted and is now under review. You will receive email notifications about status updates.
                </p>
              </div>
              <button
                onClick={() => setShowSuccessMessage(false)}
                className="ml-auto text-green-600 dark:text-green-400 hover:text-green-800 dark:hover:text-green-200"
              >
                <i className="ri-close-line text-lg"></i>
              </button>
            </div>
          </div>
        )}

        {/* Filter tabs */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Filter Applications</h2>
          <div className="flex flex-wrap gap-2">
            {[
              { key: 'all', label: 'All Applications' },
              { key: 'in_progress', label: 'In Progress' },
              { key: 'submitted', label: 'Submitted' },
              { key: 'under_review', label: 'Under Review' },
              { key: 'evaluation', label: 'Evaluation' },
              { key: 'approved', label: 'Approved' },
              { key: 'rejected', label: 'Rejected' },
              { key: 'draft', label: 'Draft' }
            ].map(filter => (
              <button
                key={filter.key}
                type="button"
                onClick={() => setActiveFilter(filter.key)}
                className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  activeFilter === filter.key
                    ? 'bg-primary text-white'
                    : 'bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-600'
                }`}
              >
                {filter.label}
              </button>
            ))}
          </div>
        </div>

        {/* Applications table */}
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Applications Overview</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              View and track all your license applications
            </p>
          </div>

          {filteredApplications.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-400 dark:text-gray-500 mb-4">
                <i className="ri-file-list-line text-4xl"></i>
              </div>
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
                No applications found
              </h3>
              <p className="text-gray-600 dark:text-gray-400">
                {activeFilter === 'all'
                  ? "You haven't submitted any license applications yet."
                  : `No applications with status "${activeFilter.replace('_', ' ')}" found.`
                }
              </p>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Application Details
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      License Category
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Status
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Progress
                    </th>
                    <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                      Submitted
                    </th>
                    <th scope="col" className="relative px-6 py-3">
                      <span className="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredApplications.map((application) => (
                    <tr key={application.application_id} className="hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mr-3">
                            <i className="ri-file-text-line text-blue-600 dark:text-blue-400"></i>
                          </div>
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                              {application.application_number}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              Application ID: {application.application_id.slice(0, 8)}...
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {application.license_category?.name || 'License Category'}
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          {application.license_category?.description || 'Category description'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusBadge(application.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mr-2 relative overflow-hidden">
                            <div
                              className={`${
                                canContinueApplication(application) ? 'bg-blue-500' : 'bg-primary'
                              } h-2 rounded-full transition-all duration-300 absolute top-0 left-0 ${
                                (application.progress_percentage || 0) >= 100 ? 'w-full' :
                                (application.progress_percentage || 0) >= 75 ? 'w-3/4' :
                                (application.progress_percentage || 0) >= 50 ? 'w-1/2' :
                                (application.progress_percentage || 0) >= 25 ? 'w-1/4' :
                                (application.progress_percentage || 0) > 0 ? 'w-1/12' : 'w-0'
                              }`}
                            />
                          </div>
                          <div className="flex flex-col">
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {application.progress_percentage || 0}%
                            </span>
                            {canContinueApplication(application) && (
                              <span className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                                In Progress
                              </span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-gray-100">
                          {application.submitted_at
                            ? new Date(application.submitted_at).toLocaleDateString()
                            : new Date(application.created_at).toLocaleDateString()
                          }
                        </div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">
                          Step {application.current_step} of 6
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div className="flex items-center justify-end space-x-2">
                          {/* Continue Application Button - for in-progress applications */}
                          {canContinueApplication(application) && (
                            <button
                              type="button"
                              title="Continue working on this application"
                              onClick={() => handleContinueApplication(application)}
                              className="inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 text-xs font-medium rounded-full hover:bg-blue-200 transition-colors"
                            >
                              <i className="ri-edit-line"></i>
                              View Continue
                            </button>
                          )}

                          {/* View Status Button */}
                          <button
                            type="button"
                            title="View application status"
                            onClick={() => openStatusModal(application)}
                            className="text-primary hover:text-red-700 transition-colors"
                          >
                            <i className="ri-eye-line"></i> View
                          </button>

                          {/* Download License Button - for approved applications */}
                          {application.status === 'approved' && (
                            <button
                              type="button"
                              title="Download license"
                              className="text-primary hover:text-red-700 transition-colors"
                            >
                              <i className="ri-download-line"></i>
                            </button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>
      </div>

      {/* Status Modal */}
      <StatusModal
        isOpen={isModalOpen}
        onClose={closeStatusModal}
        application={selectedApplication}
        onContinueApplication={handleContinueApplication}
        canContinueApplication={canContinueApplication}
      />
    </CustomerLayout>
  );
};

export default MyLicensesPage;
