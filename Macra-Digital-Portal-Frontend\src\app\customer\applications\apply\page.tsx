'use client';

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { licenseCategoryService, LicenseCategory } from '@/services/licenseCategoryService';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';

// Import step components
import ApplicantInfo from '@/components/customer/application/steps/ApplicantInfo';
import CompanyProfile from '@/components/customer/application/steps/CompanyProfile';
import Management from '@/components/customer/application/steps/Management';
import ProfessionalServices from '@/components/customer/application/steps/ProfessionalServices';
import BusinessInfo from '@/components/customer/application/steps/BusinessInfo';
import ServiceScope from '@/components/customer/application/steps/ServiceScope';
import BusinessPlan from '@/components/customer/application/steps/BusinessPlan';
import LegalHistory from '@/components/customer/application/steps/LegalHistory';
import ReviewSubmit from '@/components/customer/application/steps/ReviewSubmit';

// Define application steps (excluding ReviewSubmit which has different props)
const APPLICATION_STEPS = [
  { id: 'applicant-info', name: 'Applicant Information', component: ApplicantInfo },
  { id: 'company-profile', name: 'Company Profile', component: CompanyProfile },
  { id: 'management', name: 'Management Team', component: Management },
  { id: 'professional-services', name: 'Professional Services', component: ProfessionalServices },
  { id: 'business-info', name: 'Business Information', component: BusinessInfo },
  { id: 'service-scope', name: 'Service Scope', component: ServiceScope },
  { id: 'business-plan', name: 'Business Plan', component: BusinessPlan },
  { id: 'legal-history', name: 'Legal History', component: LegalHistory },
  { id: 'review-submit', name: 'Review & Submit', component: null }, // Special handling
];

const ApplicationFormPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');
  const stepParam = searchParams.get('step') || 'applicant-info';

  // State
  const [licenseCategory, setLicenseCategory] = useState<LicenseCategory | null>(null);
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Find current step index
  useEffect(() => {
    const stepIndex = APPLICATION_STEPS.findIndex(step => step.id === stepParam);
    setCurrentStepIndex(stepIndex >= 0 ? stepIndex : 0);
  }, [stepParam]);

  // Authentication check
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/customer/auth/login');
      return;
    }
  }, [isAuthenticated, authLoading, router]);

  // Validate required parameters and load data
  useEffect(() => {
    const loadApplicationData = async () => {
      if (!licenseCategoryId) {
        setError('License category is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);
        setError(null);

        // Load license category data
        const category = await licenseCategoryService.getLicenseCategory(licenseCategoryId);
        if (!category) {
          setError('License category not found');
          setLoading(false);
          return;
        }

        setLicenseCategory(category);

        // If editing existing application, validate application exists
        if (applicationId) {
          try {
            const application = await applicationService.getApplicationById(applicationId);
            if (!application) {
              setError('Application not found');
              setLoading(false);
              return;
            }
            // Validate that application belongs to the specified license category
            if (application.license_category_id !== licenseCategoryId) {
              setError('Application does not match the specified license category');
              setLoading(false);
              return;
            }
          } catch (err) {
            console.error('Error loading application:', err);
            setError('Failed to load application data');
            setLoading(false);
            return;
          }
        }

        setLoading(false);
      } catch (err) {
        console.error('Error loading application data:', err);
        setError('Failed to load application data');
        setLoading(false);
      }
    };

    if (isAuthenticated && !authLoading) {
      loadApplicationData();
    }
  }, [licenseCategoryId, applicationId, isAuthenticated, authLoading]);

  // Navigation functions
  const updateUrlStep = (stepId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('step', stepId);
    router.push(`/customer/applications/apply?${params.toString()}`);
  };

  const handleNextStep = () => {
    if (currentStepIndex < APPLICATION_STEPS.length - 1) {
      const nextStep = APPLICATION_STEPS[currentStepIndex + 1];
      updateUrlStep(nextStep.id);
    }
  };

  const handlePreviousStep = () => {
    if (currentStepIndex > 0) {
      const prevStep = APPLICATION_STEPS[currentStepIndex - 1];
      updateUrlStep(prevStep.id);
    }
  };

  const handleStepClick = (stepIndex: number) => {
    const step = APPLICATION_STEPS[stepIndex];
    updateUrlStep(step.id);
  };

  const handleBackToApplications = () => {
    router.push('/customer/applications');
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading application form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-96">
          <div className="text-center">
            <div className="mb-4">
              <i className="ri-error-warning-line text-4xl text-red-500"></i>
            </div>
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Error Loading Application
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              {error}
            </p>
            <button
              onClick={handleBackToApplications}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Back to Applications
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  const currentStep = APPLICATION_STEPS[currentStepIndex];
  const CurrentStepComponent = currentStep.component;

  return (
    <CustomerLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <button
            onClick={handleBackToApplications}
            className="inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mb-4"
          >
            <i className="ri-arrow-left-line mr-1"></i>
            Back to Applications
          </button>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            {licenseCategory?.name} Application
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            {applicationId ? 'Edit your application' : 'Complete your license application'}
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <nav aria-label="Progress">
            <ol className="flex items-center">
              {APPLICATION_STEPS.map((step, index) => (
                <li key={step.id} className={`relative ${index !== APPLICATION_STEPS.length - 1 ? 'pr-8 sm:pr-20' : ''}`}>
                  {index !== APPLICATION_STEPS.length - 1 && (
                    <div className="absolute inset-0 flex items-center" aria-hidden="true">
                      <div className="h-0.5 w-full bg-gray-200 dark:bg-gray-700" />
                    </div>
                  )}
                  <button
                    onClick={() => handleStepClick(index)}
                    className={`relative w-8 h-8 flex items-center justify-center rounded-full border-2 ${
                      index === currentStepIndex
                        ? 'border-primary bg-primary text-white'
                        : index < currentStepIndex
                        ? 'border-primary bg-primary text-white'
                        : 'border-gray-300 bg-white text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400'
                    } hover:border-primary transition-colors`}
                  >
                    {index < currentStepIndex ? (
                      <i className="ri-check-line text-sm"></i>
                    ) : (
                      <span className="text-sm font-medium">{index + 1}</span>
                    )}
                  </button>
                  <span className="absolute top-10 left-1/2 transform -translate-x-1/2 text-xs font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap">
                    {step.name}
                  </span>
                </li>
              ))}
            </ol>
          </nav>
        </div>

        {/* Current Step Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-medium text-gray-900 dark:text-gray-100">
              {currentStep.name}
            </h2>
          </div>

          <div className="p-6">
            {currentStep.id === 'review-submit' ? (
              <ReviewSubmit
                formData={{}}
                allFormData={{}}
                onChange={() => {}}
                onSave={async () => ''}
                onSubmit={async () => {}}
                errors={{}}
                applicationId={applicationId || undefined}
                isLoading={loading}
                isSubmitting={false}
              />
            ) : CurrentStepComponent ? (
              <CurrentStepComponent
                licenseCategoryId={licenseCategoryId!}
                applicationId={applicationId || undefined}
                licenseTypeId={licenseCategory?.license_type_id}
                isEditMode={!!applicationId}
                onNext={handleNextStep}
                onPrevious={handlePreviousStep}
                isFirstStep={currentStepIndex === 0}
                isLastStep={currentStepIndex === APPLICATION_STEPS.length - 1}
              />
            ) : (
              <div>Step component not found</div>
            )}
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default ApplicationFormPage;
