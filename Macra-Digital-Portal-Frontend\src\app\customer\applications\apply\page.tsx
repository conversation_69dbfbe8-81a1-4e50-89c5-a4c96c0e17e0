'use client';

import React, { useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { licenseCategoryService } from '@/services/licenseCategoryService';
import { applicationService } from '@/services/applicationService';

const ApplicationFormPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');
  const stepParam = searchParams.get('step');

  // Handle routing - redirect to appropriate step page
  useEffect(() => {
    const handleRouting = async () => {
      // Authentication check
      if (!authLoading && !isAuthenticated) {
        router.push('/customer/auth/login');
        return;
      }

      if (!isAuthenticated || authLoading) {
        return;
      }

      // Validate required parameters
      if (!licenseCategoryId) {
        router.push('/customer/applications');
        return;
      }

      try {
        // Validate license category exists
        const category = await licenseCategoryService.getLicenseCategory(licenseCategoryId);
        if (!category) {
          router.push('/customer/applications');
          return;
        }

        // If editing existing application, validate application exists
        if (applicationId) {
          try {
            const application = await applicationService.getApplication(applicationId);
            if (!application || application.license_category_id !== licenseCategoryId) {
              router.push('/customer/applications');
              return;
            }
          } catch (err) {
            console.error('Error loading application:', err);
            router.push('/customer/applications');
            return;
          }
        }

        // Redirect to the appropriate step page
        const targetStep = stepParam || 'applicant-info';
        const params = new URLSearchParams();
        params.set('license_category_id', licenseCategoryId);
        if (applicationId) {
          params.set('application_id', applicationId);
        }

        router.push(`/customer/applications/apply/${targetStep}?${params.toString()}`);
      } catch (err) {
        console.error('Error in routing logic:', err);
        router.push('/customer/applications');
      }
    };

    handleRouting();
  }, [licenseCategoryId, applicationId, stepParam, isAuthenticated, authLoading, router]);

  // Show loading while redirecting
  return (
    <div className="flex items-center justify-center min-h-screen">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
        <p className="text-gray-600 dark:text-gray-400">Redirecting to application form...</p>
      </div>
    </div>
  );
};

export default ApplicationFormPage;
