"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientSystemsModule = void 0;
const common_1 = require("@nestjs/common");
const typeorm_1 = require("@nestjs/typeorm");
const client_systems_controller_1 = require("./client-systems.controller");
const client_systems_service_1 = require("./client-systems.service");
const client_systems_entity_1 = require("../entities/client-systems.entity");
let ClientSystemsModule = class ClientSystemsModule {
};
exports.ClientSystemsModule = ClientSystemsModule;
exports.ClientSystemsModule = ClientSystemsModule = __decorate([
    (0, common_1.Module)({
        imports: [typeorm_1.TypeOrmModule.forFeature([client_systems_entity_1.ClientSystems])],
        controllers: [client_systems_controller_1.ClientSystemsController],
        providers: [client_systems_service_1.ClientSystemsService],
        exports: [client_systems_service_1.ClientSystemsService],
    })
], ClientSystemsModule);
//# sourceMappingURL=client-systems.module.js.map