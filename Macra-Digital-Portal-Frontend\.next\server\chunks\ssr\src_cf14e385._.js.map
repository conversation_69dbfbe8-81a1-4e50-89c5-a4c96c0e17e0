{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/LogoutButton.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport { useState } from 'react';\r\nimport { useRouter, usePathname } from 'next/navigation';\r\nimport { useAuth } from '../contexts/AuthContext';\r\n\r\ninterface LogoutButtonProps {\r\n  variant?: 'primary' | 'secondary' | 'text' | 'icon';\r\n  size?: 'sm' | 'md' | 'lg';\r\n  className?: string;\r\n  showConfirmation?: boolean;\r\n  redirectTo?: string;\r\n  children?: React.ReactNode;\r\n}\r\n\r\nexport default function LogoutButton({\r\n  variant = 'primary',\r\n  size = 'md',\r\n  className = '',\r\n  showConfirmation = true,\r\n  redirectTo = '/auth/login',\r\n  children,\r\n}: LogoutButtonProps) {\r\n  const [isLoggingOut, setIsLoggingOut] = useState(false);\r\n  const [showConfirm, setShowConfirm] = useState(false);\r\n  const { logout, user } = useAuth();\r\n  const router = useRouter();\r\n  const pathname = usePathname();\r\n\r\n  const handleLogout = async () => {\r\n    if (showConfirmation && !showConfirm) {\r\n      setShowConfirm(true);\r\n      return;\r\n    }\r\n\r\n    setIsLoggingOut(true);\r\n    \r\n    try {\r\n      // Call logout from context\r\n      logout();\r\n      \r\n      // Small delay to ensure cleanup is complete\r\n      await new Promise(resolve => setTimeout(resolve, 100));\r\n\r\n      if(pathname.includes('customer')){\r\n        // Redirect to specified page\r\n        router.push('/customer/auth/login');\r\n      } else {\r\n        router.push('/auth/login');\r\n      }\r\n      \r\n\r\n      \r\n    } catch (error) {\r\n      console.error('Logout error:', error);\r\n    } finally {\r\n      setIsLoggingOut(false);\r\n      setShowConfirm(false);\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => {\r\n    setShowConfirm(false);\r\n  };\r\n\r\n  // Base styles for different variants\r\n  const getVariantStyles = () => {\r\n    switch (variant) {\r\n      case 'primary':\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n      case 'secondary':\r\n        return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';\r\n      case 'text':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none';\r\n      case 'icon':\r\n        return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';\r\n      default:\r\n        return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';\r\n    }\r\n  };\r\n\r\n  // Size styles\r\n  const getSizeStyles = () => {\r\n    switch (size) {\r\n      case 'sm':\r\n        return 'px-3 py-1.5 text-sm';\r\n      case 'md':\r\n        return 'px-4 py-2 text-base';\r\n      case 'lg':\r\n        return 'px-6 py-3 text-lg';\r\n      default:\r\n        return 'px-4 py-2 text-base';\r\n    }\r\n  };\r\n\r\n  const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';\r\n  \r\n  const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;\r\n\r\n  // Confirmation dialog\r\n  if (showConfirm) {\r\n    return (\r\n      <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\r\n        <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl\">\r\n          <div className=\"flex items-center mb-4\">\r\n            <div className=\"flex-shrink-0\">\r\n              <svg className=\"h-6 w-6 text-red-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z\" />\r\n              </svg>\r\n            </div>\r\n            <div className=\"ml-3\">\r\n              <h3 className=\"text-lg font-medium text-gray-900\">Confirm Logout</h3>\r\n            </div>\r\n          </div>\r\n          \r\n          <div className=\"mb-4\">\r\n            <p className=\"text-sm text-gray-500\">\r\n              Are you sure you want to logout{user?.first_name ? `, ${user.first_name}` : ''}? You will need to login again to access your account.\r\n            </p>\r\n          </div>\r\n          \r\n          <div className=\"flex space-x-3\">\r\n            <button\r\n              onClick={handleLogout}\r\n              disabled={isLoggingOut}\r\n              className=\"flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50\"\r\n            >\r\n              {isLoggingOut ? 'Logging out...' : 'Yes, Logout'}\r\n            </button>\r\n            <button\r\n              onClick={handleCancel}\r\n              className=\"flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200\"\r\n            >\r\n              Cancel\r\n            </button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <button\r\n      onClick={handleLogout}\r\n      disabled={isLoggingOut}\r\n      className={buttonStyles}\r\n      title=\"Logout\"\r\n    >\r\n      {children || (\r\n        <>\r\n          {variant === 'icon' ? (\r\n            <svg className=\"h-5 w-5\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n            </svg>\r\n          ) : (\r\n            <>\r\n              <svg className=\"h-4 w-4 mr-2\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\r\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\r\n              </svg>\r\n              {isLoggingOut ? 'Logging out...' : 'Logout'}\r\n            </>\r\n          )}\r\n        </>\r\n      )}\r\n    </button>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAee,SAAS,aAAa,EACnC,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,YAAY,EAAE,EACd,mBAAmB,IAAI,EACvB,aAAa,aAAa,EAC1B,QAAQ,EACU;IAClB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAE3B,MAAM,eAAe;QACnB,IAAI,oBAAoB,CAAC,aAAa;YACpC,eAAe;YACf;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,2BAA2B;YAC3B;YAEA,4CAA4C;YAC5C,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,IAAG,SAAS,QAAQ,CAAC,aAAY;gBAC/B,6BAA6B;gBAC7B,OAAO,IAAI,CAAC;YACd,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QAIF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,gBAAgB;YAChB,eAAe;QACjB;IACF;IAEA,MAAM,eAAe;QACnB,eAAe;IACjB;IAEA,qCAAqC;IACrC,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,cAAc;IACd,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,aAAa;IAEnB,MAAM,eAAe,GAAG,WAAW,CAAC,EAAE,mBAAmB,CAAC,EAAE,YAAY,SAAS,kBAAkB,GAAG,CAAC,EAAE,WAAW;IAEpH,sBAAsB;IACtB,IAAI,aAAa;QACf,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAuB,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CAC3E,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;;;;;;;;;;;;kCAItD,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;gCAAwB;gCACH,MAAM,aAAa,CAAC,EAAE,EAAE,KAAK,UAAU,EAAE,GAAG;gCAAG;;;;;;;;;;;;kCAInF,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCACC,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,eAAe,mBAAmB;;;;;;0CAErC,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,qBACE,8OAAC;QACC,SAAS;QACT,UAAU;QACV,WAAW;QACX,OAAM;kBAEL,0BACC;sBACG,YAAY,uBACX,8OAAC;gBAAI,WAAU;gBAAU,MAAK;gBAAO,SAAQ;gBAAY,QAAO;0BAC9D,cAAA,8OAAC;oBAAK,eAAc;oBAAQ,gBAAe;oBAAQ,aAAa;oBAAG,GAAE;;;;;;;;;;qCAGvE;;kCACE,8OAAC;wBAAI,WAAU;wBAAe,MAAK;wBAAO,SAAQ;wBAAY,QAAO;kCACnE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;oBAEtE,eAAe,mBAAmB;;;;;;;;;AAOjD", "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/CustomerLayout.tsx"], "sourcesContent": ["'use client';\r\n\r\nimport React, { useState, useEffect, useMemo, useCallback } from 'react';\r\nimport Link from 'next/link';\r\nimport Image from 'next/image';\r\nimport { usePathname, useRouter } from 'next/navigation';\r\nimport { useAuth } from '@/contexts/AuthContext';\r\nimport { useLoading } from '@/contexts/LoadingContext';\r\nimport LogoutButton from '../LogoutButton';\r\n\r\ninterface BreadcrumbItem {\r\n  label: string;\r\n  href?: string;\r\n}\r\n\r\ninterface CustomerLayoutProps {\r\n  children: React.ReactNode;\r\n  breadcrumbs?: BreadcrumbItem[];\r\n}\r\n\r\nconst CustomerLayout: React.FC<CustomerLayoutProps> = ({ children, breadcrumbs }) => {\r\n  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);\r\n  const [isUserDropdownOpen, setIsUserDropdownOpen] = useState(false);\r\n  const pathname = usePathname();\r\n  const router = useRouter();\r\n  const { user, logout } = useAuth();\r\n  const { showLoader } = useLoading();\r\n\r\n  // Memoize navigation items to prevent unnecessary re-renders\r\n  const navigationItems = useMemo(() => [\r\n    {\r\n      name: 'Dashboard',\r\n      href: '/customer',\r\n      icon: 'ri-dashboard-line',\r\n      current: pathname === '/customer'\r\n    },\r\n    {\r\n      name: 'My Licenses',\r\n      href: '/customer/my-licenses',\r\n      icon: 'ri-key-line',\r\n      current: pathname === '/customer/my-licenses'\r\n    },\r\n    {\r\n      name: 'New Applications',\r\n      href: '/customer/applications',\r\n      icon: 'ri-file-list-3-line',\r\n      current: pathname === '/customer/applications'\r\n    },\r\n    {\r\n      name: 'Payments',\r\n      href: '/customer/payments',\r\n      icon: 'ri-bank-card-line',\r\n      current: pathname === '/customer/payments'\r\n    },\r\n    {\r\n      name: 'Documents',\r\n      href: '/customer/documents',\r\n      icon: 'ri-file-text-line',\r\n      current: pathname === '/customer/documents'\r\n    },\r\n    {\r\n      name: 'Procurement',\r\n      href: '/customer/procurement',\r\n      icon: 'ri-auction-line',\r\n      current: pathname === '/customer/procurement'\r\n    },\r\n    {\r\n      name: 'Request Resource',\r\n      href: '/customer/resources',\r\n      icon: 'ri-hand-heart-line',\r\n      current: pathname === '/customer/resources'\r\n    }\r\n  ], [pathname]);\r\n\r\n  const supportItems = useMemo(() => [\r\n    {\r\n      name: 'Consumer Affairs',\r\n      href: '/customer/consumer-affairs',\r\n      icon: 'ri-shield-user-line'\r\n    },\r\n    {\r\n      name: 'Help Center',\r\n      href: '/customer/help',\r\n      icon: 'ri-question-line'\r\n    }\r\n  ], []);\r\n\r\n  // Prefetch customer pages on mount for faster navigation\r\n  useEffect(() => {\r\n    const prefetchPages = () => {\r\n      const customerPages = [\r\n        '/customer',\r\n        '/customer/applications',\r\n        '/customer/applications/standards',\r\n        '/customer/payments',\r\n        '/customer/my-licenses',\r\n        '/customer/procurement',\r\n        '/customer/profile',\r\n        '/customer/consumer-affairs',\r\n        '/customer/resources',\r\n        '/customer/help'\r\n      ];\r\n\r\n      customerPages.forEach(page => {\r\n        router.prefetch(page);\r\n      });\r\n    };\r\n\r\n    // Delay prefetching to not interfere with initial page load\r\n    const timer = setTimeout(prefetchPages, 1000);\r\n    return () => clearTimeout(timer);\r\n  }, [router]);\r\n\r\n  const toggleMobileSidebar = useCallback(() => {\r\n    setIsMobileSidebarOpen(!isMobileSidebarOpen);\r\n  }, [isMobileSidebarOpen]);\r\n\r\n  const handleNavClick = useCallback((href: string, name: string) => {\r\n    const pageMessages: { [key: string]: string } = {\r\n      '/customer': 'Loading Dashboard...',\r\n      '/customer/licenses': 'Loading My Licenses...',\r\n      '/customer/applications': 'Loading Applications...',\r\n      '/customer/applications/standards': 'Loading Standards License Options...',\r\n      '/customer/payments': 'Loading Payments...',\r\n      '/customer/documents': 'Loading Documents...',\r\n      '/customer/procurement': 'Loading Procurement...',\r\n      '/customer/resources': 'Loading Resources...',\r\n      '/customer/consumer-affairs': 'Loading Consumer Affairs...',\r\n      '/customer/help': 'Loading Help Center...',\r\n      '/customer/profile': 'Loading Profile...',\r\n      '/customer/settings': 'Loading Settings...'\r\n    };\r\n\r\n    const message = pageMessages[href] || `Loading ${name}...`;\r\n    showLoader(message);\r\n    setIsMobileSidebarOpen(false);\r\n  }, [showLoader]);\r\n\r\n  const handleNavHover = useCallback((href: string) => {\r\n    // Prefetch on hover for instant navigation\r\n    router.prefetch(href);\r\n  }, [router]);\r\n\r\n  const toggleUserDropdown = useCallback(() => {\r\n    setIsUserDropdownOpen(!isUserDropdownOpen);\r\n  }, [isUserDropdownOpen]);\r\n\r\n  return (\r\n    <div className=\"flex h-screen overflow-hidden\">\r\n      {/* Mobile sidebar overlay */}\r\n      {isMobileSidebarOpen && (\r\n        <div \r\n          className=\"fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden\"\r\n          onClick={() => setIsMobileSidebarOpen(false)}\r\n        />\r\n      )}\r\n\r\n      {/* Sidebar */}\r\n      <aside className={`\r\n        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated\r\n        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\r\n      `}>\r\n        <div className=\"flex flex-col h-full\">\r\n          {/* Logo/Header */}\r\n          <div className=\"h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700\">\r\n            <div className=\"flex items-center\">\r\n              <Image \r\n                src=\"/images/macra-logo.png\" \r\n                alt=\"MACRA Logo\" \r\n                className=\"max-h-12 w-auto\" \r\n                width={120}\r\n                height={48}\r\n                priority\r\n              />\r\n            </div>\r\n          </div>\r\n\r\n          {/* Navigation */}\r\n          <nav className=\"mt-6 px-4 side-nav\">\r\n            {/* Main Navigation */}\r\n            <div className=\"space-y-1\">\r\n              {navigationItems.map((item) => (\r\n                <Link\r\n                  key={item.name}\r\n                  href={item.href}\r\n                  onClick={() => handleNavClick(item.href, item.name)}\r\n                  onMouseEnter={() => handleNavHover(item.href)}\r\n                  className={`\r\n                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated\r\n                    ${item.current\r\n                      ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400'\r\n                      : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'\r\n                    }\r\n                  `}\r\n                >\r\n                  <div className={`w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`}>\r\n                    <i className={item.icon}></i>\r\n                  </div>\r\n                  {item.name}\r\n                </Link>\r\n              ))}\r\n            </div>\r\n\r\n            {/* Support Section */}\r\n            <div className=\"mt-8\">\r\n              <h3 className=\"px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider\">\r\n                Support\r\n              </h3>\r\n              <div className=\"mt-2 space-y-1\">\r\n                {supportItems.map((item) => (\r\n                  <Link\r\n                    key={item.name}\r\n                    href={item.href}\r\n                    onClick={() => handleNavClick(item.href, item.name)}\r\n                    onMouseEnter={() => handleNavHover(item.href)}\r\n                    className=\"flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100\"\r\n                  >\r\n                    <div className=\"w-5 h-5 flex items-center justify-center mr-3\">\r\n                      <i className={item.icon}></i>\r\n                    </div>\r\n                    {item.name}\r\n                  </Link>\r\n                ))}\r\n              </div>\r\n            </div>\r\n          </nav>\r\n\r\n          {/* User Info */}\r\n          <div className=\"absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800\">\r\n            <div className=\"flex items-center space-x-3\">\r\n              <Image\r\n                className=\"h-10 w-10 rounded-full object-cover\"\r\n                src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                alt=\"Profile\"\r\n                width={40}\r\n                height={40}\r\n              />\r\n              <Link \r\n                href='/customer/profile' \r\n                className=\"flex-1 min-w-0\"\r\n                onClick={() => handleNavClick('/customer/profile', 'Profile')}\r\n              >\r\n                <p className=\"text-sm font-medium text-gray-900 dark:text-gray-100 truncate\">\r\n                  {user ? `${user.first_name} ${user.last_name}` : 'Customer'}\r\n                </p>\r\n                <p className=\"text-xs text-gray-500 dark:text-gray-400 truncate\">\r\n                  {/* {user?.organizationName || 'Organization'} */}\r\n                </p>\r\n              </Link>\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </aside>\r\n\r\n      {/* Main content */}\r\n      <div className=\"flex-1 flex flex-col overflow-hidden\">\r\n        {/* Top header */}\r\n        <header className=\"bg-white dark:bg-gray-800 shadow-sm z-10\">\r\n          <div className=\"flex items-center justify-between h-16 px-4 sm:px-6\">\r\n            <button\r\n              type=\"button\"\r\n              onClick={toggleMobileSidebar}\r\n              className=\"md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none\"\r\n              aria-label=\"Open mobile menu\"\r\n            >\r\n              <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                <i className=\"ri-menu-line ri-lg\"></i>\r\n              </div>\r\n            </button>\r\n            \r\n            <div className=\"flex-1\">\r\n              {breadcrumbs && breadcrumbs.length > 0 && (\r\n                <nav className=\"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400\">\r\n                  {breadcrumbs.map((item, index) => (\r\n                    <React.Fragment key={index}>\r\n                      {index > 0 && <i className=\"ri-arrow-right-s-line\"></i>}\r\n                      {item.href ? (\r\n                        <Link href={item.href} className=\"hover:text-primary\">\r\n                          {item.label}\r\n                        </Link>\r\n                      ) : (\r\n                        <span className=\"text-gray-900 dark:text-gray-100\">{item.label}</span>\r\n                      )}\r\n                    </React.Fragment>\r\n                  ))}\r\n                </nav>\r\n              )}\r\n            </div>\r\n            \r\n            <div className=\"flex items-center\">\r\n              <button\r\n                type=\"button\"\r\n                className=\"flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative\"\r\n              >\r\n                <span className=\"sr-only\">View notifications</span>\r\n                <div className=\"w-6 h-6 flex items-center justify-center\">\r\n                  <i className=\"ri-notification-3-line ri-lg\"></i>\r\n                </div>\r\n                <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800\"></span>\r\n              </button>\r\n              \r\n              <div className=\"relative\">\r\n                <button\r\n                  type=\"button\"\r\n                  onClick={toggleUserDropdown}\r\n                  className=\"flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\r\n                >\r\n                  <span className=\"sr-only\">Open user menu</span>\r\n                  <Image\r\n                    className=\"h-8 w-8 rounded-full\"\r\n                    src={user?.profile_image || \"https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp\"}\r\n                    alt=\"Profile\"\r\n                    width={32}\r\n                    height={32}\r\n                  />\r\n                </button>\r\n                \r\n                {isUserDropdownOpen && (\r\n                  <div className=\"absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50\">\r\n                    <div className=\"py-1\">\r\n                      <Link\r\n                        href=\"/customer/profile\"\r\n                        className=\"block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        onClick={() => {\r\n                          handleNavClick('/customer/profile', 'Profile');\r\n                          setIsUserDropdownOpen(false);\r\n                        }}\r\n                      >\r\n                        Your Profile\r\n                      </Link>\r\n          \r\n                      <LogoutButton\r\n                        variant=\"text\"\r\n                        size=\"sm\"\r\n                        className=\"w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700\"\r\n                        showConfirmation={true}\r\n                      >\r\n                        Sign out\r\n                      </LogoutButton>\r\n                    </div>\r\n                  </div>\r\n                )}\r\n              </div>\r\n            </div>\r\n          </div>\r\n        </header>\r\n\r\n        {/* Main content area */}\r\n        <main className=\"flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900\">\r\n          <div className=\"py-6\">\r\n            <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\r\n              {children}\r\n            </div>\r\n          </div>\r\n        </main>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CustomerLayout;"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAoBA,MAAM,iBAAgD,CAAC,EAAE,QAAQ,EAAE,WAAW,EAAE;IAC9E,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,aAAU,AAAD;IAEhC,6DAA6D;IAC7D,MAAM,kBAAkB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACpC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;gBACN,SAAS,aAAa;YACxB;SACD,EAAE;QAAC;KAAS;IAEb,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAM;YACjC;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM;YACR;SACD,EAAE,EAAE;IAEL,yDAAyD;IACzD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB;YACpB,MAAM,gBAAgB;gBACpB;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;aACD;YAED,cAAc,OAAO,CAAC,CAAA;gBACpB,OAAO,QAAQ,CAAC;YAClB;QACF;QAEA,4DAA4D;QAC5D,MAAM,QAAQ,WAAW,eAAe;QACxC,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAO;IAEX,MAAM,sBAAsB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACtC,uBAAuB,CAAC;IAC1B,GAAG;QAAC;KAAoB;IAExB,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,MAAc;QAChD,MAAM,eAA0C;YAC9C,aAAa;YACb,sBAAsB;YACtB,0BAA0B;YAC1B,oCAAoC;YACpC,sBAAsB;YACtB,uBAAuB;YACvB,yBAAyB;YACzB,uBAAuB;YACvB,8BAA8B;YAC9B,kBAAkB;YAClB,qBAAqB;YACrB,sBAAsB;QACxB;QAEA,MAAM,UAAU,YAAY,CAAC,KAAK,IAAI,CAAC,QAAQ,EAAE,KAAK,GAAG,CAAC;QAC1D,WAAW;QACX,uBAAuB;IACzB,GAAG;QAAC;KAAW;IAEf,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAClC,2CAA2C;QAC3C,OAAO,QAAQ,CAAC;IAClB,GAAG;QAAC;KAAO;IAEX,MAAM,qBAAqB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACrC,sBAAsB,CAAC;IACzB,GAAG;QAAC;KAAmB;IAEvB,qBACE,8OAAC;QAAI,WAAU;;YAEZ,qCACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,uBAAuB;;;;;;0BAK1C,8OAAC;gBAAM,WAAW,CAAC;;QAEjB,EAAE,sBAAsB,kBAAkB,qCAAqC;MACjF,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,WAAU;oCACV,OAAO;oCACP,QAAQ;oCACR,QAAQ;;;;;;;;;;;;;;;;sCAMd,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAC,qBACpB,8OAAC,4JAAA,CAAA,UAAI;4CAEH,MAAM,KAAK,IAAI;4CACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;4CAClD,cAAc,IAAM,eAAe,KAAK,IAAI;4CAC5C,WAAW,CAAC;;oBAEV,EAAE,KAAK,OAAO,GACV,8GACA,wHACH;kBACH,CAAC;;8DAED,8OAAC;oDAAI,WAAW,CAAC,8CAA8C,EAAE,KAAK,OAAO,GAAG,mCAAmC,IAAI;8DACrH,cAAA,8OAAC;wDAAE,WAAW,KAAK,IAAI;;;;;;;;;;;gDAExB,KAAK,IAAI;;2CAfL,KAAK,IAAI;;;;;;;;;;8CAqBpB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAuF;;;;;;sDAGrG,8OAAC;4CAAI,WAAU;sDACZ,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,4JAAA,CAAA,UAAI;oDAEH,MAAM,KAAK,IAAI;oDACf,SAAS,IAAM,eAAe,KAAK,IAAI,EAAE,KAAK,IAAI;oDAClD,cAAc,IAAM,eAAe,KAAK,IAAI;oDAC5C,WAAU;;sEAEV,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAE,WAAW,KAAK,IAAI;;;;;;;;;;;wDAExB,KAAK,IAAI;;mDATL,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;sCAiBxB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6HAAA,CAAA,UAAK;wCACJ,WAAU;wCACV,KAAK,MAAM,iBAAiB;wCAC5B,KAAI;wCACJ,OAAO;wCACP,QAAQ;;;;;;kDAEV,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;wCACV,SAAS,IAAM,eAAe,qBAAqB;;0DAEnD,8OAAC;gDAAE,WAAU;0DACV,OAAO,GAAG,KAAK,UAAU,CAAC,CAAC,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;0DAEnD,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;oCACV,cAAW;8CAEX,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAE,WAAU;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,YAAY,MAAM,GAAG,mBACnC,8OAAC;wCAAI,WAAU;kDACZ,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,8OAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ;;oDACZ,QAAQ,mBAAK,8OAAC;wDAAE,WAAU;;;;;;oDAC1B,KAAK,IAAI,iBACR,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAM,KAAK,IAAI;wDAAE,WAAU;kEAC9B,KAAK,KAAK;;;;;6EAGb,8OAAC;wDAAK,WAAU;kEAAoC,KAAK,KAAK;;;;;;;+CAP7C;;;;;;;;;;;;;;;8CAe7B,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,WAAU;;8DAEV,8OAAC;oDAAK,WAAU;8DAAU;;;;;;8DAC1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;;;;;;;;;;8DAEf,8OAAC;oDAAK,WAAU;;;;;;;;;;;;sDAGlB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDACC,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,8OAAC;4DAAK,WAAU;sEAAU;;;;;;sEAC1B,8OAAC,6HAAA,CAAA,UAAK;4DACJ,WAAU;4DACV,KAAK,MAAM,iBAAiB;4DAC5B,KAAI;4DACJ,OAAO;4DACP,QAAQ;;;;;;;;;;;;gDAIX,oCACC,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,4JAAA,CAAA,UAAI;gEACH,MAAK;gEACL,WAAU;gEACV,SAAS;oEACP,eAAe,qBAAqB;oEACpC,sBAAsB;gEACxB;0EACD;;;;;;0EAID,8OAAC,kIAAA,CAAA,UAAY;gEACX,SAAQ;gEACR,MAAK;gEACL,WAAU;gEACV,kBAAkB;0EACnB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAYf,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;uCAEe", "debugId": null}}, {"offset": {"line": 873, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/cacheService.ts"], "sourcesContent": ["// Cache service for API responses to reduce rate limiting\n\ninterface CacheItem<T> {\n  data: T;\n  timestamp: number;\n  expiresAt: number;\n}\n\nclass CacheService {\n  private cache = new Map<string, CacheItem<any>>();\n  private defaultTTL = 5 * 60 * 1000; // 5 minutes default TTL\n\n  /**\n   * Set cache item with TTL\n   */\n  set<T>(key: string, data: T, ttl: number = this.defaultTTL): void {\n    const now = Date.now();\n    const item: CacheItem<T> = {\n      data,\n      timestamp: now,\n      expiresAt: now + ttl\n    };\n    \n    this.cache.set(key, item);\n    console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);\n  }\n\n  /**\n   * Get cache item if not expired\n   */\n  get<T>(key: string): T | null {\n    const item = this.cache.get(key);\n    \n    if (!item) {\n      console.log(`Cache MISS: ${key}`);\n      return null;\n    }\n\n    const now = Date.now();\n    if (now > item.expiresAt) {\n      console.log(`Cache EXPIRED: ${key}`);\n      this.cache.delete(key);\n      return null;\n    }\n\n    console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);\n    return item.data as T;\n  }\n\n  /**\n   * Check if cache has valid item\n   */\n  has(key: string): boolean {\n    return this.get(key) !== null;\n  }\n\n  /**\n   * Delete cache item\n   */\n  delete(key: string): boolean {\n    console.log(`Cache DELETE: ${key}`);\n    return this.cache.delete(key);\n  }\n\n  /**\n   * Clear all cache\n   */\n  clear(): void {\n    console.log('Cache CLEAR: All items');\n    this.cache.clear();\n  }\n\n  /**\n   * Get cache stats\n   */\n  getStats(): { size: number; keys: string[] } {\n    return {\n      size: this.cache.size,\n      keys: Array.from(this.cache.keys())\n    };\n  }\n\n  /**\n   * Clean expired items\n   */\n  cleanup(): void {\n    const now = Date.now();\n    let cleaned = 0;\n\n    for (const [key, item] of this.cache.entries()) {\n      if (now > item.expiresAt) {\n        this.cache.delete(key);\n        cleaned++;\n      }\n    }\n\n    if (cleaned > 0) {\n      console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);\n    }\n  }\n\n  /**\n   * Get or set pattern - fetch data if not cached\n   */\n  async getOrSet<T>(\n    key: string,\n    fetcher: () => Promise<T>,\n    ttl: number = this.defaultTTL\n  ): Promise<T> {\n    // Try to get from cache first\n    const cached = this.get<T>(key);\n    if (cached !== null) {\n      return cached;\n    }\n\n    // Fetch fresh data\n    console.log(`Cache FETCH: ${key}`);\n    const data = await fetcher();\n    \n    // Store in cache\n    this.set(key, data, ttl);\n    \n    return data;\n  }\n\n  /**\n   * Invalidate cache by pattern\n   */\n  invalidatePattern(pattern: string): void {\n    const regex = new RegExp(pattern);\n    let invalidated = 0;\n\n    for (const key of this.cache.keys()) {\n      if (regex.test(key)) {\n        this.cache.delete(key);\n        invalidated++;\n      }\n    }\n\n    if (invalidated > 0) {\n      console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);\n    }\n  }\n}\n\n// Create singleton instance\nexport const cacheService = new CacheService();\n\n// Cache keys constants\nexport const CACHE_KEYS = {\n  LICENSE_TYPES: 'license-types',\n  LICENSE_CATEGORIES: 'license-categories',\n  LICENSE_CATEGORIES_BY_TYPE: (typeId: string) => `license-categories-type-${typeId}`,\n  USER_APPLICATIONS: 'user-applications',\n  APPLICATION: (id: string) => `application-${id}`,\n} as const;\n\n// Cache TTL constants (in milliseconds)\nexport const CACHE_TTL = {\n  SHORT: 2 * 60 * 1000,      // 2 minutes\n  MEDIUM: 5 * 60 * 1000,     // 5 minutes\n  LONG: 15 * 60 * 1000,      // 15 minutes\n  VERY_LONG: 60 * 60 * 1000, // 1 hour\n} as const;\n\n// Auto cleanup every 5 minutes\nsetInterval(() => {\n  cacheService.cleanup();\n}, 5 * 60 * 1000);\n\nexport default cacheService;\n"], "names": [], "mappings": "AAAA,0DAA0D;;;;;;;AAQ1D,MAAM;IACI,QAAQ,IAAI,MAA8B;IAC1C,aAAa,IAAI,KAAK,KAAK;IAEnC;;GAEC,GACD,IAAO,GAAW,EAAE,IAAO,EAAE,MAAc,IAAI,CAAC,UAAU,EAAQ;QAChE,MAAM,MAAM,KAAK,GAAG;QACpB,MAAM,OAAqB;YACzB;YACA,WAAW;YACX,WAAW,MAAM;QACnB;QAEA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK;QACpB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,aAAa,EAAE,IAAI,GAAG,CAAC;IACvD;IAEA;;GAEC,GACD,IAAO,GAAW,EAAY;QAC5B,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QAE5B,IAAI,CAAC,MAAM;YACT,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK;YAChC,OAAO;QACT;QAEA,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,MAAM,KAAK,SAAS,EAAE;YACxB,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,KAAK;YACnC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;YAClB,OAAO;QACT;QAEA,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,OAAO,EAAE,MAAM,KAAK,SAAS,CAAC,GAAG,CAAC;QAChE,OAAO,KAAK,IAAI;IAClB;IAEA;;GAEC,GACD,IAAI,GAAW,EAAW;QACxB,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS;IAC3B;IAEA;;GAEC,GACD,OAAO,GAAW,EAAW;QAC3B,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,KAAK;QAClC,OAAO,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;IAC3B;IAEA;;GAEC,GACD,QAAc;QACZ,QAAQ,GAAG,CAAC;QACZ,IAAI,CAAC,KAAK,CAAC,KAAK;IAClB;IAEA;;GAEC,GACD,WAA6C;QAC3C,OAAO;YACL,MAAM,IAAI,CAAC,KAAK,CAAC,IAAI;YACrB,MAAM,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI;QAClC;IACF;IAEA;;GAEC,GACD,UAAgB;QACd,MAAM,MAAM,KAAK,GAAG;QACpB,IAAI,UAAU;QAEd,KAAK,MAAM,CAAC,KAAK,KAAK,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,GAAI;YAC9C,IAAI,MAAM,KAAK,SAAS,EAAE;gBACxB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,UAAU,GAAG;YACf,QAAQ,GAAG,CAAC,CAAC,uBAAuB,EAAE,QAAQ,cAAc,CAAC;QAC/D;IACF;IAEA;;GAEC,GACD,MAAM,SACJ,GAAW,EACX,OAAyB,EACzB,MAAc,IAAI,CAAC,UAAU,EACjB;QACZ,8BAA8B;QAC9B,MAAM,SAAS,IAAI,CAAC,GAAG,CAAI;QAC3B,IAAI,WAAW,MAAM;YACnB,OAAO;QACT;QAEA,mBAAmB;QACnB,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK;QACjC,MAAM,OAAO,MAAM;QAEnB,iBAAiB;QACjB,IAAI,CAAC,GAAG,CAAC,KAAK,MAAM;QAEpB,OAAO;IACT;IAEA;;GAEC,GACD,kBAAkB,OAAe,EAAQ;QACvC,MAAM,QAAQ,IAAI,OAAO;QACzB,IAAI,cAAc;QAElB,KAAK,MAAM,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,GAAI;YACnC,IAAI,MAAM,IAAI,CAAC,MAAM;gBACnB,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC;gBAClB;YACF;QACF;QAEA,IAAI,cAAc,GAAG;YACnB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,YAAY,yBAAyB,EAAE,SAAS;QAC3F;IACF;AACF;AAGO,MAAM,eAAe,IAAI;AAGzB,MAAM,aAAa;IACxB,eAAe;IACf,oBAAoB;IACpB,4BAA4B,CAAC,SAAmB,CAAC,wBAAwB,EAAE,QAAQ;IACnF,mBAAmB;IACnB,aAAa,CAAC,KAAe,CAAC,YAAY,EAAE,IAAI;AAClD;AAGO,MAAM,YAAY;IACvB,OAAO,IAAI,KAAK;IAChB,QAAQ,IAAI,KAAK;IACjB,MAAM,KAAK,KAAK;IAChB,WAAW,KAAK,KAAK;AACvB;AAEA,+BAA+B;AAC/B,YAAY;IACV,aAAa,OAAO;AACtB,GAAG,IAAI,KAAK;uCAEG", "debugId": null}}, {"offset": {"line": 1008, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/licenseCategoryService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { LicenseType } from './licenseTypeService';\nimport { cacheService, CACHE_KEYS, CACHE_TTL } from './cacheService';\n\n// Utility functions for category codes\nexport const generateCategoryCode = (name: string): string => {\n  return name\n    .toLowerCase()\n    .replace(/[^a-z0-9\\s]/g, '') // Remove special characters\n    .replace(/\\s+/g, '-') // Replace spaces with hyphens\n    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens\n    .substring(0, 50); // Limit length\n};\n\nexport const addCodesToCategories = (categories: LicenseCategory[]): LicenseCategory[] => {\n  return categories.map(category => ({\n    ...category,\n    code: generateCategoryCode(category.name),\n    children: category.children ? addCodesToCategories(category.children) : undefined\n  }));\n};\n\nexport const findCategoryByCode = (categories: LicenseCategory[], code: string): LicenseCategory | null => {\n  for (const category of categories) {\n    if (category.code === code) {\n      return category;\n    }\n    if (category.children) {\n      const found = findCategoryByCode(category.children, code);\n      if (found) return found;\n    }\n  }\n  return null;\n};\n\nexport const findCategoryById = (categories: LicenseCategory[], id: string): LicenseCategory | null => {\n  for (const category of categories) {\n    if (category.license_category_id === id) {\n      return category;\n    }\n    if (category.children) {\n      const found = findCategoryById(category.children, id);\n      if (found) return found;\n    }\n  }\n  return null;\n};\n\n// Types\nexport interface LicenseCategory {\n  license_category_id: string;\n  license_type_id: string;\n  parent_id?: string;\n  name: string;\n  fee: string;\n  description: string;\n  authorizes: string;\n  created_at: string;\n  updated_at: string;\n  created_by?: string;\n  updated_by?: string;\n  license_type?: LicenseType;\n  parent?: LicenseCategory;\n  children?: LicenseCategory[];\n  creator?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  updater?: {\n    user_id: string;\n    first_name: string;\n    last_name: string;\n    email: string;\n  };\n  // Generated code for URL-friendly routing\n  code?: string;\n}\n\nexport interface CreateLicenseCategoryDto {\n  license_type_id: string;\n  parent_id?: string;\n  name: string;\n  fee: string;\n  description: string;\n  authorizes: string;\n}\n\nexport interface UpdateLicenseCategoryDto {\n  license_type_id?: string;\n  parent_id?: string;\n  name?: string;\n  fee?: string;\n  description?: string;\n  authorizes?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  data: T[];\n  meta: {\n    itemsPerPage: number;\n    totalItems?: number;\n    currentPage?: number;\n    totalPages?: number;\n    sortBy: [string, string][];\n    searchBy: string[];\n    search: string;\n    select: string[];\n    filter?: Record<string, string | string[]>;\n  };\n  links: {\n    first?: string;\n    previous?: string;\n    current: string;\n    next?: string;\n    last?: string;\n  };\n}\n\nexport type LicenseCategoriesResponse = PaginatedResponse<LicenseCategory>;\n\nexport interface PaginateQuery {\n  page?: number;\n  limit?: number;\n  sortBy?: string[];\n  searchBy?: string[];\n  search?: string;\n  filter?: Record<string, string | string[]>;\n}\n\nexport const licenseCategoryService = {\n  // Get all license categories with pagination\n  async getLicenseCategories(query: PaginateQuery = {}): Promise<LicenseCategoriesResponse> {\n    const params = new URLSearchParams();\n\n    if (query.page) params.set('page', query.page.toString());\n    if (query.limit) params.set('limit', query.limit.toString());\n    if (query.search) params.set('search', query.search);\n    if (query.sortBy) {\n      query.sortBy.forEach(sort => params.append('sortBy', sort));\n    }\n    if (query.searchBy) {\n      query.searchBy.forEach(search => params.append('searchBy', search));\n    }\n    if (query.filter) {\n      Object.entries(query.filter).forEach(([key, value]) => {\n        if (Array.isArray(value)) {\n          value.forEach(v => params.append(`filter.${key}`, v));\n        } else {\n          params.set(`filter.${key}`, value);\n        }\n      });\n    }\n\n    const response = await apiClient.get(`/license-categories?${params.toString()}`);\n    return response.data;\n  },\n\n  // Get license category by ID\n  async getLicenseCategory(id: string): Promise<LicenseCategory> {\n    const response = await apiClient.get(`/license-categories/${id}`);\n    return response.data;\n  },\n\n  // Get license categories by license type\n  async getLicenseCategoriesByType(licenseTypeId: string): Promise<LicenseCategory[]> {\n    const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\n    return response.data;\n  },\n\n  // Create new license category\n  async createLicenseCategory(licenseCategoryData: CreateLicenseCategoryDto): Promise<LicenseCategory> {\n    const response = await apiClient.post('/license-categories', licenseCategoryData);\n    return response.data;\n  },\n\n  // Update license category\n  async updateLicenseCategory(id: string, licenseCategoryData: UpdateLicenseCategoryDto): Promise<LicenseCategory> {\n    const response = await apiClient.put(`/license-categories/${id}`, licenseCategoryData);\n    return response.data;\n  },\n\n  // Delete license category\n  async deleteLicenseCategory(id: string): Promise<{ message: string }> {\n    const response = await apiClient.delete(`/license-categories/${id}`);\n    return response.data;\n  },\n\n  // Get all license categories (simple list for dropdowns) with caching\n  async getAllLicenseCategories(): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      CACHE_KEYS.LICENSE_CATEGORIES,\n      async () => {\n        console.log('Fetching license categories from API...');\n        // Reduce limit to avoid rate limiting\n        const response = await this.getLicenseCategories({ limit: 100 });\n        return addCodesToCategories(response.data);\n      },\n      CACHE_TTL.LONG // Cache for 15 minutes\n    );\n  },\n\n  // Get hierarchical tree of categories for a license type with caching\n  async getCategoryTree(licenseTypeId: string): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      `category-tree-${licenseTypeId}`,\n      async () => {\n        console.log(`Fetching category tree for license type: ${licenseTypeId}`);\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/tree`);\n        return addCodesToCategories(response.data);\n      },\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\n    );\n  },\n\n  // Get root categories (no parent) for a license type with caching\n  async getRootCategories(licenseTypeId: string): Promise<LicenseCategory[]> {\n    return cacheService.getOrSet(\n      `root-categories-${licenseTypeId}`,\n      async () => {\n        console.log(`Fetching root categories for license type: ${licenseTypeId}`);\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/root`);\n        return response.data;\n      },\n      CACHE_TTL.MEDIUM // Cache for 5 minutes\n    );\n  },\n\n  // Get license categories for parent selection dropdown\n  async getCategoriesForParentSelection(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\n    try {\n      const params = excludeId ? { excludeId } : {};\n      console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);\n\n      // Try the new endpoint first\n      try {\n        const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, { params });\n\n\n        if (response.data && Array.isArray(response.data.data)) {\n          console.log('✅ Valid array response with', response.data.data.length, 'items')\n          return response.data.data;\n        } else {\n          console.warn('⚠️ API returned non-array data:', response.data);\n          return [];\n        }\n      } catch (newEndpointError) {\n        console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);\n\n        // Fallback to existing endpoint\n        const response = await apiClient.get(`/license-categories/by-license-type/${licenseTypeId}`);\n        console.log('🔄 Fallback response:', response.data);\n\n        if (response.data && Array.isArray(response.data)) {\n          // Filter out the excluded category if specified\n          let categories = response.data;\n          if (excludeId) {\n            categories = categories.filter(cat => cat.license_category_id !== excludeId);\n          }\n          console.log('✅ Fallback successful with', categories.length, 'items');\n          return categories;\n        } else {\n          console.warn('⚠️ Fallback also returned non-array data:', response.data);\n          return [];\n        }\n      }\n    } catch (error) {\n\n      return [];\n    }\n  },\n\n  // Get potential parent categories for a license type\n  async getPotentialParents(licenseTypeId: string, excludeId?: string): Promise<LicenseCategory[]> {\n    const params = excludeId ? { excludeId } : {};\n    const response = await apiClient.get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, { params });\n    return response.data;\n  },\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;AAEA;;;AAGO,MAAM,uBAAuB,CAAC;IACnC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,gBAAgB,IAAI,4BAA4B;KACxD,OAAO,CAAC,QAAQ,KAAK,8BAA8B;KACnD,OAAO,CAAC,YAAY,IAAI,kCAAkC;KAC1D,SAAS,CAAC,GAAG,KAAK,eAAe;AACtC;AAEO,MAAM,uBAAuB,CAAC;IACnC,OAAO,WAAW,GAAG,CAAC,CAAA,WAAY,CAAC;YACjC,GAAG,QAAQ;YACX,MAAM,qBAAqB,SAAS,IAAI;YACxC,UAAU,SAAS,QAAQ,GAAG,qBAAqB,SAAS,QAAQ,IAAI;QAC1E,CAAC;AACH;AAEO,MAAM,qBAAqB,CAAC,YAA+B;IAChE,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,IAAI,KAAK,MAAM;YAC1B,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,mBAAmB,SAAS,QAAQ,EAAE;YACpD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAEO,MAAM,mBAAmB,CAAC,YAA+B;IAC9D,KAAK,MAAM,YAAY,WAAY;QACjC,IAAI,SAAS,mBAAmB,KAAK,IAAI;YACvC,OAAO;QACT;QACA,IAAI,SAAS,QAAQ,EAAE;YACrB,MAAM,QAAQ,iBAAiB,SAAS,QAAQ,EAAE;YAClD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAqFO,MAAM,yBAAyB;IACpC,6CAA6C;IAC7C,MAAM,sBAAqB,QAAuB,CAAC,CAAC;QAClD,MAAM,SAAS,IAAI;QAEnB,IAAI,MAAM,IAAI,EAAE,OAAO,GAAG,CAAC,QAAQ,MAAM,IAAI,CAAC,QAAQ;QACtD,IAAI,MAAM,KAAK,EAAE,OAAO,GAAG,CAAC,SAAS,MAAM,KAAK,CAAC,QAAQ;QACzD,IAAI,MAAM,MAAM,EAAE,OAAO,GAAG,CAAC,UAAU,MAAM,MAAM;QACnD,IAAI,MAAM,MAAM,EAAE;YAChB,MAAM,MAAM,CAAC,OAAO,CAAC,CAAA,OAAQ,OAAO,MAAM,CAAC,UAAU;QACvD;QACA,IAAI,MAAM,QAAQ,EAAE;YAClB,MAAM,QAAQ,CAAC,OAAO,CAAC,CAAA,SAAU,OAAO,MAAM,CAAC,YAAY;QAC7D;QACA,IAAI,MAAM,MAAM,EAAE;YAChB,OAAO,OAAO,CAAC,MAAM,MAAM,EAAE,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;gBAChD,IAAI,MAAM,OAAO,CAAC,QAAQ;oBACxB,MAAM,OAAO,CAAC,CAAA,IAAK,OAAO,MAAM,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBACpD,OAAO;oBACL,OAAO,GAAG,CAAC,CAAC,OAAO,EAAE,KAAK,EAAE;gBAC9B;YACF;QACF;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,OAAO,QAAQ,IAAI;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,oBAAmB,EAAU;QACjC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI;QAChE,OAAO,SAAS,IAAI;IACtB;IAEA,yCAAyC;IACzC,MAAM,4BAA2B,aAAqB;QACpD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;QAC3F,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,MAAM,uBAAsB,mBAA6C;QACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU,EAAE,mBAA6C;QACnF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QAClE,OAAO,SAAS,IAAI;IACtB;IAEA,0BAA0B;IAC1B,MAAM,uBAAsB,EAAU;QACpC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;QACnE,OAAO,SAAS,IAAI;IACtB;IAEA,sEAAsE;IACtE,MAAM;QACJ,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,+HAAA,CAAA,aAAU,CAAC,kBAAkB,EAC7B;YACE,QAAQ,GAAG,CAAC;YACZ,sCAAsC;YACtC,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;gBAAE,OAAO;YAAI;YAC9D,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,+HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,uBAAuB;;IAE1C;IAEA,sEAAsE;IACtE,MAAM,iBAAgB,aAAqB;QACzC,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,cAAc,EAAE,eAAe,EAChC;YACE,QAAQ,GAAG,CAAC,CAAC,yCAAyC,EAAE,eAAe;YACvE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,qBAAqB,SAAS,IAAI;QAC3C,GACA,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,kEAAkE;IAClE,MAAM,mBAAkB,aAAqB;QAC3C,OAAO,+HAAA,CAAA,eAAY,CAAC,QAAQ,CAC1B,CAAC,gBAAgB,EAAE,eAAe,EAClC;YACE,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,eAAe;YACzE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,KAAK,CAAC;YAC7F,OAAO,SAAS,IAAI;QACtB,GACA,+HAAA,CAAA,YAAS,CAAC,MAAM,CAAC,sBAAsB;;IAE3C;IAEA,uDAAuD;IACvD,MAAM,iCAAgC,aAAqB,EAAE,SAAkB;QAC7E,IAAI;YACF,MAAM,SAAS,YAAY;gBAAE;YAAU,IAAI,CAAC;YAC5C,QAAQ,GAAG,CAAC,mDAAmD,eAAe,gBAAgB;YAE9F,6BAA6B;YAC7B,IAAI;gBACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,qBAAqB,CAAC,EAAE;oBAAE;gBAAO;gBAGxH,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,GAAG;oBACtD,QAAQ,GAAG,CAAC,+BAA+B,SAAS,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;oBACtE,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAAO;oBACL,QAAQ,IAAI,CAAC,mCAAmC,SAAS,IAAI;oBAC7D,OAAO,EAAE;gBACX;YACF,EAAE,OAAO,kBAAkB;gBACzB,QAAQ,IAAI,CAAC,4CAA4C;gBAEzD,gCAAgC;gBAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,oCAAoC,EAAE,eAAe;gBAC3F,QAAQ,GAAG,CAAC,yBAAyB,SAAS,IAAI;gBAElD,IAAI,SAAS,IAAI,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;oBACjD,gDAAgD;oBAChD,IAAI,aAAa,SAAS,IAAI;oBAC9B,IAAI,WAAW;wBACb,aAAa,WAAW,MAAM,CAAC,CAAA,MAAO,IAAI,mBAAmB,KAAK;oBACpE;oBACA,QAAQ,GAAG,CAAC,8BAA8B,WAAW,MAAM,EAAE;oBAC7D,OAAO;gBACT,OAAO;oBACL,QAAQ,IAAI,CAAC,6CAA6C,SAAS,IAAI;oBACvE,OAAO,EAAE;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YAEd,OAAO,EAAE;QACX;IACF;IAEA,qDAAqD;IACrD,MAAM,qBAAoB,aAAqB,EAAE,SAAkB;QACjE,MAAM,SAAS,YAAY;YAAE;QAAU,IAAI,CAAC;QAC5C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,iCAAiC,EAAE,cAAc,kBAAkB,CAAC,EAAE;YAAE;QAAO;QACrH,OAAO,SAAS,IAAI;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1194, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationService.ts"], "sourcesContent": ["import { processApiResponse } from '@/lib/authUtils';\r\nimport { apiClient } from '../lib/apiClient';\r\nimport { Application, ApplicationStatus, ApplicationFilters, PaginatedResponse } from '../types/license';\r\n\r\nexport const applicationService = {\r\n  // Get all applications with pagination and filters\r\n  async getApplications(params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    sortBy?: string;\r\n    sortOrder?: 'ASC' | 'DESC';\r\n    filters?: ApplicationFilters;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.sortBy) queryParams.append('sortBy', params.sortBy);\r\n    if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);\r\n    \r\n    // Add filters\r\n    if (params?.filters?.licenseTypeId) {\r\n      queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);\r\n    }\r\n    if (params?.filters?.licenseCategoryId) {\r\n      queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);\r\n    }\r\n    if (params?.filters?.status) {\r\n      queryParams.append('filter.status', params.filters.status);\r\n    }\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by license type (through license category)\r\n  async getApplicationsByLicenseType(licenseTypeId: string, params?: {\r\n    page?: number;\r\n    limit?: number;\r\n    search?: string;\r\n    status?: ApplicationStatus;\r\n  }): Promise<PaginatedResponse<Application>> {\r\n    const queryParams = new URLSearchParams();\r\n    \r\n    if (params?.page) queryParams.append('page', params.page.toString());\r\n    if (params?.limit) queryParams.append('limit', params.limit.toString());\r\n    if (params?.search) queryParams.append('search', params.search);\r\n    if (params?.status) queryParams.append('filter.status', params.status);\r\n    \r\n    // Filter by license type through license category\r\n    queryParams.append('filter.license_category.license_type_id', licenseTypeId);\r\n\r\n    const response = await apiClient.get(`/applications?${queryParams.toString()}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get single application by ID\r\n  async getApplication(id: string): Promise<Application> {\r\n    const response = await apiClient.get(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by applicant\r\n  async getApplicationsByApplicant(applicantId: string): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-applicant/${applicantId}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Get applications by status\r\n  async getApplicationsByStatus(status: ApplicationStatus): Promise<Application[]> {\r\n    const response = await apiClient.get(`/applications/by-status/${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application status\r\n  async updateApplicationStatus(id: string, status: ApplicationStatus): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}/status?status=${status}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Update application progress\r\n  async updateApplicationProgress(id: string, currentStep: number, progressPercentage: number): Promise<Application> {\r\n    const response = await apiClient.put(\r\n      `/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`\r\n    );\r\n    return response.data;\r\n  },\r\n\r\n  // Get application statistics\r\n  async getApplicationStats(): Promise<Record<string, number>> {\r\n    const response = await apiClient.get('/applications/stats');\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application\r\n  async createApplication(data: {\r\n    application_number: string;\r\n    applicant_id: string;\r\n    license_category_id: string;\r\n    status?: ApplicationStatus;\r\n    current_step?: number;\r\n    progress_percentage?: number;\r\n    submitted_at?: Date;\r\n  }): Promise<Application> {\r\n    console.log('ApplicationService.createApplication called with:', data);\r\n    try {\r\n      const response = await apiClient.post('/applications', data);\r\n      return processApiResponse(response);\r\n    } catch (error) {\r\n      console.error('ApplicationService.createApplication error:', error);\r\n      console.error('Error response:', (error as any)?.response?.data);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Update application\r\n  async updateApplication(id: string, data: Partial<Application>): Promise<Application> {\r\n    const response = await apiClient.put(`/applications/${id}`, data);\r\n    return response.data;\r\n  },\r\n\r\n  // Delete application\r\n  async deleteApplication(id: string): Promise<{ message: string }> {\r\n    const response = await apiClient.delete(`/applications/${id}`);\r\n    return response.data;\r\n  },\r\n\r\n  // Create new application with applicant data\r\n  async createApplicationWithApplicant(data: {\r\n    license_type_id: string;\r\n    license_category_id: string;\r\n    applicant_data: Record<string, any>;\r\n    user_id: string;\r\n  }): Promise<Application> {\r\n    try {\r\n      console.log('Creating application with data:', data);\r\n\r\n      // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)\r\n      const now = new Date();\r\n      const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');\r\n      const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');\r\n      const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();\r\n      const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;\r\n\r\n      // Validate user_id is a proper UUID\r\n      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\r\n      if (!uuidRegex.test(data.user_id)) {\r\n        throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);\r\n      }\r\n\r\n      console.log('Creating application with:', {\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id,\r\n        license_category_id: data.license_category_id,\r\n        status: 'draft'\r\n      });\r\n\r\n      // Create application using user_id as applicant_id\r\n      // In most systems, the authenticated user is the applicant\r\n      const application = await this.createApplication({\r\n        application_number: applicationNumber,\r\n        applicant_id: data.user_id, // Use user_id as applicant_id\r\n        license_category_id: data.license_category_id,\r\n        current_step: 1,\r\n        progress_percentage: 14 // ~1/7 steps completed\r\n      });\r\n\r\n      console.log('Application created:', application);\r\n\r\n      // Save applicant form data separately if needed\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        await applicationFormDataService.saveFormSection(\r\n          application.application_id,\r\n          'applicantInfo',\r\n          data.applicant_data\r\n        );\r\n        console.log('Applicant form data saved');\r\n      } catch (formDataError) {\r\n        console.warn('Could not save form data separately, continuing...', formDataError);\r\n        // Continue even if form data saving fails\r\n      }\r\n\r\n      console.log('Application created successfully');\r\n      return application;\r\n    } catch (error) {\r\n      console.error('Error creating application with applicant:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application section data\r\n  async saveApplicationSection(\r\n    applicationId: string,\r\n    sectionName: string,\r\n    sectionData: Record<string, any>\r\n  ): Promise<void> {\r\n    try {\r\n      console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);\r\n\r\n      // Try to save using form data service, but continue if it fails\r\n      let completedSections = 1; // At least one section is being saved\r\n\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n\r\n        // Save section data using form data service\r\n        await applicationFormDataService.saveOrUpdateFormSection(\r\n          applicationId,\r\n          sectionName,\r\n          sectionData\r\n        );\r\n\r\n        // Get all form data to calculate progress\r\n        const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n        completedSections = Object.keys(allFormData).length;\r\n\r\n        console.log(`Form data saved using form data service`);\r\n      } catch (formDataError) {\r\n        console.warn('Form data service not available, using basic progress tracking:', formDataError);\r\n\r\n        // Fallback: estimate progress based on section name\r\n        const sectionOrder = ['applicantInfo', 'companyProfile', 'businessInfo', 'serviceScope', 'businessPlan', 'legalHistory', 'reviewSubmit'];\r\n        const sectionIndex = sectionOrder.indexOf(sectionName);\r\n        completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;\r\n      }\r\n\r\n      // Calculate progress based on completed sections\r\n      const totalSections = 7; // Total number of form sections\r\n      const progressPercentage = Math.round((completedSections / totalSections) * 100);\r\n\r\n      // Update the application progress\r\n      await this.updateApplication(applicationId, {\r\n        progress_percentage: progressPercentage,\r\n        current_step: completedSections\r\n      });\r\n\r\n      console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);\r\n    } catch (error) {\r\n      console.error(`Error saving section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get application section data\r\n  async getApplicationSection(applicationId: string, sectionName: string): Promise<any> {\r\n    try {\r\n      const response = await apiClient.get(`/applications/${applicationId}/sections/${sectionName}`);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error(`Error fetching section ${sectionName}:`, error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Submit application for review\r\n  async submitApplication(applicationId: string): Promise<Application> {\r\n    try {\r\n      console.log('Submitting application:', applicationId);\r\n\r\n      // Update application status to submitted and set submission date\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        status: 'submitted',\r\n        submitted_at: new Date().toISOString(),\r\n        progress_percentage: 100,\r\n        current_step: 7\r\n      });\r\n\r\n      console.log('Application submitted successfully:', response.data);\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error submitting application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Get user's applications\r\n  async getUserApplications(): Promise<Application[]> {\r\n    try {\r\n      console.log('Fetching user applications...');\r\n\r\n      // Get all applications - the backend should filter by authenticated user\r\n      const response = await apiClient.get('/applications');\r\n\r\n      console.log('Applications API response:', response.data);\r\n\r\n      // Handle different response structures\r\n      let applications = [];\r\n      if (response.data?.data) {\r\n        applications = Array.isArray(response.data.data) ? response.data.data : [];\r\n      } else if (Array.isArray(response.data)) {\r\n        applications = response.data;\r\n      } else if (response.data) {\r\n        // Single application or other structure\r\n        applications = [response.data];\r\n      }\r\n\r\n      console.log('Processed applications:', applications);\r\n      return applications;\r\n    } catch (error) {\r\n      console.error('Error fetching user applications:', error);\r\n      console.error('Error details:', {\r\n        message: (error as any)?.message,\r\n        response: (error as any)?.response?.data,\r\n        status: (error as any)?.response?.status\r\n      });\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Save application as draft\r\n  async saveAsDraft(applicationId: string, formData: Record<string, any>): Promise<Application> {\r\n    try {\r\n      const response = await apiClient.put(`/applications/${applicationId}`, {\r\n        form_data: formData,\r\n        status: 'draft'\r\n      });\r\n      return response.data;\r\n    } catch (error) {\r\n      console.error('Error saving application as draft:', error);\r\n      throw error;\r\n    }\r\n  },\r\n\r\n  // Validate application before submission\r\n  async validateApplication(applicationId: string): Promise<{ isValid: boolean; errors: any[] }> {\r\n    try {\r\n      // Get form data from the form data service\r\n      let formData: Record<string, any> = {};\r\n      try {\r\n        const { applicationFormDataService } = await import('./applicationFormDataService');\r\n        formData = await applicationFormDataService.getApplicationFormData(applicationId);\r\n      } catch (formDataError) {\r\n        console.warn('Could not retrieve form data for validation:', formDataError);\r\n        // Continue with empty form data\r\n      }\r\n\r\n      const errors: any[] = [];\r\n      const requiredSections = ['applicantInfo', 'companyProfile', 'businessInfo', 'legalHistory'];\r\n\r\n      // Check if all required sections are completed\r\n      for (const section of requiredSections) {\r\n        if (!formData[section] || Object.keys(formData[section]).length === 0) {\r\n          errors.push(`${section} section is incomplete`);\r\n        }\r\n      }\r\n\r\n      return {\r\n        isValid: errors.length === 0,\r\n        errors\r\n      };\r\n    } catch (error) {\r\n      console.error('Error validating application:', error);\r\n      throw error;\r\n    }\r\n  },\r\n};\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAGO,MAAM,qBAAqB;IAChC,mDAAmD;IACnD,MAAM,iBAAgB,MAOrB;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,WAAW,YAAY,MAAM,CAAC,aAAa,OAAO,SAAS;QAEvE,cAAc;QACd,IAAI,QAAQ,SAAS,eAAe;YAClC,YAAY,MAAM,CAAC,2CAA2C,OAAO,OAAO,CAAC,aAAa;QAC5F;QACA,IAAI,QAAQ,SAAS,mBAAmB;YACtC,YAAY,MAAM,CAAC,8BAA8B,OAAO,OAAO,CAAC,iBAAiB;QACnF;QACA,IAAI,QAAQ,SAAS,QAAQ;YAC3B,YAAY,MAAM,CAAC,iBAAiB,OAAO,OAAO,CAAC,MAAM;QAC3D;QAEA,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,8DAA8D;IAC9D,MAAM,8BAA6B,aAAqB,EAAE,MAKzD;QACC,MAAM,cAAc,IAAI;QAExB,IAAI,QAAQ,MAAM,YAAY,MAAM,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ;QACjE,IAAI,QAAQ,OAAO,YAAY,MAAM,CAAC,SAAS,OAAO,KAAK,CAAC,QAAQ;QACpE,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,UAAU,OAAO,MAAM;QAC9D,IAAI,QAAQ,QAAQ,YAAY,MAAM,CAAC,iBAAiB,OAAO,MAAM;QAErE,kDAAkD;QAClD,YAAY,MAAM,CAAC,2CAA2C;QAE9D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,YAAY,QAAQ,IAAI;QAC9E,OAAO,SAAS,IAAI;IACtB;IAEA,+BAA+B;IAC/B,MAAM,gBAAe,EAAU;QAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,gCAAgC;IAChC,MAAM,4BAA2B,WAAmB;QAClD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,2BAA2B,EAAE,aAAa;QAChF,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM,yBAAwB,MAAyB;QACrD,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,wBAAwB,EAAE,QAAQ;QACxE,OAAO,SAAS,IAAI;IACtB;IAEA,4BAA4B;IAC5B,MAAM,yBAAwB,EAAU,EAAE,MAAyB;QACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,eAAe,EAAE,QAAQ;QAClF,OAAO,SAAS,IAAI;IACtB;IAEA,8BAA8B;IAC9B,MAAM,2BAA0B,EAAU,EAAE,WAAmB,EAAE,kBAA0B;QACzF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAClC,CAAC,cAAc,EAAE,GAAG,sBAAsB,EAAE,YAAY,oBAAoB,EAAE,oBAAoB;QAEpG,OAAO,SAAS,IAAI;IACtB;IAEA,6BAA6B;IAC7B,MAAM;QACJ,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;QACrC,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,mBAAkB,IAQvB;QACC,QAAQ,GAAG,CAAC,qDAAqD;QACjE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,iBAAiB;YACvD,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;YAC7D,QAAQ,KAAK,CAAC,mBAAoB,OAAe,UAAU;YAC3D,MAAM;QACR;IACF;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU,EAAE,IAA0B;QAC5D,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,IAAI,EAAE;QAC5D,OAAO,SAAS,IAAI;IACtB;IAEA,qBAAqB;IACrB,MAAM,mBAAkB,EAAU;QAChC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,cAAc,EAAE,IAAI;QAC7D,OAAO,SAAS,IAAI;IACtB;IAEA,6CAA6C;IAC7C,MAAM,gCAA+B,IAKpC;QACC,IAAI;YACF,QAAQ,GAAG,CAAC,mCAAmC;YAE/C,uEAAuE;YACvE,MAAM,MAAM,IAAI;YAChB,MAAM,UAAU,IAAI,WAAW,GAAG,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,MAAM;YAC7D,MAAM,UAAU,IAAI,YAAY,GAAG,KAAK,CAAC,GAAG,GAAG,OAAO,CAAC,MAAM;YAC7D,MAAM,YAAY,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,GAAG,WAAW;YACrE,MAAM,oBAAoB,CAAC,IAAI,EAAE,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,WAAW;YAElE,oCAAoC;YACpC,MAAM,YAAY;YAClB,IAAI,CAAC,UAAU,IAAI,CAAC,KAAK,OAAO,GAAG;gBACjC,MAAM,IAAI,MAAM,CAAC,wBAAwB,EAAE,KAAK,OAAO,CAAC,uBAAuB,CAAC;YAClF;YAEA,QAAQ,GAAG,CAAC,8BAA8B;gBACxC,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,QAAQ;YACV;YAEA,mDAAmD;YACnD,2DAA2D;YAC3D,MAAM,cAAc,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBAC/C,oBAAoB;gBACpB,cAAc,KAAK,OAAO;gBAC1B,qBAAqB,KAAK,mBAAmB;gBAC7C,cAAc;gBACd,qBAAqB,GAAG,uBAAuB;YACjD;YAEA,QAAQ,GAAG,CAAC,wBAAwB;YAEpC,gDAAgD;YAChD,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,MAAM,2BAA2B,eAAe,CAC9C,YAAY,cAAc,EAC1B,iBACA,KAAK,cAAc;gBAErB,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,sDAAsD;YACnE,0CAA0C;YAC5C;YAEA,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8CAA8C;YAC5D,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,wBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAE/E,gEAAgE;YAChE,IAAI,oBAAoB,GAAG,sCAAsC;YAEjE,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBAEvC,4CAA4C;gBAC5C,MAAM,2BAA2B,uBAAuB,CACtD,eACA,aACA;gBAGF,0CAA0C;gBAC1C,MAAM,cAAc,MAAM,2BAA2B,sBAAsB,CAAC;gBAC5E,oBAAoB,OAAO,IAAI,CAAC,aAAa,MAAM;gBAEnD,QAAQ,GAAG,CAAC,CAAC,uCAAuC,CAAC;YACvD,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,mEAAmE;gBAEhF,oDAAoD;gBACpD,MAAM,eAAe;oBAAC;oBAAiB;oBAAkB;oBAAgB;oBAAgB;oBAAgB;oBAAgB;iBAAe;gBACxI,MAAM,eAAe,aAAa,OAAO,CAAC;gBAC1C,oBAAoB,gBAAgB,IAAI,eAAe,IAAI;YAC7D;YAEA,iDAAiD;YACjD,MAAM,gBAAgB,GAAG,gCAAgC;YACzD,MAAM,qBAAqB,KAAK,KAAK,CAAC,AAAC,oBAAoB,gBAAiB;YAE5E,kCAAkC;YAClC,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe;gBAC1C,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,CAAC,QAAQ,EAAE,YAAY,yBAAyB,EAAE,mBAAmB,UAAU,CAAC;QAC9F,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,qBAAqB,EAAE,YAAY,CAAC,CAAC,EAAE;YACtD,MAAM;QACR;IACF;IAEA,+BAA+B;IAC/B,MAAM,uBAAsB,aAAqB,EAAE,WAAmB;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,cAAc,UAAU,EAAE,aAAa;YAC7F,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,uBAAuB,EAAE,YAAY,CAAC,CAAC,EAAE;YACxD,MAAM;QACR;IACF;IAEA,gCAAgC;IAChC,MAAM,mBAAkB,aAAqB;QAC3C,IAAI;YACF,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,iEAAiE;YACjE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,QAAQ;gBACR,cAAc,IAAI,OAAO,WAAW;gBACpC,qBAAqB;gBACrB,cAAc;YAChB;YAEA,QAAQ,GAAG,CAAC,uCAAuC,SAAS,IAAI;YAChE,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;IAEA,0BAA0B;IAC1B,MAAM;QACJ,IAAI;YACF,QAAQ,GAAG,CAAC;YAEZ,yEAAyE;YACzE,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YAErC,QAAQ,GAAG,CAAC,8BAA8B,SAAS,IAAI;YAEvD,uCAAuC;YACvC,IAAI,eAAe,EAAE;YACrB,IAAI,SAAS,IAAI,EAAE,MAAM;gBACvB,eAAe,MAAM,OAAO,CAAC,SAAS,IAAI,CAAC,IAAI,IAAI,SAAS,IAAI,CAAC,IAAI,GAAG,EAAE;YAC5E,OAAO,IAAI,MAAM,OAAO,CAAC,SAAS,IAAI,GAAG;gBACvC,eAAe,SAAS,IAAI;YAC9B,OAAO,IAAI,SAAS,IAAI,EAAE;gBACxB,wCAAwC;gBACxC,eAAe;oBAAC,SAAS,IAAI;iBAAC;YAChC;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YACvC,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAU,OAAe;gBACzB,UAAW,OAAe,UAAU;gBACpC,QAAS,OAAe,UAAU;YACpC;YACA,MAAM;QACR;IACF;IAEA,4BAA4B;IAC5B,MAAM,aAAY,aAAqB,EAAE,QAA6B;QACpE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,cAAc,EAAE,eAAe,EAAE;gBACrE,WAAW;gBACX,QAAQ;YACV;YACA,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sCAAsC;YACpD,MAAM;QACR;IACF;IAEA,yCAAyC;IACzC,MAAM,qBAAoB,aAAqB;QAC7C,IAAI;YACF,2CAA2C;YAC3C,IAAI,WAAgC,CAAC;YACrC,IAAI;gBACF,MAAM,EAAE,0BAA0B,EAAE,GAAG;gBACvC,WAAW,MAAM,2BAA2B,sBAAsB,CAAC;YACrE,EAAE,OAAO,eAAe;gBACtB,QAAQ,IAAI,CAAC,gDAAgD;YAC7D,gCAAgC;YAClC;YAEA,MAAM,SAAgB,EAAE;YACxB,MAAM,mBAAmB;gBAAC;gBAAiB;gBAAkB;gBAAgB;aAAe;YAE5F,+CAA+C;YAC/C,KAAK,MAAM,WAAW,iBAAkB;gBACtC,IAAI,CAAC,QAAQ,CAAC,QAAQ,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAAM,KAAK,GAAG;oBACrE,OAAO,IAAI,CAAC,GAAG,QAAQ,sBAAsB,CAAC;gBAChD;YACF;YAEA,OAAO;gBACL,SAAS,OAAO,MAAM,KAAK;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/TextInput.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface TextInputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  className?: string;\n  containerClassName?: string;\n}\n\nconst TextInput = forwardRef<HTMLInputElement, TextInputProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  className = '',\n  containerClassName = '',\n  id,\n  ...props\n}, ref) => {\n  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseInputClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n  `;\n  \n  const inputClasses = error\n    ? `${baseInputClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseInputClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={inputId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <input\n        ref={ref}\n        id={inputId}\n        className={`${inputClasses} ${className}`}\n        {...props}\n      />\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nTextInput.displayName = 'TextInput';\n\nexport default TextInput;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAaA,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAoC,CAAC,EAC9D,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,EAAE,EACF,GAAG,OACJ,EAAE;IACD,MAAM,UAAU,MAAM,CAAC,MAAM,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAExE,MAAM,mBAAmB,CAAC;;;;;;EAM1B,CAAC;IAED,MAAM,eAAe,QACjB,GAAG,iBAAiB,2EAA2E,CAAC,GAChG,GAAG,iBAAiB,0GAA0G,CAAC;IAEnI,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBACC,KAAK;gBACL,IAAI;gBACJ,WAAW,GAAG,aAAa,CAAC,EAAE,WAAW;gBACxC,GAAG,KAAK;;;;;;YAGV,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,UAAU,WAAW,GAAG;uCAET", "debugId": null}}, {"offset": {"line": 1580, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/common/Select.tsx"], "sourcesContent": ["'use client';\n\nimport React, { forwardRef } from 'react';\n\ninterface SelectOption {\n  value: string;\n  label: string;\n  disabled?: boolean;\n}\n\ninterface SelectProps extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, 'onChange'> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  required?: boolean;\n  options: SelectOption[];\n  placeholder?: string;\n  className?: string;\n  containerClassName?: string;\n  onChange?: (value: string) => void;\n}\n\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(({\n  label,\n  error,\n  helperText,\n  required = false,\n  options = [],\n  placeholder = 'Select an option...',\n  className = '',\n  containerClassName = '',\n  onChange,\n  id,\n  value,\n  ...props\n}, ref) => {\n  const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;\n  \n  const baseSelectClasses = `\n    w-full px-3 py-2 border rounded-md shadow-sm \n    focus:outline-none focus:ring-2 focus:ring-offset-2 \n    disabled:opacity-50 disabled:cursor-not-allowed\n    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600\n    transition-colors duration-200\n    appearance-none bg-white\n    bg-no-repeat bg-right bg-[length:16px_16px]\n    pr-10\n  `;\n  \n  const selectClasses = error\n    ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600`\n    : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;\n\n  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {\n    if (onChange) {\n      onChange(e.target.value);\n    }\n  };\n\n  return (\n    <div className={`space-y-1 ${containerClassName}`}>\n      {label && (\n        <label \n          htmlFor={selectId}\n          className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\"\n        >\n          {label}\n          {required && <span className=\"text-red-500 ml-1\">*</span>}\n        </label>\n      )}\n      \n      <div className=\"relative\">\n        <select\n          ref={ref}\n          id={selectId}\n          value={value || ''}\n          onChange={handleChange}\n          className={`${selectClasses} ${className}`}\n          {...props}\n        >\n          {placeholder && (\n            <option value=\"\" disabled>\n              {placeholder}\n            </option>\n          )}\n          \n          {options.map((option) => (\n            <option \n              key={option.value} \n              value={option.value}\n              disabled={option.disabled}\n            >\n              {option.label}\n            </option>\n          ))}\n        </select>\n        \n        {/* Custom dropdown arrow */}\n        <div className=\"absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none\">\n          <i className=\"ri-arrow-down-s-line text-gray-400 dark:text-gray-500\"></i>\n        </div>\n      </div>\n      \n      {error && (\n        <p className=\"text-sm text-red-600 dark:text-red-400 flex items-center\">\n          <i className=\"ri-error-warning-line mr-1\"></i>\n          {error}\n        </p>\n      )}\n      \n      {helperText && !error && (\n        <p className=\"text-sm text-gray-500 dark:text-gray-400\">\n          {helperText}\n        </p>\n      )}\n    </div>\n  );\n});\n\nSelect.displayName = 'Select';\n\nexport default Select;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAsBA,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAkC,CAAC,EACzD,KAAK,EACL,KAAK,EACL,UAAU,EACV,WAAW,KAAK,EAChB,UAAU,EAAE,EACZ,cAAc,qBAAqB,EACnC,YAAY,EAAE,EACd,qBAAqB,EAAE,EACvB,QAAQ,EACR,EAAE,EACF,KAAK,EACL,GAAG,OACJ,EAAE;IACD,MAAM,WAAW,MAAM,CAAC,OAAO,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;IAE1E,MAAM,oBAAoB,CAAC;;;;;;;;;EAS3B,CAAC;IAED,MAAM,gBAAgB,QAClB,GAAG,kBAAkB,2EAA2E,CAAC,GACjG,GAAG,kBAAkB,0GAA0G,CAAC;IAEpI,MAAM,eAAe,CAAC;QACpB,IAAI,UAAU;YACZ,SAAS,EAAE,MAAM,CAAC,KAAK;QACzB;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,oBAAoB;;YAC9C,uBACC,8OAAC;gBACC,SAAS;gBACT,WAAU;;oBAET;oBACA,0BAAY,8OAAC;wBAAK,WAAU;kCAAoB;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,KAAK;wBACL,IAAI;wBACJ,OAAO,SAAS;wBAChB,UAAU;wBACV,WAAW,GAAG,cAAc,CAAC,EAAE,WAAW;wBACzC,GAAG,KAAK;;4BAER,6BACC,8OAAC;gCAAO,OAAM;gCAAG,QAAQ;0CACtB;;;;;;4BAIJ,QAAQ,GAAG,CAAC,CAAC,uBACZ,8OAAC;oCAEC,OAAO,OAAO,KAAK;oCACnB,UAAU,OAAO,QAAQ;8CAExB,OAAO,KAAK;mCAJR,OAAO,KAAK;;;;;;;;;;;kCAUvB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;;;;;;;;;;;;;;;;YAIhB,uBACC,8OAAC;gBAAE,WAAU;;kCACX,8OAAC;wBAAE,WAAU;;;;;;oBACZ;;;;;;;YAIJ,cAAc,CAAC,uBACd,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX;AAEA,OAAO,WAAW,GAAG;uCAEN", "debugId": null}}, {"offset": {"line": 1723, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/utils/formValidation.ts"], "sourcesContent": ["// Form validation utilities\r\n\r\nexport interface ValidationRule {\r\n  required?: boolean;\r\n  minLength?: number;\r\n  maxLength?: number;\r\n  pattern?: RegExp;\r\n  custom?: (value: any) => string | null;\r\n}\r\n\r\nexport interface ValidationErrors {\r\n  [key: string]: string;\r\n}\r\n\r\nexport const validateField = (value: any, rules: ValidationRule): string | null => {\r\n  // Required validation\r\n  if (rules.required && (!value || (typeof value === 'string' && value.trim() === ''))) {\r\n    return 'This field is required';\r\n  }\r\n\r\n  // Skip other validations if field is empty and not required\r\n  if (!value || (typeof value === 'string' && value.trim() === '')) {\r\n    return null;\r\n  }\r\n\r\n  // String validations\r\n  if (typeof value === 'string') {\r\n    // Min length validation\r\n    if (rules.minLength && value.length < rules.minLength) {\r\n      return `Must be at least ${rules.minLength} characters`;\r\n    }\r\n\r\n    // Max length validation\r\n    if (rules.maxLength && value.length > rules.maxLength) {\r\n      return `Must be no more than ${rules.maxLength} characters`;\r\n    }\r\n\r\n    // Pattern validation\r\n    if (rules.pattern && !rules.pattern.test(value)) {\r\n      return 'Invalid format';\r\n    }\r\n  }\r\n\r\n  // Custom validation\r\n  if (rules.custom) {\r\n    return rules.custom(value);\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\nexport const validateForm = (data: any, rules: { [key: string]: ValidationRule }): ValidationErrors => {\r\n  const errors: ValidationErrors = {};\r\n\r\n  Object.keys(rules).forEach(field => {\r\n    const error = validateField(data[field], rules[field]);\r\n    if (error) {\r\n      errors[field] = error;\r\n    }\r\n  });\r\n\r\n  return errors;\r\n};\r\n\r\n// Common validation patterns\r\nexport const patterns = {\r\n  email: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\r\n  phone: /^(\\+265|0)[0-9]{8,9}$/,\r\n  url: /^https?:\\/\\/.+/,\r\n  alphanumeric: /^[a-zA-Z0-9]+$/,\r\n  alphabetic: /^[a-zA-Z\\s]+$/,\r\n  numeric: /^[0-9]+$/,\r\n  percentage: /^(100|[1-9]?[0-9])$/\r\n};\r\n\r\n// Common validation rules\r\nexport const commonRules = {\r\n  required: { required: true },\r\n  email: { \r\n    required: true, \r\n    pattern: patterns.email,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.email.test(value)) {\r\n        return 'Please enter a valid email address';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  phone: {\r\n    required: true,\r\n    pattern: patterns.phone,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.phone.test(value)) {\r\n        return 'Please enter a valid Malawi phone number (e.g., +265123456789 or 0123456789)';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  businessRegistration: {\r\n    required: true,\r\n    minLength: 5,\r\n    custom: (value: string) => {\r\n      if (value && value.length < 5) {\r\n        return 'Business registration number must be at least 5 characters';\r\n      }\r\n      return null;\r\n    }\r\n  },\r\n  percentage: {\r\n    pattern: patterns.percentage,\r\n    custom: (value: string) => {\r\n      if (value && !patterns.percentage.test(value)) {\r\n        return 'Please enter a valid percentage (0-100)';\r\n      }\r\n      return null;\r\n    }\r\n  }\r\n};\r\n\r\n// Utility function to check if form has errors\r\nexport const hasErrors = (errors: ValidationErrors): boolean => {\r\n  return Object.keys(errors).length > 0;\r\n};\r\n\r\n// Utility function to get first error message\r\nexport const getFirstError = (errors: ValidationErrors): string | null => {\r\n  const firstKey = Object.keys(errors)[0];\r\n  return firstKey ? errors[firstKey] : null;\r\n};\r\n\r\n// Utility function to validate array fields\r\nexport const validateArrayField = (\r\n  array: any[], \r\n  rules: { [key: string]: ValidationRule },\r\n  minItems?: number,\r\n  maxItems?: number\r\n): { [key: string]: ValidationErrors } => {\r\n  const arrayErrors: { [key: string]: ValidationErrors } = {};\r\n\r\n  // Check array length\r\n  if (minItems && array.length < minItems) {\r\n    arrayErrors._array = { length: `Must have at least ${minItems} items` };\r\n  }\r\n  if (maxItems && array.length > maxItems) {\r\n    arrayErrors._array = { length: `Must have no more than ${maxItems} items` };\r\n  }\r\n\r\n  // Validate each item in array\r\n  array.forEach((item, index) => {\r\n    const itemErrors = validateForm(item, rules);\r\n    if (hasErrors(itemErrors)) {\r\n      arrayErrors[index] = itemErrors;\r\n    }\r\n  });\r\n\r\n  return arrayErrors;\r\n};\r\n\r\n// File validation\r\nexport const validateFile = (\r\n  file: File | null, \r\n  required: boolean = false,\r\n  maxSize: number = 10, // MB\r\n  allowedTypes: string[] = ['.pdf']\r\n): string | null => {\r\n  if (required && !file) {\r\n    return 'File is required';\r\n  }\r\n\r\n  if (!file) {\r\n    return null;\r\n  }\r\n\r\n  // Check file size\r\n  if (file.size > maxSize * 1024 * 1024) {\r\n    return `File size must be less than ${maxSize}MB`;\r\n  }\r\n\r\n  // Check file type\r\n  const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();\r\n  if (!allowedTypes.includes(fileExtension)) {\r\n    return `File type must be: ${allowedTypes.join(', ')}`;\r\n  }\r\n\r\n  return null;\r\n};\r\n\r\n// Section validation interface\r\nexport interface ValidationResult {\r\n  isValid: boolean;\r\n  errors: Record<string, string>;\r\n}\r\n\r\n// Validate section data for application forms\r\nexport const validateSection = (data: Record<string, any>, sectionName: string): ValidationResult => {\r\n  const errors: Record<string, string> = {};\r\n\r\n  switch (sectionName) {\r\n    case 'applicantInfo':\r\n      // Required fields for applicant info\r\n      const applicantRequiredFields = [\r\n        'applicant_type', 'first_name', 'last_name', 'email', 'phone',\r\n        'national_id', 'date_of_birth', 'nationality', 'gender',\r\n        'postal_address', 'physical_address', 'city', 'district'\r\n      ];\r\n\r\n      applicantRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.email)) {\r\n        errors.email = 'Please enter a valid email address';\r\n      }\r\n\r\n      // Phone validation\r\n      if (data.phone && !/^(\\+265|0)?[1-9]\\d{7,8}$/.test(data.phone)) {\r\n        errors.phone = 'Please enter a valid phone number';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'companyProfile':\r\n      const companyRequiredFields = [\r\n        'company_name', 'business_registration_number', 'tax_number', 'company_type',\r\n        'incorporation_date', 'incorporation_place', 'company_email', 'company_phone',\r\n        'company_address', 'company_city', 'company_district', 'number_of_employees',\r\n        'annual_revenue', 'business_description'\r\n      ];\r\n\r\n      companyRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      // Email validation\r\n      if (data.company_email && !/^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/.test(data.company_email)) {\r\n        errors.company_email = 'Please enter a valid email address';\r\n      }\r\n\r\n      break;\r\n\r\n    case 'businessInfo':\r\n      const businessRequiredFields = [\r\n        'business_model', 'operational_structure', 'target_market', 'competitive_advantage',\r\n        'facilities_description', 'equipment_description', 'operational_areas',\r\n        'service_delivery_model', 'quality_assurance', 'customer_support'\r\n      ];\r\n\r\n      businessRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'serviceScope':\r\n      const serviceScopeRequiredFields = [\r\n        'services_offered', 'geographic_coverage', 'service_categories',\r\n        'target_customers', 'service_capacity'\r\n      ];\r\n\r\n      serviceScopeRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'businessPlan':\r\n      const businessPlanRequiredFields = [\r\n        'executive_summary', 'market_analysis', 'financial_projections',\r\n        'revenue_model', 'investment_requirements', 'implementation_timeline',\r\n        'risk_analysis', 'success_metrics'\r\n      ];\r\n\r\n      businessPlanRequiredFields.forEach(field => {\r\n        if (!data[field] || (typeof data[field] === 'string' && data[field].trim() === '')) {\r\n          errors[field] = `${field.replace(/_/g, ' ')} is required`;\r\n        }\r\n      });\r\n\r\n      break;\r\n\r\n    case 'legalHistory':\r\n      // Required fields\r\n      if (!data.compliance_record || data.compliance_record.trim() === '') {\r\n        errors.compliance_record = 'Compliance record is required';\r\n      }\r\n\r\n      // Declaration must be accepted\r\n      if (!data.declaration_accepted) {\r\n        errors.declaration_accepted = 'You must accept the declaration to proceed';\r\n      }\r\n\r\n      // Conditional validations\r\n      if (data.criminal_history && (!data.criminal_details || data.criminal_details.trim() === '')) {\r\n        errors.criminal_details = 'Please provide details of your criminal history';\r\n      }\r\n\r\n      if (data.bankruptcy_history && (!data.bankruptcy_details || data.bankruptcy_details.trim() === '')) {\r\n        errors.bankruptcy_details = 'Please provide details of your bankruptcy history';\r\n      }\r\n\r\n      if (data.regulatory_actions && (!data.regulatory_details || data.regulatory_details.trim() === '')) {\r\n        errors.regulatory_details = 'Please provide details of regulatory actions';\r\n      }\r\n\r\n      if (data.litigation_history && (!data.litigation_details || data.litigation_details.trim() === '')) {\r\n        errors.litigation_details = 'Please provide details of litigation history';\r\n      }\r\n\r\n      break;\r\n\r\n    default:\r\n      // No validation for unknown sections\r\n      break;\r\n  }\r\n\r\n  return {\r\n    isValid: Object.keys(errors).length === 0,\r\n    errors\r\n  };\r\n};\r\n"], "names": [], "mappings": "AAAA,4BAA4B;;;;;;;;;;;;AAcrB,MAAM,gBAAgB,CAAC,OAAY;IACxC,sBAAsB;IACtB,IAAI,MAAM,QAAQ,IAAI,CAAC,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,EAAG,GAAG;QACpF,OAAO;IACT;IAEA,4DAA4D;IAC5D,IAAI,CAAC,SAAU,OAAO,UAAU,YAAY,MAAM,IAAI,OAAO,IAAK;QAChE,OAAO;IACT;IAEA,qBAAqB;IACrB,IAAI,OAAO,UAAU,UAAU;QAC7B,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,iBAAiB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QACzD;QAEA,wBAAwB;QACxB,IAAI,MAAM,SAAS,IAAI,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE;YACrD,OAAO,CAAC,qBAAqB,EAAE,MAAM,SAAS,CAAC,WAAW,CAAC;QAC7D;QAEA,qBAAqB;QACrB,IAAI,MAAM,OAAO,IAAI,CAAC,MAAM,OAAO,CAAC,IAAI,CAAC,QAAQ;YAC/C,OAAO;QACT;IACF;IAEA,oBAAoB;IACpB,IAAI,MAAM,MAAM,EAAE;QAChB,OAAO,MAAM,MAAM,CAAC;IACtB;IAEA,OAAO;AACT;AAEO,MAAM,eAAe,CAAC,MAAW;IACtC,MAAM,SAA2B,CAAC;IAElC,OAAO,IAAI,CAAC,OAAO,OAAO,CAAC,CAAA;QACzB,MAAM,QAAQ,cAAc,IAAI,CAAC,MAAM,EAAE,KAAK,CAAC,MAAM;QACrD,IAAI,OAAO;YACT,MAAM,CAAC,MAAM,GAAG;QAClB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,OAAO;IACP,KAAK;IACL,cAAc;IACd,YAAY;IACZ,SAAS;IACT,YAAY;AACd;AAGO,MAAM,cAAc;IACzB,UAAU;QAAE,UAAU;IAAK;IAC3B,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,UAAU;QACV,SAAS,SAAS,KAAK;QACvB,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,QAAQ;gBACxC,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,sBAAsB;QACpB,UAAU;QACV,WAAW;QACX,QAAQ,CAAC;YACP,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;gBAC7B,OAAO;YACT;YACA,OAAO;QACT;IACF;IACA,YAAY;QACV,SAAS,SAAS,UAAU;QAC5B,QAAQ,CAAC;YACP,IAAI,SAAS,CAAC,SAAS,UAAU,CAAC,IAAI,CAAC,QAAQ;gBAC7C,OAAO;YACT;YACA,OAAO;QACT;IACF;AACF;AAGO,MAAM,YAAY,CAAC;IACxB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,GAAG;AACtC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,WAAW,OAAO,IAAI,CAAC,OAAO,CAAC,EAAE;IACvC,OAAO,WAAW,MAAM,CAAC,SAAS,GAAG;AACvC;AAGO,MAAM,qBAAqB,CAChC,OACA,OACA,UACA;IAEA,MAAM,cAAmD,CAAC;IAE1D,qBAAqB;IACrB,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,mBAAmB,EAAE,SAAS,MAAM,CAAC;QAAC;IACxE;IACA,IAAI,YAAY,MAAM,MAAM,GAAG,UAAU;QACvC,YAAY,MAAM,GAAG;YAAE,QAAQ,CAAC,uBAAuB,EAAE,SAAS,MAAM,CAAC;QAAC;IAC5E;IAEA,8BAA8B;IAC9B,MAAM,OAAO,CAAC,CAAC,MAAM;QACnB,MAAM,aAAa,aAAa,MAAM;QACtC,IAAI,UAAU,aAAa;YACzB,WAAW,CAAC,MAAM,GAAG;QACvB;IACF;IAEA,OAAO;AACT;AAGO,MAAM,eAAe,CAC1B,MACA,WAAoB,KAAK,EACzB,UAAkB,EAAE,EACpB,eAAyB;IAAC;CAAO;IAEjC,IAAI,YAAY,CAAC,MAAM;QACrB,OAAO;IACT;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,kBAAkB;IAClB,IAAI,KAAK,IAAI,GAAG,UAAU,OAAO,MAAM;QACrC,OAAO,CAAC,4BAA4B,EAAE,QAAQ,EAAE,CAAC;IACnD;IAEA,kBAAkB;IAClB,MAAM,gBAAgB,MAAM,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,IAAI;IACxD,IAAI,CAAC,aAAa,QAAQ,CAAC,gBAAgB;QACzC,OAAO,CAAC,mBAAmB,EAAE,aAAa,IAAI,CAAC,OAAO;IACxD;IAEA,OAAO;AACT;AASO,MAAM,kBAAkB,CAAC,MAA2B;IACzD,MAAM,SAAiC,CAAC;IAExC,OAAQ;QACN,KAAK;YACH,qCAAqC;YACrC,MAAM,0BAA0B;gBAC9B;gBAAkB;gBAAc;gBAAa;gBAAS;gBACtD;gBAAe;gBAAiB;gBAAe;gBAC/C;gBAAkB;gBAAoB;gBAAQ;aAC/C;YAED,wBAAwB,OAAO,CAAC,CAAA;gBAC9B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAChE,OAAO,KAAK,GAAG;YACjB;YAEA,mBAAmB;YACnB,IAAI,KAAK,KAAK,IAAI,CAAC,2BAA2B,IAAI,CAAC,KAAK,KAAK,GAAG;gBAC9D,OAAO,KAAK,GAAG;YACjB;YAEA;QAEF,KAAK;YACH,MAAM,wBAAwB;gBAC5B;gBAAgB;gBAAgC;gBAAc;gBAC9D;gBAAsB;gBAAuB;gBAAiB;gBAC9D;gBAAmB;gBAAgB;gBAAoB;gBACvD;gBAAkB;aACnB;YAED,sBAAsB,OAAO,CAAC,CAAA;gBAC5B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA,mBAAmB;YACnB,IAAI,KAAK,aAAa,IAAI,CAAC,6BAA6B,IAAI,CAAC,KAAK,aAAa,GAAG;gBAChF,OAAO,aAAa,GAAG;YACzB;YAEA;QAEF,KAAK;YACH,MAAM,yBAAyB;gBAC7B;gBAAkB;gBAAyB;gBAAiB;gBAC5D;gBAA0B;gBAAyB;gBACnD;gBAA0B;gBAAqB;aAChD;YAED,uBAAuB,OAAO,CAAC,CAAA;gBAC7B,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAoB;gBAAuB;gBAC3C;gBAAoB;aACrB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,MAAM,6BAA6B;gBACjC;gBAAqB;gBAAmB;gBACxC;gBAAiB;gBAA2B;gBAC5C;gBAAiB;aAClB;YAED,2BAA2B,OAAO,CAAC,CAAA;gBACjC,IAAI,CAAC,IAAI,CAAC,MAAM,IAAK,OAAO,IAAI,CAAC,MAAM,KAAK,YAAY,IAAI,CAAC,MAAM,CAAC,IAAI,OAAO,IAAK;oBAClF,MAAM,CAAC,MAAM,GAAG,GAAG,MAAM,OAAO,CAAC,MAAM,KAAK,YAAY,CAAC;gBAC3D;YACF;YAEA;QAEF,KAAK;YACH,kBAAkB;YAClB,IAAI,CAAC,KAAK,iBAAiB,IAAI,KAAK,iBAAiB,CAAC,IAAI,OAAO,IAAI;gBACnE,OAAO,iBAAiB,GAAG;YAC7B;YAEA,+BAA+B;YAC/B,IAAI,CAAC,KAAK,oBAAoB,EAAE;gBAC9B,OAAO,oBAAoB,GAAG;YAChC;YAEA,0BAA0B;YAC1B,IAAI,KAAK,gBAAgB,IAAI,CAAC,CAAC,KAAK,gBAAgB,IAAI,KAAK,gBAAgB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAC5F,OAAO,gBAAgB,GAAG;YAC5B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA,IAAI,KAAK,kBAAkB,IAAI,CAAC,CAAC,KAAK,kBAAkB,IAAI,KAAK,kBAAkB,CAAC,IAAI,OAAO,EAAE,GAAG;gBAClG,OAAO,kBAAkB,GAAG;YAC9B;YAEA;QAEF;YAEE;IACJ;IAEA,OAAO;QACL,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;QACxC;IACF;AACF", "debugId": null}}, {"offset": {"line": 2025, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationFormDataService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { processApiResponse } from '@/lib/authUtils';\n\nexport interface ApplicationFormData {\n  form_data_id?: string;\n  application_id: string;\n  section_name: string;\n  section_data: Record<string, any>;\n  completed: boolean;\n  created_at?: string;\n  updated_at?: string;\n}\n\nexport const applicationFormDataService = {\n  // Save form section data\n  async saveFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to saveFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to saveFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Saving form section ${sectionName} for application ${applicationId}:`, sectionData);\n\n      const response = await apiClient.post('/application-form-data', {\n        application_id: applicationId,\n        section_name: sectionName,\n        section_data: sectionData,\n        completed: true\n      });\n\n      console.log(`Form section ${sectionName} saved successfully:`, response.data);\n      return processApiResponse(response);\n    } catch (error) {\n      console.error(`Error saving form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Update existing form section data\n  async updateFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to updateFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to updateFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Updating form section ${sectionName} for application ${applicationId}:`, sectionData);\n\n      const response = await apiClient.put(`/application-form-data/${applicationId}/${sectionName}`, {\n        section_data: sectionData,\n        completed: true\n      });\n\n      console.log(`Form section ${sectionName} updated successfully:`, response.data);\n      return processApiResponse(response);\n    } catch (error) {\n      console.error(`Error updating form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Get form section data\n  async getFormSection(applicationId: string, sectionName: string): Promise<ApplicationFormData | null> {\n    // Validate inputs before making API call\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      console.warn('Invalid applicationId provided to getFormSection:', applicationId);\n      return null;\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      console.warn('Invalid sectionName provided to getFormSection:', sectionName);\n      return null;\n    }\n\n    try {\n      console.log(`Fetching form section ${sectionName} for application ${applicationId}`);\n      const response = await apiClient.get(`/application-form-data/${applicationId}/${sectionName}`);\n      return processApiResponse(response);\n    } catch (error) {\n      if ((error as any)?.response?.status === 404) {\n        console.log(`Form section ${sectionName} not found for application ${applicationId} - this is normal for new applications`);\n        return null; // Section doesn't exist yet\n      }\n      console.error(`Error fetching form section ${sectionName} for application ${applicationId}:`, error);\n      throw error;\n    }\n  },\n\n  // Get all form data for an application\n  async getApplicationFormData(applicationId: string): Promise<Record<string, any>> {\n    try {\n      const response = await apiClient.get(`/application-form-data/${applicationId}`);\n      const processedResponse = processApiResponse(response);\n\n      // Convert array of sections to object\n      const formData: Record<string, any> = {};\n      if (Array.isArray(processedResponse)) {\n        processedResponse.forEach((section: ApplicationFormData) => {\n          formData[section.section_name] = section.section_data;\n        });\n      }\n\n      return formData;\n    } catch (error) {\n      console.error('Error fetching application form data:', error);\n      return {}; // Return empty object if no data found\n    }\n  },\n\n  // Delete form section\n  async deleteFormSection(applicationId: string, sectionName: string): Promise<{ message: string }> {\n    try {\n      const response = await apiClient.delete(`/application-form-data/${applicationId}/${sectionName}`);\n      return response.data;\n    } catch (error) {\n      console.error(`Error deleting form section ${sectionName}:`, error);\n      throw error;\n    }\n  },\n\n  // Save or update form section (upsert)\n  async saveOrUpdateFormSection(\n    applicationId: string,\n    sectionName: string,\n    sectionData: Record<string, any>\n  ): Promise<ApplicationFormData> {\n    // Validate inputs before proceeding\n    if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {\n      throw new Error(`Invalid applicationId provided to saveOrUpdateFormSection: ${applicationId}`);\n    }\n\n    if (!sectionName || sectionName.trim() === '') {\n      throw new Error(`Invalid sectionName provided to saveOrUpdateFormSection: ${sectionName}`);\n    }\n\n    try {\n      console.log(`Saving/updating form section ${sectionName} for application ${applicationId}`);\n\n      // Try to get existing section first\n      const existingSection = await this.getFormSection(applicationId, sectionName);\n\n      if (existingSection) {\n        // Update existing section\n        console.log(`Updating existing section ${sectionName}`);\n        return await this.updateFormSection(applicationId, sectionName, sectionData);\n      } else {\n        // Create new section\n        console.log(`Creating new section ${sectionName}`);\n        return await this.saveFormSection(applicationId, sectionName, sectionData);\n      }\n    } catch (error) {\n      console.error(`Error saving/updating form section ${sectionName}:`, error);\n      throw error;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAYO,MAAM,6BAA6B;IACxC,yBAAyB;IACzB,MAAM,iBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,mDAAmD,EAAE,eAAe;QACvF;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,iDAAiD,EAAE,aAAa;QACnF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,oBAAoB,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAEpF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,0BAA0B;gBAC9D,gBAAgB;gBAChB,cAAc;gBACd,cAAc;gBACd,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,oBAAoB,CAAC,EAAE,SAAS,IAAI;YAC5E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,0BAA0B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC3D,MAAM;QACR;IACF;IAEA,oCAAoC;IACpC,MAAM,mBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,qDAAqD,EAAE,eAAe;QACzF;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,mDAAmD,EAAE,aAAa;QACrF;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAEtF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa,EAAE;gBAC7F,cAAc;gBACd,WAAW;YACb;YAEA,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,sBAAsB,CAAC,EAAE,SAAS,IAAI;YAC9E,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC7D,MAAM;QACR;IACF;IAEA,wBAAwB;IACxB,MAAM,gBAAe,aAAqB,EAAE,WAAmB;QAC7D,yCAAyC;QACzC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,QAAQ,IAAI,CAAC,qDAAqD;YAClE,OAAO;QACT;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,QAAQ,IAAI,CAAC,mDAAmD;YAChE,OAAO;QACT;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,YAAY,iBAAiB,EAAE,eAAe;YACnF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa;YAC7F,OAAO,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;QAC5B,EAAE,OAAO,OAAO;YACd,IAAI,AAAC,OAAe,UAAU,WAAW,KAAK;gBAC5C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,YAAY,2BAA2B,EAAE,cAAc,sCAAsC,CAAC;gBAC1H,OAAO,MAAM,4BAA4B;YAC3C;YACA,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,iBAAiB,EAAE,cAAc,CAAC,CAAC,EAAE;YAC9F,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,MAAM,wBAAuB,aAAqB;QAChD,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,uBAAuB,EAAE,eAAe;YAC9E,MAAM,oBAAoB,CAAA,GAAA,uHAAA,CAAA,qBAAkB,AAAD,EAAE;YAE7C,sCAAsC;YACtC,MAAM,WAAgC,CAAC;YACvC,IAAI,MAAM,OAAO,CAAC,oBAAoB;gBACpC,kBAAkB,OAAO,CAAC,CAAC;oBACzB,QAAQ,CAAC,QAAQ,YAAY,CAAC,GAAG,QAAQ,YAAY;gBACvD;YACF;YAEA,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,OAAO,CAAC,GAAG,uCAAuC;QACpD;IACF;IAEA,sBAAsB;IACtB,MAAM,mBAAkB,aAAqB,EAAE,WAAmB;QAChE,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,uBAAuB,EAAE,cAAc,CAAC,EAAE,aAAa;YAChG,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,4BAA4B,EAAE,YAAY,CAAC,CAAC,EAAE;YAC7D,MAAM;QACR;IACF;IAEA,uCAAuC;IACvC,MAAM,yBACJ,aAAqB,EACrB,WAAmB,EACnB,WAAgC;QAEhC,oCAAoC;QACpC,IAAI,CAAC,iBAAiB,kBAAkB,eAAe,kBAAkB,SAAS,cAAc,IAAI,OAAO,IAAI;YAC7G,MAAM,IAAI,MAAM,CAAC,2DAA2D,EAAE,eAAe;QAC/F;QAEA,IAAI,CAAC,eAAe,YAAY,IAAI,OAAO,IAAI;YAC7C,MAAM,IAAI,MAAM,CAAC,yDAAyD,EAAE,aAAa;QAC3F;QAEA,IAAI;YACF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,YAAY,iBAAiB,EAAE,eAAe;YAE1F,oCAAoC;YACpC,MAAM,kBAAkB,MAAM,IAAI,CAAC,cAAc,CAAC,eAAe;YAEjE,IAAI,iBAAiB;gBACnB,0BAA0B;gBAC1B,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,aAAa;gBACtD,OAAO,MAAM,IAAI,CAAC,iBAAiB,CAAC,eAAe,aAAa;YAClE,OAAO;gBACL,qBAAqB;gBACrB,QAAQ,GAAG,CAAC,CAAC,qBAAqB,EAAE,aAAa;gBACjD,OAAO,MAAM,IAAI,CAAC,eAAe,CAAC,eAAe,aAAa;YAChE;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,YAAY,CAAC,CAAC,EAAE;YACpE,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 2165, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicantService.ts"], "sourcesContent": ["import { apiClient } from '../lib/apiClient';\nimport { Applicant } from '../types/license';\n\nexport interface CreateApplicantData {\n  name: string;\n  business_registration_number: string;\n  tpin: string;\n  website: string;\n  email: string;\n  phone: string;\n  fax?: string;\n  level_of_insurance_cover?: string;\n  address_id?: string;\n  contact_id?: string;\n  date_incorporation: string; // Changed from Date to string to match backend DTO\n  place_incorporation: string;\n}\n\nexport const applicantService = {\n  // Create new applicant\n  async createApplicant(data: CreateApplicantData): Promise<Applicant> {\n    try {\n      console.log('Creating applicant with data:', data);\n\n      const response = await apiClient.post('/applicants', data);\n      // Handle different response formats\n      if (response.data) {\n        // Check if it's a standard success response format\n        if (response.data.success !== undefined && response.data.data) {\n          console.log('Standard response format detected');\n          return response.data.data;\n        }\n        // Check if it's direct data format\n        else if (response.data.applicant_id || response.data.id) {\n          console.log('Direct data format detected');\n          return response.data;\n        }\n        // Fallback: assume it's the applicant data\n        else {\n          console.log('Fallback: treating response.data as applicant');\n          return response.data;\n        }\n      }\n\n      throw new Error('Invalid response format from applicant creation');\n    } catch (error) {\n      console.error('Error creating applicant:', error);\n      console.error('Error details:', (error as any)?.response?.data);\n      throw error;\n    }\n  },\n\n  // Get applicant by ID\n  async getApplicant(id: string): Promise<Applicant> {\n    try {\n      const response = await apiClient.get(`/applicants/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching applicant:', error);\n      throw error;\n    }\n  },\n\n  // Update applicant\n  async updateApplicant(id: string, data: Partial<CreateApplicantData>): Promise<Applicant> {\n    try {\n      console.log('Updating applicant:', id, data);\n      \n      const response = await apiClient.put(`/applicants/${id}`, data);\n      \n      console.log('Applicant updated successfully:', response.data);\n      return response.data;\n    } catch (error) {\n      console.error('Error updating applicant:', error);\n      throw error;\n    }\n  },\n\n  // Get applicants by user (if user can have multiple applicants)\n  async getApplicantsByUser(): Promise<Applicant[]> {\n    try {\n      const response = await apiClient.get('/applicants/by-user');\n      return response.data;\n    } catch (error) {\n      console.error('Error fetching user applicants:', error);\n      throw error;\n    }\n  },\n\n  // Delete applicant\n  async deleteApplicant(id: string): Promise<{ message: string }> {\n    try {\n      const response = await apiClient.delete(`/applicants/${id}`);\n      return response.data;\n    } catch (error) {\n      console.error('Error deleting applicant:', error);\n      throw error;\n    }\n  }\n};\n"], "names": [], "mappings": ";;;AAAA;;AAkBO,MAAM,mBAAmB;IAC9B,uBAAuB;IACvB,MAAM,iBAAgB,IAAyB;QAC7C,IAAI;YACF,QAAQ,GAAG,CAAC,iCAAiC;YAE7C,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,IAAI,CAAC,eAAe;YACrD,oCAAoC;YACpC,IAAI,SAAS,IAAI,EAAE;gBACjB,mDAAmD;gBACnD,IAAI,SAAS,IAAI,CAAC,OAAO,KAAK,aAAa,SAAS,IAAI,CAAC,IAAI,EAAE;oBAC7D,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI,CAAC,IAAI;gBAC3B,OAEK,IAAI,SAAS,IAAI,CAAC,YAAY,IAAI,SAAS,IAAI,CAAC,EAAE,EAAE;oBACvD,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI;gBACtB,OAEK;oBACH,QAAQ,GAAG,CAAC;oBACZ,OAAO,SAAS,IAAI;gBACtB;YACF;YAEA,MAAM,IAAI,MAAM;QAClB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,QAAQ,KAAK,CAAC,kBAAmB,OAAe,UAAU;YAC1D,MAAM;QACR;IACF;IAEA,sBAAsB;IACtB,MAAM,cAAa,EAAU;QAC3B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;YACxD,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU,EAAE,IAAkC;QAClE,IAAI;YACF,QAAQ,GAAG,CAAC,uBAAuB,IAAI;YAEvC,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI,EAAE;YAE1D,QAAQ,GAAG,CAAC,mCAAmC,SAAS,IAAI;YAC5D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;IAEA,gEAAgE;IAChE,MAAM;QACJ,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,GAAG,CAAC;YACrC,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mCAAmC;YACjD,MAAM;QACR;IACF;IAEA,mBAAmB;IACnB,MAAM,iBAAgB,EAAU;QAC9B,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,YAAS,CAAC,MAAM,CAAC,CAAC,YAAY,EAAE,IAAI;YAC3D,OAAO,SAAS,IAAI;QACtB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6BAA6B;YAC3C,MAAM;QACR;IACF;AACF", "debugId": null}}, {"offset": {"line": 2246, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/config/licenseTypeStepConfig.ts"], "sourcesContent": ["/**\n * License Type Step Configuration System\n * Defines which form steps are required for each license type\n */\n\nexport interface StepConfig {\n  id: string;\n  name: string;\n  component: string;\n  route: string;\n  required: boolean;\n  description: string;\n  estimatedTime: string; // in minutes\n}\n\nexport interface LicenseTypeStepConfig {\n  licenseTypeId: string;\n  name: string;\n  description: string;\n  steps: StepConfig[];\n  estimatedTotalTime: string;\n  requirements: string[];\n}\n\n// Base steps that can be used across license types\nconst BASE_STEPS: Record<string, StepConfig> = {\n  applicantInfo: {\n    id: 'applicant-info',\n    name: 'Applicant Information',\n    component: 'ApplicantInfo',\n    route: 'applicant-info',\n    required: true,\n    description: 'Personal or company information of the applicant',\n    estimatedTime: '5'\n  },\n  companyProfile: {\n    id: 'company-profile',\n    name: 'Company Profile',\n    component: 'CompanyProfile',\n    route: 'company-profile',\n    required: true,\n    description: 'Company structure, shareholders, and directors',\n    estimatedTime: '10'\n  },\n  management: {\n    id: 'management',\n    name: 'Management Structure',\n    component: 'Management',\n    route: 'management',\n    required: false,\n    description: 'Management team and organizational structure',\n    estimatedTime: '8'\n  },\n  professionalServices: {\n    id: 'professional-services',\n    name: 'Professional Services',\n    component: 'ProfessionalServices',\n    route: 'professional-services',\n    required: false,\n    description: 'External consultants and service providers',\n    estimatedTime: '6'\n  },\n  businessInfo: {\n    id: 'business-info',\n    name: 'Business Information',\n    component: 'BusinessInfo',\n    route: 'business-info',\n    required: true,\n    description: 'Business description and operational plan',\n    estimatedTime: '7'\n  },\n  serviceScope: {\n    id: 'service-scope',\n    name: 'Service Scope',\n    component: 'ServiceScope',\n    route: 'service-scope',\n    required: true,\n    description: 'Services offered and geographic coverage',\n    estimatedTime: '8'\n  },\n  businessPlan: {\n    id: 'business-plan',\n    name: 'Business Plan',\n    component: 'BusinessPlan',\n    route: 'business-plan',\n    required: true,\n    description: 'Market analysis and financial projections',\n    estimatedTime: '15'\n  },\n  legalHistory: {\n    id: 'legal-history',\n    name: 'Legal History',\n    component: 'LegalHistory',\n    route: 'legal-history',\n    required: true,\n    description: 'Legal compliance and regulatory history',\n    estimatedTime: '5'\n  },\n  reviewSubmit: {\n    id: 'review-submit',\n    name: 'Review & Submit',\n    component: 'ReviewSubmit',\n    route: 'review-submit',\n    required: true,\n    description: 'Review all information and submit application',\n    estimatedTime: '10'\n  }\n};\n\n// License type specific configurations\nexport const LICENSE_TYPE_STEP_CONFIGS: Record<string, LicenseTypeStepConfig> = {\n  telecommunications: {\n    licenseTypeId: 'telecommunications',\n    name: 'Telecommunications License',\n    description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '74 minutes',\n    requirements: [\n      'Business registration certificate',\n      'Tax compliance certificate',\n      'Technical specifications',\n      'Financial statements',\n      'Management CVs',\n      'Network coverage plans'\n    ]\n  },\n\n  postal_services: {\n    licenseTypeId: 'postal_services',\n    name: 'Postal Services License',\n    description: 'License for postal and courier service providers',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '42 minutes',\n    requirements: [\n      'Business registration certificate',\n      'Fleet inventory',\n      'Service coverage map',\n      'Insurance certificates',\n      'Premises documentation'\n    ]\n  },\n\n  standards_compliance: {\n    licenseTypeId: 'standards_compliance',\n    name: 'Standards Compliance License',\n    description: 'License for standards compliance and certification services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '59 minutes',\n    requirements: [\n      'Accreditation certificates',\n      'Technical competency proof',\n      'Quality management system',\n      'Laboratory facilities documentation',\n      'Staff qualifications'\n    ]\n  },\n\n  broadcasting: {\n    licenseTypeId: 'broadcasting',\n    name: 'Broadcasting License',\n    description: 'License for radio and television broadcasting services',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '63 minutes',\n    requirements: [\n      'Broadcasting equipment specifications',\n      'Content programming plan',\n      'Studio facility documentation',\n      'Transmission coverage maps',\n      'Local content compliance plan'\n    ]\n  },\n\n  spectrum_management: {\n    licenseTypeId: 'spectrum_management',\n    name: 'Spectrum Management License',\n    description: 'License for radio frequency spectrum management and allocation',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.professionalServices,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.serviceScope,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '74 minutes',\n    requirements: [\n      'Spectrum usage plan',\n      'Technical interference analysis',\n      'Equipment type approval',\n      'Frequency coordination agreements',\n      'Monitoring capabilities documentation'\n    ]\n  },\n\n  clf: {\n    licenseTypeId: 'clf',\n    name: 'CLF License',\n    description: 'Consumer Lending and Finance license',\n    steps: [\n      BASE_STEPS.applicantInfo,\n      BASE_STEPS.companyProfile,\n      BASE_STEPS.management,\n      BASE_STEPS.businessInfo,\n      BASE_STEPS.businessPlan,\n      BASE_STEPS.legalHistory,\n      BASE_STEPS.reviewSubmit\n    ],\n    estimatedTotalTime: '50 minutes',\n    requirements: [\n      'Financial institution license',\n      'Capital adequacy documentation',\n      'Risk management framework',\n      'Consumer protection policies',\n      'Anti-money laundering procedures'\n    ]\n  }\n};\n\n// License type name to config key mapping\nconst LICENSE_TYPE_NAME_MAPPING: Record<string, string> = {\n  'telecommunications': 'telecommunications',\n  'postal services': 'postal_services',\n  'postal_services': 'postal_services',\n  'standards compliance': 'standards_compliance',\n  'standards_compliance': 'standards_compliance',\n  'broadcasting': 'broadcasting',\n  'spectrum management': 'spectrum_management',\n  'spectrum_management': 'spectrum_management',\n  'clf': 'clf',\n  'consumer lending and finance': 'clf'\n};\n\n// Helper functions\nexport const getLicenseTypeStepConfig = (licenseTypeId: string): LicenseTypeStepConfig | null => {\n  console.log('getLicenseTypeStepConfig called with:', licenseTypeId);\n  console.log('Available configs:', Object.keys(LICENSE_TYPE_STEP_CONFIGS));\n  console.log('UUID to code map:', licenseTypeUUIDToCodeMap);\n\n  // First try direct lookup\n  let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];\n  if (config) {\n    console.log('Found config via direct lookup:', config.name);\n    return config;\n  }\n\n  // Try normalized lookup\n  const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');\n  config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];\n  if (config) {\n    console.log('Found config via normalized lookup:', config.name);\n    return config;\n  }\n\n  // Try name mapping\n  const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];\n  if (mappedKey) {\n    console.log('Found config via name mapping:', mappedKey);\n    return LICENSE_TYPE_STEP_CONFIGS[mappedKey];\n  }\n\n  // If licenseTypeId looks like a UUID, try to get the code from license types\n  if (isUUID(licenseTypeId)) {\n    console.log('Detected UUID, trying to get code...');\n    const code = getLicenseTypeCodeFromUUID(licenseTypeId);\n    console.log('Got code from UUID:', code);\n    if (code) {\n      const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];\n      if (foundConfig) {\n        console.log('Found config via UUID mapping:', foundConfig.name);\n        return foundConfig;\n      }\n    }\n  }\n\n  console.log('No config found for license type:', licenseTypeId);\n  return null;\n};\n\n// Helper function to check if a string is a UUID\nconst isUUID = (str: string): boolean => {\n  const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;\n  return uuidRegex.test(str);\n};\n\n// Helper function to get license type code from UUID\n// This will be populated by the license type service\nlet licenseTypeUUIDToCodeMap: Record<string, string> = {};\n\nexport const setLicenseTypeUUIDToCodeMap = (map: Record<string, string>) => {\n  licenseTypeUUIDToCodeMap = map;\n};\n\nconst getLicenseTypeCodeFromUUID = (uuid: string): string | null => {\n  return licenseTypeUUIDToCodeMap[uuid] || null;\n};\n\nexport const getStepByRoute = (licenseTypeId: string, stepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  return config.steps.find(step => step.route === stepRoute) || null;\n};\n\nexport const getStepByIndex = (licenseTypeId: string, stepIndex: number): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config || stepIndex < 0 || stepIndex >= config.steps.length) return null;\n  \n  return config.steps[stepIndex];\n};\n\nexport const getStepIndex = (licenseTypeId: string, stepRoute: string): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return -1;\n  \n  return config.steps.findIndex(step => step.route === stepRoute);\n};\n\nexport const getTotalSteps = (licenseTypeId: string): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.length : 0;\n};\n\nexport const getRequiredSteps = (licenseTypeId: string): StepConfig[] => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.filter(step => step.required) : [];\n};\n\nexport const getOptionalSteps = (licenseTypeId: string): StepConfig[] => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  return config ? config.steps.filter(step => !step.required) : [];\n};\n\nexport const calculateProgress = (licenseTypeId: string, completedSteps: string[]): number => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return 0;\n  \n  const totalSteps = config.steps.length;\n  const completed = completedSteps.length;\n  \n  return Math.round((completed / totalSteps) * 100);\n};\n\nexport const getNextStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\n  if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;\n  \n  return config.steps[currentIndex + 1];\n};\n\nexport const getPreviousStep = (licenseTypeId: string, currentStepRoute: string): StepConfig | null => {\n  const config = getLicenseTypeStepConfig(licenseTypeId);\n  if (!config) return null;\n  \n  const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);\n  if (currentIndex <= 0) return null;\n  \n  return config.steps[currentIndex - 1];\n};\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;;;;;;;;;;;AAqBD,mDAAmD;AACnD,MAAM,aAAyC;IAC7C,eAAe;QACb,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,gBAAgB;QACd,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,YAAY;QACV,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,sBAAsB;QACpB,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;IACA,cAAc;QACZ,IAAI;QACJ,MAAM;QACN,WAAW;QACX,OAAO;QACP,UAAU;QACV,aAAa;QACb,eAAe;IACjB;AACF;AAGO,MAAM,4BAAmE;IAC9E,oBAAoB;QAClB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,iBAAiB;QACf,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,sBAAsB;QACpB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,cAAc;QACZ,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,qBAAqB;QACnB,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,oBAAoB;YAC/B,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;IAEA,KAAK;QACH,eAAe;QACf,MAAM;QACN,aAAa;QACb,OAAO;YACL,WAAW,aAAa;YACxB,WAAW,cAAc;YACzB,WAAW,UAAU;YACrB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;YACvB,WAAW,YAAY;SACxB;QACD,oBAAoB;QACpB,cAAc;YACZ;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAEA,0CAA0C;AAC1C,MAAM,4BAAoD;IACxD,sBAAsB;IACtB,mBAAmB;IACnB,mBAAmB;IACnB,wBAAwB;IACxB,wBAAwB;IACxB,gBAAgB;IAChB,uBAAuB;IACvB,uBAAuB;IACvB,OAAO;IACP,gCAAgC;AAClC;AAGO,MAAM,2BAA2B,CAAC;IACvC,QAAQ,GAAG,CAAC,yCAAyC;IACrD,QAAQ,GAAG,CAAC,sBAAsB,OAAO,IAAI,CAAC;IAC9C,QAAQ,GAAG,CAAC,qBAAqB;IAEjC,0BAA0B;IAC1B,IAAI,SAAS,yBAAyB,CAAC,cAAc;IACrD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,mCAAmC,OAAO,IAAI;QAC1D,OAAO;IACT;IAEA,wBAAwB;IACxB,MAAM,eAAe,cAAc,WAAW,GAAG,OAAO,CAAC,cAAc;IACvE,SAAS,yBAAyB,CAAC,aAAa;IAChD,IAAI,QAAQ;QACV,QAAQ,GAAG,CAAC,uCAAuC,OAAO,IAAI;QAC9D,OAAO;IACT;IAEA,mBAAmB;IACnB,MAAM,YAAY,yBAAyB,CAAC,aAAa;IACzD,IAAI,WAAW;QACb,QAAQ,GAAG,CAAC,kCAAkC;QAC9C,OAAO,yBAAyB,CAAC,UAAU;IAC7C;IAEA,6EAA6E;IAC7E,IAAI,OAAO,gBAAgB;QACzB,QAAQ,GAAG,CAAC;QACZ,MAAM,OAAO,2BAA2B;QACxC,QAAQ,GAAG,CAAC,uBAAuB;QACnC,IAAI,MAAM;YACR,MAAM,cAAc,yBAAyB,CAAC,KAAK;YACnD,IAAI,aAAa;gBACf,QAAQ,GAAG,CAAC,kCAAkC,YAAY,IAAI;gBAC9D,OAAO;YACT;QACF;IACF;IAEA,QAAQ,GAAG,CAAC,qCAAqC;IACjD,OAAO;AACT;AAEA,iDAAiD;AACjD,MAAM,SAAS,CAAC;IACd,MAAM,YAAY;IAClB,OAAO,UAAU,IAAI,CAAC;AACxB;AAEA,qDAAqD;AACrD,qDAAqD;AACrD,IAAI,2BAAmD,CAAC;AAEjD,MAAM,8BAA8B,CAAC;IAC1C,2BAA2B;AAC7B;AAEA,MAAM,6BAA6B,CAAC;IAClC,OAAO,wBAAwB,CAAC,KAAK,IAAI;AAC3C;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK,cAAc;AAChE;AAEO,MAAM,iBAAiB,CAAC,eAAuB;IACpD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,UAAU,YAAY,KAAK,aAAa,OAAO,KAAK,CAAC,MAAM,EAAE,OAAO;IAEzE,OAAO,OAAO,KAAK,CAAC,UAAU;AAChC;AAEO,MAAM,eAAe,CAAC,eAAuB;IAClD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO,CAAC;IAErB,OAAO,OAAO,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,KAAK,KAAK;AACvD;AAEO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,GAAG;AACxC;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,IAAI,EAAE;AACjE;AAEO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAS,yBAAyB;IACxC,OAAO,SAAS,OAAO,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,CAAC,KAAK,QAAQ,IAAI,EAAE;AAClE;AAEO,MAAM,oBAAoB,CAAC,eAAuB;IACvD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,aAAa,OAAO,KAAK,CAAC,MAAM;IACtC,MAAM,YAAY,eAAe,MAAM;IAEvC,OAAO,KAAK,KAAK,CAAC,AAAC,YAAY,aAAc;AAC/C;AAEO,MAAM,cAAc,CAAC,eAAuB;IACjD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,aAAa,eAAe;IACjD,IAAI,iBAAiB,CAAC,KAAK,gBAAgB,OAAO,KAAK,CAAC,MAAM,GAAG,GAAG,OAAO;IAE3E,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC;AAEO,MAAM,kBAAkB,CAAC,eAAuB;IACrD,MAAM,SAAS,yBAAyB;IACxC,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,eAAe,aAAa,eAAe;IACjD,IAAI,gBAAgB,GAAG,OAAO;IAE9B,OAAO,OAAO,KAAK,CAAC,eAAe,EAAE;AACvC", "debugId": null}}, {"offset": {"line": 2607, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/services/applicationProgressService.ts"], "sourcesContent": ["/**\n * Application Progress Service\n * Manages step completion tracking and progress calculation for license applications\n */\n\nimport { getLicenseTypeStepConfig, calculateProgress } from '@/config/licenseTypeStepConfig';\n\nexport interface StepProgress {\n  stepId: string;\n  stepName: string;\n  completed: boolean;\n  completedAt?: Date;\n  data?: any;\n}\n\nexport interface ApplicationProgress {\n  applicationId: string;\n  licenseTypeId: string;\n  totalSteps: number;\n  completedSteps: number;\n  progressPercentage: number;\n  steps: StepProgress[];\n  lastUpdated: Date;\n}\n\nclass ApplicationProgressService {\n  private progressCache = new Map<string, ApplicationProgress>();\n\n  /**\n   * Initialize progress tracking for a new application\n   */\n  async initializeProgress(applicationId: string, licenseTypeId: string): Promise<ApplicationProgress> {\n    const licenseConfig = getLicenseTypeStepConfig(licenseTypeId);\n    if (!licenseConfig) {\n      throw new Error(`Invalid license type: ${licenseTypeId}`);\n    }\n\n    const steps: StepProgress[] = licenseConfig.steps.map(step => ({\n      stepId: step.id,\n      stepName: step.name,\n      completed: false\n    }));\n\n    const progress: ApplicationProgress = {\n      applicationId,\n      licenseTypeId,\n      totalSteps: steps.length,\n      completedSteps: 0,\n      progressPercentage: 0,\n      steps,\n      lastUpdated: new Date()\n    };\n\n    this.progressCache.set(applicationId, progress);\n    await this.saveProgressToStorage(progress);\n    \n    return progress;\n  }\n\n  /**\n   * Mark a step as completed\n   */\n  async markStepCompleted(applicationId: string, stepId: string, data?: any): Promise<ApplicationProgress> {\n    let progress = await this.getProgress(applicationId);\n    \n    if (!progress) {\n      throw new Error(`No progress found for application: ${applicationId}`);\n    }\n\n    // Update the specific step\n    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);\n    if (stepIndex === -1) {\n      throw new Error(`Step not found: ${stepId}`);\n    }\n\n    if (!progress.steps[stepIndex].completed) {\n      progress.steps[stepIndex].completed = true;\n      progress.steps[stepIndex].completedAt = new Date();\n      progress.steps[stepIndex].data = data;\n\n      // Recalculate progress\n      progress.completedSteps = progress.steps.filter(step => step.completed).length;\n      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);\n      progress.lastUpdated = new Date();\n\n      // Update cache and storage\n      this.progressCache.set(applicationId, progress);\n      await this.saveProgressToStorage(progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Mark a step as incomplete (for editing)\n   */\n  async markStepIncomplete(applicationId: string, stepId: string): Promise<ApplicationProgress> {\n    let progress = await this.getProgress(applicationId);\n    \n    if (!progress) {\n      throw new Error(`No progress found for application: ${applicationId}`);\n    }\n\n    // Update the specific step\n    const stepIndex = progress.steps.findIndex(step => step.stepId === stepId);\n    if (stepIndex === -1) {\n      throw new Error(`Step not found: ${stepId}`);\n    }\n\n    if (progress.steps[stepIndex].completed) {\n      progress.steps[stepIndex].completed = false;\n      progress.steps[stepIndex].completedAt = undefined;\n      progress.steps[stepIndex].data = undefined;\n\n      // Recalculate progress\n      progress.completedSteps = progress.steps.filter(step => step.completed).length;\n      progress.progressPercentage = Math.round((progress.completedSteps / progress.totalSteps) * 100);\n      progress.lastUpdated = new Date();\n\n      // Update cache and storage\n      this.progressCache.set(applicationId, progress);\n      await this.saveProgressToStorage(progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Get current progress for an application\n   */\n  async getProgress(applicationId: string): Promise<ApplicationProgress | null> {\n    // Check cache first\n    if (this.progressCache.has(applicationId)) {\n      return this.progressCache.get(applicationId)!;\n    }\n\n    // Load from storage\n    const progress = await this.loadProgressFromStorage(applicationId);\n    if (progress) {\n      this.progressCache.set(applicationId, progress);\n    }\n\n    return progress;\n  }\n\n  /**\n   * Get completed step IDs for an application\n   */\n  async getCompletedStepIds(applicationId: string): Promise<string[]> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return [];\n\n    return progress.steps\n      .filter(step => step.completed)\n      .map(step => step.stepId);\n  }\n\n  /**\n   * Check if a specific step is completed\n   */\n  async isStepCompleted(applicationId: string, stepId: string): Promise<boolean> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return false;\n\n    const step = progress.steps.find(s => s.stepId === stepId);\n    return step?.completed || false;\n  }\n\n  /**\n   * Get next incomplete step\n   */\n  async getNextIncompleteStep(applicationId: string): Promise<StepProgress | null> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return null;\n\n    return progress.steps.find(step => !step.completed) || null;\n  }\n\n  /**\n   * Calculate overall application completion status\n   */\n  async getApplicationStatus(applicationId: string): Promise<'not_started' | 'in_progress' | 'completed'> {\n    const progress = await this.getProgress(applicationId);\n    if (!progress) return 'not_started';\n\n    if (progress.completedSteps === 0) return 'not_started';\n    if (progress.completedSteps === progress.totalSteps) return 'completed';\n    return 'in_progress';\n  }\n\n  /**\n   * Save progress to localStorage (in a real app, this would be an API call)\n   */\n  private async saveProgressToStorage(progress: ApplicationProgress): Promise<void> {\n    try {\n      const key = `application_progress_${progress.applicationId}`;\n      localStorage.setItem(key, JSON.stringify({\n        ...progress,\n        lastUpdated: progress.lastUpdated.toISOString()\n      }));\n    } catch (error) {\n      console.error('Error saving progress to storage:', error);\n    }\n  }\n\n  /**\n   * Load progress from localStorage (in a real app, this would be an API call)\n   */\n  private async loadProgressFromStorage(applicationId: string): Promise<ApplicationProgress | null> {\n    try {\n      const key = `application_progress_${applicationId}`;\n      const stored = localStorage.getItem(key);\n      \n      if (!stored) return null;\n\n      const parsed = JSON.parse(stored);\n      return {\n        ...parsed,\n        lastUpdated: new Date(parsed.lastUpdated),\n        steps: parsed.steps.map((step: any) => ({\n          ...step,\n          completedAt: step.completedAt ? new Date(step.completedAt) : undefined\n        }))\n      };\n    } catch (error) {\n      console.error('Error loading progress from storage:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Clear progress cache (useful for testing or when switching applications)\n   */\n  clearCache(): void {\n    this.progressCache.clear();\n  }\n\n  /**\n   * Delete progress for an application\n   */\n  async deleteProgress(applicationId: string): Promise<void> {\n    this.progressCache.delete(applicationId);\n    \n    try {\n      const key = `application_progress_${applicationId}`;\n      localStorage.removeItem(key);\n    } catch (error) {\n      console.error('Error deleting progress from storage:', error);\n    }\n  }\n}\n\n// Export singleton instance\nexport const applicationProgressService = new ApplicationProgressService();\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AAED;;AAoBA,MAAM;IACI,gBAAgB,IAAI,MAAmC;IAE/D;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAE,aAAqB,EAAgC;QACnG,MAAM,gBAAgB,CAAA,GAAA,sIAAA,CAAA,2BAAwB,AAAD,EAAE;QAC/C,IAAI,CAAC,eAAe;YAClB,MAAM,IAAI,MAAM,CAAC,sBAAsB,EAAE,eAAe;QAC1D;QAEA,MAAM,QAAwB,cAAc,KAAK,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;gBAC7D,QAAQ,KAAK,EAAE;gBACf,UAAU,KAAK,IAAI;gBACnB,WAAW;YACb,CAAC;QAED,MAAM,WAAgC;YACpC;YACA;YACA,YAAY,MAAM,MAAM;YACxB,gBAAgB;YAChB,oBAAoB;YACpB;YACA,aAAa,IAAI;QACnB;QAEA,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;QACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QAEjC,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,kBAAkB,aAAqB,EAAE,MAAc,EAAE,IAAU,EAAgC;QACvG,IAAI,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,eAAe;QACvE;QAEA,2BAA2B;QAC3B,MAAM,YAAY,SAAS,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACnE,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;QAC7C;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE;YACxC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG;YACtC,SAAS,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG,IAAI;YAC5C,SAAS,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG;YAEjC,uBAAuB;YACvB,SAAS,cAAc,GAAG,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC9E,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI;YAC3F,SAAS,WAAW,GAAG,IAAI;YAE3B,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;YACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,mBAAmB,aAAqB,EAAE,MAAc,EAAgC;QAC5F,IAAI,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QAEtC,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM,CAAC,mCAAmC,EAAE,eAAe;QACvE;QAEA,2BAA2B;QAC3B,MAAM,YAAY,SAAS,KAAK,CAAC,SAAS,CAAC,CAAA,OAAQ,KAAK,MAAM,KAAK;QACnE,IAAI,cAAc,CAAC,GAAG;YACpB,MAAM,IAAI,MAAM,CAAC,gBAAgB,EAAE,QAAQ;QAC7C;QAEA,IAAI,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,EAAE;YACvC,SAAS,KAAK,CAAC,UAAU,CAAC,SAAS,GAAG;YACtC,SAAS,KAAK,CAAC,UAAU,CAAC,WAAW,GAAG;YACxC,SAAS,KAAK,CAAC,UAAU,CAAC,IAAI,GAAG;YAEjC,uBAAuB;YACvB,SAAS,cAAc,GAAG,SAAS,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAAE,MAAM;YAC9E,SAAS,kBAAkB,GAAG,KAAK,KAAK,CAAC,AAAC,SAAS,cAAc,GAAG,SAAS,UAAU,GAAI;YAC3F,SAAS,WAAW,GAAG,IAAI;YAE3B,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;YACtC,MAAM,IAAI,CAAC,qBAAqB,CAAC;QACnC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,YAAY,aAAqB,EAAuC;QAC5E,oBAAoB;QACpB,IAAI,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,gBAAgB;YACzC,OAAO,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAChC;QAEA,oBAAoB;QACpB,MAAM,WAAW,MAAM,IAAI,CAAC,uBAAuB,CAAC;QACpD,IAAI,UAAU;YACZ,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,eAAe;QACxC;QAEA,OAAO;IACT;IAEA;;GAEC,GACD,MAAM,oBAAoB,aAAqB,EAAqB;QAClE,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO,EAAE;QAExB,OAAO,SAAS,KAAK,CAClB,MAAM,CAAC,CAAA,OAAQ,KAAK,SAAS,EAC7B,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM;IAC5B;IAEA;;GAEC,GACD,MAAM,gBAAgB,aAAqB,EAAE,MAAc,EAAoB;QAC7E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,MAAM,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK;QACnD,OAAO,MAAM,aAAa;IAC5B;IAEA;;GAEC,GACD,MAAM,sBAAsB,aAAqB,EAAgC;QAC/E,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,OAAO,SAAS,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,CAAC,KAAK,SAAS,KAAK;IACzD;IAEA;;GAEC,GACD,MAAM,qBAAqB,aAAqB,EAAwD;QACtG,MAAM,WAAW,MAAM,IAAI,CAAC,WAAW,CAAC;QACxC,IAAI,CAAC,UAAU,OAAO;QAEtB,IAAI,SAAS,cAAc,KAAK,GAAG,OAAO;QAC1C,IAAI,SAAS,cAAc,KAAK,SAAS,UAAU,EAAE,OAAO;QAC5D,OAAO;IACT;IAEA;;GAEC,GACD,MAAc,sBAAsB,QAA6B,EAAiB;QAChF,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,SAAS,aAAa,EAAE;YAC5D,aAAa,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;gBACvC,GAAG,QAAQ;gBACX,aAAa,SAAS,WAAW,CAAC,WAAW;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD;IACF;IAEA;;GAEC,GACD,MAAc,wBAAwB,aAAqB,EAAuC;QAChG,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,eAAe;YACnD,MAAM,SAAS,aAAa,OAAO,CAAC;YAEpC,IAAI,CAAC,QAAQ,OAAO;YAEpB,MAAM,SAAS,KAAK,KAAK,CAAC;YAC1B,OAAO;gBACL,GAAG,MAAM;gBACT,aAAa,IAAI,KAAK,OAAO,WAAW;gBACxC,OAAO,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,OAAc,CAAC;wBACtC,GAAG,IAAI;wBACP,aAAa,KAAK,WAAW,GAAG,IAAI,KAAK,KAAK,WAAW,IAAI;oBAC/D,CAAC;YACH;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wCAAwC;YACtD,OAAO;QACT;IACF;IAEA;;GAEC,GACD,aAAmB;QACjB,IAAI,CAAC,aAAa,CAAC,KAAK;IAC1B;IAEA;;GAEC,GACD,MAAM,eAAe,aAAqB,EAAiB;QACzD,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC;QAE1B,IAAI;YACF,MAAM,MAAM,CAAC,qBAAqB,EAAE,eAAe;YACnD,aAAa,UAAU,CAAC;QAC1B,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;QACzD;IACF;AACF;AAGO,MAAM,6BAA6B,IAAI", "debugId": null}}, {"offset": {"line": 2797, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/ApplicantInfo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport TextInput from '@/components/common/TextInput';\nimport Select from '@/components/common/Select';\nimport { validateSection } from '@/utils/formValidation';\nimport { applicationFormDataService } from '@/services/applicationFormDataService';\nimport { applicationService } from '@/services/applicationService';\nimport { applicantService } from '@/services/applicantService';\nimport { applicationProgressService } from '@/services/applicationProgressService';\nimport { IndependentStepProps } from '../types';\n\ninterface ApplicantInfoProps extends IndependentStepProps {}\n\nconst ApplicantInfo: React.FC<ApplicantInfoProps> = ({\n  applicationId,\n  licenseTypeId,\n  licenseCategoryId,\n  isEditMode = false,\n  onNext,\n  onPrevious,\n  isFirstStep = true,\n  isLastStep = false,\n  onStepComplete,\n  onStepError,\n  onNavigate\n}) => {\n\n  // Debug logging for props\n  console.log('ApplicantInfo component props:', {\n    applicationId,\n    licenseTypeId,\n    licenseCategoryId,\n    isEditMode\n  });\n\n  const [localData, setLocalData] = useState({\n    applicant_type: '',\n    first_name: '',\n    last_name: '',\n    middle_name: '',\n    email: '',\n    phone: '',\n    national_id: '',\n    date_of_birth: '',\n    nationality: 'Malawian',\n    gender: '',\n    postal_address: '',\n    physical_address: '',\n    city: '',\n    district: '',\n    postal_code: ''\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n  const [applicationCreated, setApplicationCreated] = useState(false);\n  const [createdApplicationId, setCreatedApplicationId] = useState<string | null>(null);\n\n  // Load existing data when component mounts (for edit mode)\n  useEffect(() => {\n    const loadExistingData = async () => {\n      // Add more robust validation for applicationId\n      if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {\n        console.log('Loading existing data for application:', applicationId);\n        setIsLoading(true);\n        try {\n          // Load data from multiple sources since ApplicantInfo saves to different places\n          const dataPromises = [];\n\n          // 1. Load form data (individual person fields)\n          dataPromises.push(\n            applicationFormDataService.getFormSection(applicationId, 'applicantInfo')\n              .then(data => ({ source: 'formData', data }))\n              .catch(error => {\n                console.warn('Could not load form data:', error);\n                return { source: 'formData', data: null };\n              })\n          );\n\n          // 2. Load application data (includes applicant relation)\n          dataPromises.push(\n            applicationService.getApplication(applicationId)\n              .then(data => ({ source: 'application', data }))\n              .catch(error => {\n                console.warn('Could not load application data:', error);\n                return { source: 'application', data: null };\n              })\n          );\n\n          const results = await Promise.all(dataPromises);\n\n          // Merge data from different sources\n          let mergedData = { ...localData };\n\n          for (const result of results) {\n            if (result.data) {\n              if (result.source === 'formData') {\n                // Type guard: check if it's form data with section_data\n                const formData = result.data as any;\n                if (formData && formData.section_data) {\n                  console.log('Loaded form data:', formData.section_data);\n                  mergedData = { ...mergedData, ...formData.section_data };\n                }\n              } else if (result.source === 'application') {\n                // Type guard: check if it's application data with applicant\n                const applicationData = result.data as any;\n                if (applicationData && applicationData.applicant) {\n                  const applicant = applicationData.applicant;\n                  console.log('Loaded application with applicant:', applicant);\n\n                  // Map applicant business data to form fields where applicable\n                  if (applicant.name && !mergedData.first_name && !mergedData.last_name) {\n                    // If no individual names, try to split business name\n                    const nameParts = applicant.name.split(' ');\n                    if (nameParts.length >= 2) {\n                      mergedData.first_name = nameParts[0];\n                      mergedData.last_name = nameParts.slice(1).join(' ');\n                    } else {\n                      mergedData.first_name = applicant.name;\n                    }\n                  }\n\n                  if (applicant.email && !mergedData.email) {\n                    mergedData.email = applicant.email;\n                  }\n\n                  if (applicant.phone && !mergedData.phone) {\n                    mergedData.phone = applicant.phone;\n                  }\n\n                  if (applicant.place_incorporation && !mergedData.city) {\n                    mergedData.city = applicant.place_incorporation;\n                  }\n                }\n              }\n            }\n          }\n\n          console.log('Merged applicant data from all sources:', mergedData);\n          setLocalData(mergedData);\n\n        } catch (error) {\n          console.error('Error loading existing applicant data:', error);\n          // Don't call onStepError as it would hide the form\n          // Just log the error and continue with empty form\n          console.log('Continuing with empty form due to load error');\n        } finally {\n          setIsLoading(false);\n        }\n      } else {\n        console.log('Skipping data load - not in edit mode or invalid applicationId:', { isEditMode, applicationId });\n      }\n    };\n\n    loadExistingData();\n  }, [applicationId, isEditMode]);\n\n  // Handle local changes\n  const handleLocalChange = useCallback((field: string, value: any) => {\n    setLocalData((prev: any) => ({ ...prev, [field]: value }));\n    setHasUnsavedChanges(true);\n\n    // Clear validation error for this field if it exists\n    if (validationErrors && validationErrors[field]) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));\n    }\n\n    // Clear save error when user starts making changes\n    if (validationErrors.save) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, save: '' }));\n    }\n  }, [validationErrors]);\n\n  // Validate form data\n  const validateForm = () => {\n    const validation = validateSection(localData, 'applicantInfo');\n    setValidationErrors(validation.errors);\n    return validation.isValid;\n  };\n\n  // Save data to backend\n  const saveData = async () => {\n    const validation = validateSection(localData, 'applicantInfo');\n    setValidationErrors(validation.errors);\n\n    if (!validation.isValid) {\n      // Don't call onStepError as it hides the form\n      // Instead, let the form show validation errors inline\n      console.log('Validation failed:', validation.errors);\n      return false;\n    }\n\n    setIsSaving(true);\n    try {\n      if (isEditMode && applicationId && applicationId !== 'new') {\n        // Update existing application data\n        await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'applicantInfo', localData);\n\n        // Mark step as completed in progress tracking\n        await applicationProgressService.markStepCompleted(applicationId, 'applicant-info', localData);\n\n        console.log('Applicant info updated for application:', applicationId);\n      } else {\n        // Create new applicant and application\n        // Map individual person data to business applicant structure\n        const applicantPayload = {\n          name: `${localData.first_name} ${localData.last_name}`.trim(),\n          email: localData.email,\n          phone: localData.phone,\n          business_registration_number: `BRN-${Date.now()}`,\n          tpin: `TPIN-${Date.now()}`,\n          website: 'https://example.com',\n          date_incorporation: new Date().toISOString(),\n          place_incorporation: localData.city || 'Malawi'\n        };\n\n        const createdApplicant = await applicantService.createApplicant(applicantPayload);\n\n\n\n        console.log('Created applicant response:', createdApplicant);\n        console.log('Applicant ID from response:', createdApplicant?.applicant_id);\n\n        // Validate that we got a valid applicant ID\n        if (!createdApplicant || !createdApplicant.applicant_id) {\n          throw new Error('Failed to create applicant: No applicant ID returned');\n        }\n\n        const applicationPayload = {\n          application_number: `APP-${Date.now()}`,\n          applicant_id: createdApplicant.applicant_id,\n          license_category_id: licenseCategoryId,\n          status: 'draft' as any\n        };\n\n        console.log('Creating application with payload:', applicationPayload);\n        const createdApplication = await applicationService.createApplication(applicationPayload);\n\n        console.log('Created application response:', createdApplication);\n        console.log('Application ID from response:', createdApplication?.application_id);\n\n        // Validate that we got a valid application ID\n        if (!createdApplication || !createdApplication.application_id) {\n          throw new Error('Failed to create application: No application ID returned');\n        }\n\n        const newApplicationId = createdApplication.application_id;\n        console.log('Using application ID:', newApplicationId);\n\n        // Save form data\n        await applicationFormDataService.saveOrUpdateFormSection(\n          newApplicationId,\n          'applicantInfo',\n          localData\n        );\n\n        console.log('New application created:', newApplicationId);\n\n        // Initialize progress tracking for the new application\n        if (licenseTypeId) {\n          await applicationProgressService.initializeProgress(newApplicationId, licenseTypeId);\n        }\n\n        // Mark this step as completed\n        await applicationProgressService.markStepCompleted(newApplicationId, 'applicant-info', localData);\n\n        console.log('Applicant info step completed, passing application ID to parent:', newApplicationId);\n\n        // Set application created state\n        setApplicationCreated(true);\n        setCreatedApplicationId(newApplicationId);\n\n        // Pass the application ID to the parent - let the parent handle navigation\n        onStepComplete?.('applicant-info', { ...localData, applicationId: newApplicationId });\n\n        // Don't navigate here - show continue button instead\n        return true;\n      }\n\n      setHasUnsavedChanges(false);\n      onStepComplete?.('applicant-info', localData);\n      return true;\n    } catch (error: any) {\n      console.error('Error saving applicant info:', error);\n      console.error('Error details:', {\n        message: error.message,\n        response: error.response?.data,\n        status: error.response?.status\n      });\n\n      // Extract meaningful error message\n      let errorMessage = 'Failed to save applicant information';\n      if (error.message && error.message.includes('Invalid applicationId')) {\n        errorMessage = 'Error creating application record. Please try again.';\n      } else if (error.response?.data?.message) {\n        errorMessage = error.response.data.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      // Don't call onStepError as it hides the form - instead show inline error\n      // onStepError?.('applicant-info', { save: errorMessage });\n\n      // Show error in the form itself\n      setValidationErrors({ save: errorMessage });\n      return false;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Handle save button click\n  const handleSave = async () => {\n    await saveData();\n  };\n\n  // Handle continue to next step\n  const handleContinueToNextStep = () => {\n    console.log('Continue to next step clicked');\n    console.log('Created application ID:', createdApplicationId);\n    console.log('License category ID:', licenseCategoryId);\n\n    if (createdApplicationId && onNext) {\n      // Update the URL with the application ID and move to next step\n      const params = new URLSearchParams(window.location.search);\n      params.set('application_id', createdApplicationId);\n      const newUrl = `/customer/applications/apply?${params.toString()}`;\n      window.history.replaceState({}, '', newUrl);\n\n      // Call the onNext callback to move to the next step\n      onNext();\n    } else if (onNext) {\n      // If no application ID yet, just move to next step\n      onNext();\n    } else {\n      console.error('No navigation callback available');\n    }\n  };\n\n\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Applicant Information\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Please provide your personal information. This will create your application record.\n        </p>\n        {!applicationId && (\n          <div className=\"mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg\">\n            <p className=\"text-sm text-blue-700 dark:text-blue-300\">\n              <i className=\"ri-information-line mr-1\"></i>\n              Your application will be created when you save this step.\n            </p>\n          </div>\n        )}\n        {applicationId && (\n          <div className=\"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n            <p className=\"text-sm text-green-700 dark:text-green-300\">\n              <i className=\"ri-check-line mr-1\"></i>\n              Application created: {applicationId.slice(0, 8)}...\n            </p>\n          </div>\n        )}\n      </div>\n\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        {/* Applicant Type */}\n        <div className=\"md:col-span-2\">\n          <Select\n            label=\"Applicant Type\"\n            value={localData.applicant_type || ''}\n            onChange={(value) => handleLocalChange('applicant_type', value)}\n            options={[\n              { value: 'individual', label: 'Individual' },\n              { value: 'company', label: 'Company' },\n              { value: 'organization', label: 'Organization' }\n            ]}\n            required\n            error={validationErrors.applicant_type}\n          />\n        </div>\n\n        {/* Personal Information */}\n        <TextInput\n          label=\"First Name\"\n          value={localData.first_name || ''}\n          onChange={(e) => handleLocalChange('first_name', e.target.value)}\n          required\n          error={validationErrors.first_name}\n        />\n\n        <TextInput\n          label=\"Last Name\"\n          value={localData.last_name || ''}\n          onChange={(e) => handleLocalChange('last_name', e.target.value)}\n          required\n          error={validationErrors.last_name}\n        />\n\n        <TextInput\n          label=\"Middle Name\"\n          value={localData.middle_name || ''}\n          onChange={(e) => handleLocalChange('middle_name', e.target.value)}\n          error={validationErrors.middle_name}\n        />\n\n        <Select\n          label=\"Gender\"\n          value={localData.gender || ''}\n          onChange={(value) => handleLocalChange('gender', value)}\n          options={[\n            { value: 'male', label: 'Male' },\n            { value: 'female', label: 'Female' },\n            { value: 'other', label: 'Other' }\n          ]}\n          required\n          error={validationErrors.gender}\n        />\n\n        {/* Contact Information */}\n        <TextInput\n          label=\"Email Address\"\n          type=\"email\"\n          value={localData.email || ''}\n          onChange={(e) => handleLocalChange('email', e.target.value)}\n          required\n          error={validationErrors.email}\n        />\n\n        <TextInput\n          label=\"Phone Number\"\n          value={localData.phone || ''}\n          onChange={(e) => handleLocalChange('phone', e.target.value)}\n          placeholder=\"+265 1 234 567\"\n          required\n          error={validationErrors.phone}\n        />\n\n        {/* Identification */}\n        <TextInput\n          label=\"National ID\"\n          value={localData.national_id || ''}\n          onChange={(e) => handleLocalChange('national_id', e.target.value)}\n          placeholder=\"**********\"\n          required\n          error={validationErrors.national_id}\n        />\n\n        <TextInput\n          label=\"Date of Birth\"\n          type=\"date\"\n          value={localData.date_of_birth || ''}\n          onChange={(e) => handleLocalChange('date_of_birth', e.target.value)}\n          required\n          error={validationErrors.date_of_birth}\n        />\n\n        <Select\n          label=\"Nationality\"\n          value={localData.nationality || 'Malawian'}\n          onChange={(value) => handleLocalChange('nationality', value)}\n          options={[\n            { value: 'Malawian', label: 'Malawian' },\n            { value: 'Other', label: 'Other' }\n          ]}\n          required\n          error={validationErrors.nationality}\n        />\n      </div>\n\n      {/* Address Information */}\n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n        <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          Address Information\n        </h4>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div className=\"md:col-span-2\">\n            <TextInput\n              label=\"Postal Address\"\n              value={localData.postal_address || ''}\n              onChange={(e) => handleLocalChange('postal_address', e.target.value)}\n              placeholder=\"P.O. Box 123\"\n              required\n              error={validationErrors.postal_address}\n            />\n          </div>\n\n          <div className=\"md:col-span-2\">\n            <TextInput\n              label=\"Physical Address\"\n              value={localData.physical_address || ''}\n              onChange={(e) => handleLocalChange('physical_address', e.target.value)}\n              placeholder=\"Street address\"\n              required\n              error={validationErrors.physical_address}\n            />\n          </div>\n\n          <TextInput\n            label=\"City\"\n            value={localData.city || ''}\n            onChange={(e) => handleLocalChange('city', e.target.value)}\n            required\n            error={validationErrors.city}\n          />\n\n          <Select\n            label=\"District\"\n            value={localData.district || ''}\n            onChange={(value) => handleLocalChange('district', value)}\n            options={[\n              { value: 'Blantyre', label: 'Blantyre' },\n              { value: 'Lilongwe', label: 'Lilongwe' },\n              { value: 'Mzuzu', label: 'Mzuzu' },\n              { value: 'Zomba', label: 'Zomba' },\n              { value: 'Other', label: 'Other' }\n            ]}\n            required\n            error={validationErrors.district}\n          />\n\n          <TextInput\n            label=\"Postal Code\"\n            value={localData.postal_code || ''}\n            onChange={(e) => handleLocalChange('postal_code', e.target.value)}\n            error={validationErrors.postal_code}\n          />\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {validationErrors.save && (\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3\"></i>\n            <div>\n              <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                Error Saving Application\n              </h3>\n              <p className=\"text-red-700 dark:text-red-300 text-sm mt-1\">\n                {validationErrors.save}\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Success Message */}\n      {applicationCreated && !isEditMode && (\n        <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <i className=\"ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3\"></i>\n            <div>\n              <h3 className=\"text-sm font-medium text-green-800 dark:text-green-200\">\n                Application Created Successfully!\n              </h3>\n              <p className=\"text-green-700 dark:text-green-300 text-sm mt-1\">\n                Your application has been created. You can now continue to the next step.\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Action Buttons */}\n      <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\n        {applicationCreated && !isEditMode ? (\n          <>\n            {/* Save Changes Button (for edit mode) */}\n            <button\n              onClick={handleSave}\n              disabled={isSaving || isLoading}\n              className=\"inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSaving || isLoading ? (\n                <>\n                  <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n                  Saving...\n                </>\n              ) : (\n                <>\n                  <i className=\"ri-save-line mr-2\"></i>\n                  Save Changes\n                </>\n              )}\n            </button>\n\n            {/* Continue to Next Step Button */}\n            <button\n              onClick={handleContinueToNextStep}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              <i className=\"ri-arrow-right-line mr-2\"></i>\n              Continue to Company Profile\n            </button>\n          </>\n        ) : (\n          /* Create/Save Application Button */\n          <button\n            onClick={handleSave}\n            disabled={isSaving || isLoading}\n            className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isSaving || isLoading ? (\n              <>\n                <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n                {applicationId ? 'Saving...' : 'Creating Application...'}\n              </>\n            ) : (\n              <>\n                <i className=\"ri-save-line mr-2\"></i>\n                {applicationId ? 'Save Changes' : 'Create Application'}\n              </>\n            )}\n          </button>\n        )}\n      </div>\n    </div>\n  );\n};\n\nexport default ApplicantInfo;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAcA,MAAM,gBAA8C,CAAC,EACnD,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,aAAa,KAAK,EAClB,MAAM,EACN,UAAU,EACV,cAAc,IAAI,EAClB,aAAa,KAAK,EAClB,cAAc,EACd,WAAW,EACX,UAAU,EACX;IAEC,0BAA0B;IAC1B,QAAQ,GAAG,CAAC,kCAAkC;QAC5C;QACA;QACA;QACA;IACF;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,gBAAgB;QAChB,YAAY;QACZ,WAAW;QACX,aAAa;QACb,OAAO;QACP,OAAO;QACP,aAAa;QACb,eAAe;QACf,aAAa;QACb,QAAQ;QACR,gBAAgB;QAChB,kBAAkB;QAClB,MAAM;QACN,UAAU;QACV,aAAa;IACf;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAEhF,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,+CAA+C;YAC/C,IAAI,cAAc,iBAAiB,kBAAkB,SAAS,kBAAkB,eAAe,cAAc,IAAI,OAAO,IAAI;gBAC1H,QAAQ,GAAG,CAAC,0CAA0C;gBACtD,aAAa;gBACb,IAAI;oBACF,gFAAgF;oBAChF,MAAM,eAAe,EAAE;oBAEvB,+CAA+C;oBAC/C,aAAa,IAAI,CACf,6IAAA,CAAA,6BAA0B,CAAC,cAAc,CAAC,eAAe,iBACtD,IAAI,CAAC,CAAA,OAAQ,CAAC;4BAAE,QAAQ;4BAAY;wBAAK,CAAC,GAC1C,KAAK,CAAC,CAAA;wBACL,QAAQ,IAAI,CAAC,6BAA6B;wBAC1C,OAAO;4BAAE,QAAQ;4BAAY,MAAM;wBAAK;oBAC1C;oBAGJ,yDAAyD;oBACzD,aAAa,IAAI,CACf,qIAAA,CAAA,qBAAkB,CAAC,cAAc,CAAC,eAC/B,IAAI,CAAC,CAAA,OAAQ,CAAC;4BAAE,QAAQ;4BAAe;wBAAK,CAAC,GAC7C,KAAK,CAAC,CAAA;wBACL,QAAQ,IAAI,CAAC,oCAAoC;wBACjD,OAAO;4BAAE,QAAQ;4BAAe,MAAM;wBAAK;oBAC7C;oBAGJ,MAAM,UAAU,MAAM,QAAQ,GAAG,CAAC;oBAElC,oCAAoC;oBACpC,IAAI,aAAa;wBAAE,GAAG,SAAS;oBAAC;oBAEhC,KAAK,MAAM,UAAU,QAAS;wBAC5B,IAAI,OAAO,IAAI,EAAE;4BACf,IAAI,OAAO,MAAM,KAAK,YAAY;gCAChC,wDAAwD;gCACxD,MAAM,WAAW,OAAO,IAAI;gCAC5B,IAAI,YAAY,SAAS,YAAY,EAAE;oCACrC,QAAQ,GAAG,CAAC,qBAAqB,SAAS,YAAY;oCACtD,aAAa;wCAAE,GAAG,UAAU;wCAAE,GAAG,SAAS,YAAY;oCAAC;gCACzD;4BACF,OAAO,IAAI,OAAO,MAAM,KAAK,eAAe;gCAC1C,4DAA4D;gCAC5D,MAAM,kBAAkB,OAAO,IAAI;gCACnC,IAAI,mBAAmB,gBAAgB,SAAS,EAAE;oCAChD,MAAM,YAAY,gBAAgB,SAAS;oCAC3C,QAAQ,GAAG,CAAC,sCAAsC;oCAElD,8DAA8D;oCAC9D,IAAI,UAAU,IAAI,IAAI,CAAC,WAAW,UAAU,IAAI,CAAC,WAAW,SAAS,EAAE;wCACrE,qDAAqD;wCACrD,MAAM,YAAY,UAAU,IAAI,CAAC,KAAK,CAAC;wCACvC,IAAI,UAAU,MAAM,IAAI,GAAG;4CACzB,WAAW,UAAU,GAAG,SAAS,CAAC,EAAE;4CACpC,WAAW,SAAS,GAAG,UAAU,KAAK,CAAC,GAAG,IAAI,CAAC;wCACjD,OAAO;4CACL,WAAW,UAAU,GAAG,UAAU,IAAI;wCACxC;oCACF;oCAEA,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,KAAK,EAAE;wCACxC,WAAW,KAAK,GAAG,UAAU,KAAK;oCACpC;oCAEA,IAAI,UAAU,KAAK,IAAI,CAAC,WAAW,KAAK,EAAE;wCACxC,WAAW,KAAK,GAAG,UAAU,KAAK;oCACpC;oCAEA,IAAI,UAAU,mBAAmB,IAAI,CAAC,WAAW,IAAI,EAAE;wCACrD,WAAW,IAAI,GAAG,UAAU,mBAAmB;oCACjD;gCACF;4BACF;wBACF;oBACF;oBAEA,QAAQ,GAAG,CAAC,2CAA2C;oBACvD,aAAa;gBAEf,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,0CAA0C;oBACxD,mDAAmD;oBACnD,kDAAkD;oBAClD,QAAQ,GAAG,CAAC;gBACd,SAAU;oBACR,aAAa;gBACf;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,mEAAmE;oBAAE;oBAAY;gBAAc;YAC7G;QACF;QAEA;IACF,GAAG;QAAC;QAAe;KAAW;IAE9B,uBAAuB;IACvB,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QACpD,aAAa,CAAC,OAAc,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACxD,qBAAqB;QAErB,qDAAqD;QACrD,IAAI,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;YAC/C,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACjF;QAEA,mDAAmD;QACnD,IAAI,iBAAiB,IAAI,EAAE;YACzB,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAG,CAAC;QAC9E;IACF,GAAG;QAAC;KAAiB;IAErB,qBAAqB;IACrB,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,oBAAoB,WAAW,MAAM;QACrC,OAAO,WAAW,OAAO;IAC3B;IAEA,uBAAuB;IACvB,MAAM,WAAW;QACf,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,oBAAoB,WAAW,MAAM;QAErC,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,8CAA8C;YAC9C,sDAAsD;YACtD,QAAQ,GAAG,CAAC,sBAAsB,WAAW,MAAM;YACnD,OAAO;QACT;QAEA,YAAY;QACZ,IAAI;YACF,IAAI,cAAc,iBAAiB,kBAAkB,OAAO;gBAC1D,mCAAmC;gBACnC,MAAM,6IAAA,CAAA,6BAA0B,CAAC,uBAAuB,CAAC,eAAe,iBAAiB;gBAEzF,8CAA8C;gBAC9C,MAAM,6IAAA,CAAA,6BAA0B,CAAC,iBAAiB,CAAC,eAAe,kBAAkB;gBAEpF,QAAQ,GAAG,CAAC,2CAA2C;YACzD,OAAO;gBACL,uCAAuC;gBACvC,6DAA6D;gBAC7D,MAAM,mBAAmB;oBACvB,MAAM,GAAG,UAAU,UAAU,CAAC,CAAC,EAAE,UAAU,SAAS,EAAE,CAAC,IAAI;oBAC3D,OAAO,UAAU,KAAK;oBACtB,OAAO,UAAU,KAAK;oBACtB,8BAA8B,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;oBACjD,MAAM,CAAC,KAAK,EAAE,KAAK,GAAG,IAAI;oBAC1B,SAAS;oBACT,oBAAoB,IAAI,OAAO,WAAW;oBAC1C,qBAAqB,UAAU,IAAI,IAAI;gBACzC;gBAEA,MAAM,mBAAmB,MAAM,mIAAA,CAAA,mBAAgB,CAAC,eAAe,CAAC;gBAIhE,QAAQ,GAAG,CAAC,+BAA+B;gBAC3C,QAAQ,GAAG,CAAC,+BAA+B,kBAAkB;gBAE7D,4CAA4C;gBAC5C,IAAI,CAAC,oBAAoB,CAAC,iBAAiB,YAAY,EAAE;oBACvD,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,qBAAqB;oBACzB,oBAAoB,CAAC,IAAI,EAAE,KAAK,GAAG,IAAI;oBACvC,cAAc,iBAAiB,YAAY;oBAC3C,qBAAqB;oBACrB,QAAQ;gBACV;gBAEA,QAAQ,GAAG,CAAC,sCAAsC;gBAClD,MAAM,qBAAqB,MAAM,qIAAA,CAAA,qBAAkB,CAAC,iBAAiB,CAAC;gBAEtE,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,QAAQ,GAAG,CAAC,iCAAiC,oBAAoB;gBAEjE,8CAA8C;gBAC9C,IAAI,CAAC,sBAAsB,CAAC,mBAAmB,cAAc,EAAE;oBAC7D,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,mBAAmB,mBAAmB,cAAc;gBAC1D,QAAQ,GAAG,CAAC,yBAAyB;gBAErC,iBAAiB;gBACjB,MAAM,6IAAA,CAAA,6BAA0B,CAAC,uBAAuB,CACtD,kBACA,iBACA;gBAGF,QAAQ,GAAG,CAAC,4BAA4B;gBAExC,uDAAuD;gBACvD,IAAI,eAAe;oBACjB,MAAM,6IAAA,CAAA,6BAA0B,CAAC,kBAAkB,CAAC,kBAAkB;gBACxE;gBAEA,8BAA8B;gBAC9B,MAAM,6IAAA,CAAA,6BAA0B,CAAC,iBAAiB,CAAC,kBAAkB,kBAAkB;gBAEvF,QAAQ,GAAG,CAAC,oEAAoE;gBAEhF,gCAAgC;gBAChC,sBAAsB;gBACtB,wBAAwB;gBAExB,2EAA2E;gBAC3E,iBAAiB,kBAAkB;oBAAE,GAAG,SAAS;oBAAE,eAAe;gBAAiB;gBAEnF,qDAAqD;gBACrD,OAAO;YACT;YAEA,qBAAqB;YACrB,iBAAiB,kBAAkB;YACnC,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,QAAQ,KAAK,CAAC,kBAAkB;gBAC9B,SAAS,MAAM,OAAO;gBACtB,UAAU,MAAM,QAAQ,EAAE;gBAC1B,QAAQ,MAAM,QAAQ,EAAE;YAC1B;YAEA,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,MAAM,OAAO,IAAI,MAAM,OAAO,CAAC,QAAQ,CAAC,0BAA0B;gBACpE,eAAe;YACjB,OAAO,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACxC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC5C,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,eAAe,MAAM,OAAO;YAC9B;YAEA,0EAA0E;YAC1E,2DAA2D;YAE3D,gCAAgC;YAChC,oBAAoB;gBAAE,MAAM;YAAa;YACzC,OAAO;QACT,SAAU;YACR,YAAY;QACd;IACF;IAEA,2BAA2B;IAC3B,MAAM,aAAa;QACjB,MAAM;IACR;IAEA,+BAA+B;IAC/B,MAAM,2BAA2B;QAC/B,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,2BAA2B;QACvC,QAAQ,GAAG,CAAC,wBAAwB;QAEpC,IAAI,wBAAwB,QAAQ;YAClC,+DAA+D;YAC/D,MAAM,SAAS,IAAI,gBAAgB,OAAO,QAAQ,CAAC,MAAM;YACzD,OAAO,GAAG,CAAC,kBAAkB;YAC7B,MAAM,SAAS,CAAC,6BAA6B,EAAE,OAAO,QAAQ,IAAI;YAClE,OAAO,OAAO,CAAC,YAAY,CAAC,CAAC,GAAG,IAAI;YAEpC,oDAAoD;YACpD;QACF,OAAO,IAAI,QAAQ;YACjB,mDAAmD;YACnD;QACF,OAAO;YACL,QAAQ,KAAK,CAAC;QAChB;IACF;IAIA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;oBAG5D,CAAC,+BACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;8CACX,8OAAC;oCAAE,WAAU;;;;;;gCAA+B;;;;;;;;;;;;oBAKjD,+BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;8CACX,8OAAC;oCAAE,WAAU;;;;;;gCAAyB;gCAChB,cAAc,KAAK,CAAC,GAAG;gCAAG;;;;;;;;;;;;;;;;;;0BAMxD,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sIAAA,CAAA,UAAM;4BACL,OAAM;4BACN,OAAO,UAAU,cAAc,IAAI;4BACnC,UAAU,CAAC,QAAU,kBAAkB,kBAAkB;4BACzD,SAAS;gCACP;oCAAE,OAAO;oCAAc,OAAO;gCAAa;gCAC3C;oCAAE,OAAO;oCAAW,OAAO;gCAAU;gCACrC;oCAAE,OAAO;oCAAgB,OAAO;gCAAe;6BAChD;4BACD,QAAQ;4BACR,OAAO,iBAAiB,cAAc;;;;;;;;;;;kCAK1C,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,UAAU,IAAI;wBAC/B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC/D,QAAQ;wBACR,OAAO,iBAAiB,UAAU;;;;;;kCAGpC,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,SAAS,IAAI;wBAC9B,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC9D,QAAQ;wBACR,OAAO,iBAAiB,SAAS;;;;;;kCAGnC,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,WAAW,IAAI;wBAChC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wBAChE,OAAO,iBAAiB,WAAW;;;;;;kCAGrC,8OAAC,sIAAA,CAAA,UAAM;wBACL,OAAM;wBACN,OAAO,UAAU,MAAM,IAAI;wBAC3B,UAAU,CAAC,QAAU,kBAAkB,UAAU;wBACjD,SAAS;4BACP;gCAAE,OAAO;gCAAQ,OAAO;4BAAO;4BAC/B;gCAAE,OAAO;gCAAU,OAAO;4BAAS;4BACnC;gCAAE,OAAO;gCAAS,OAAO;4BAAQ;yBAClC;wBACD,QAAQ;wBACR,OAAO,iBAAiB,MAAM;;;;;;kCAIhC,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,MAAK;wBACL,OAAO,UAAU,KAAK,IAAI;wBAC1B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wBAC1D,QAAQ;wBACR,OAAO,iBAAiB,KAAK;;;;;;kCAG/B,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,KAAK,IAAI;wBAC1B,UAAU,CAAC,IAAM,kBAAkB,SAAS,EAAE,MAAM,CAAC,KAAK;wBAC1D,aAAY;wBACZ,QAAQ;wBACR,OAAO,iBAAiB,KAAK;;;;;;kCAI/B,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,WAAW,IAAI;wBAChC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wBAChE,aAAY;wBACZ,QAAQ;wBACR,OAAO,iBAAiB,WAAW;;;;;;kCAGrC,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,MAAK;wBACL,OAAO,UAAU,aAAa,IAAI;wBAClC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wBAClE,QAAQ;wBACR,OAAO,iBAAiB,aAAa;;;;;;kCAGvC,8OAAC,sIAAA,CAAA,UAAM;wBACL,OAAM;wBACN,OAAO,UAAU,WAAW,IAAI;wBAChC,UAAU,CAAC,QAAU,kBAAkB,eAAe;wBACtD,SAAS;4BACP;gCAAE,OAAO;gCAAY,OAAO;4BAAW;4BACvC;gCAAE,OAAO;gCAAS,OAAO;4BAAQ;yBAClC;wBACD,QAAQ;wBACR,OAAO,iBAAiB,WAAW;;;;;;;;;;;;0BAKvC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAI1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,UAAU,cAAc,IAAI;oCACnC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;oCACnE,aAAY;oCACZ,QAAQ;oCACR,OAAO,iBAAiB,cAAc;;;;;;;;;;;0CAI1C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,UAAU,gBAAgB,IAAI;oCACrC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;oCACrE,aAAY;oCACZ,QAAQ;oCACR,OAAO,iBAAiB,gBAAgB;;;;;;;;;;;0CAI5C,8OAAC,yIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,UAAU,IAAI,IAAI;gCACzB,UAAU,CAAC,IAAM,kBAAkB,QAAQ,EAAE,MAAM,CAAC,KAAK;gCACzD,QAAQ;gCACR,OAAO,iBAAiB,IAAI;;;;;;0CAG9B,8OAAC,sIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,UAAU,QAAQ,IAAI;gCAC7B,UAAU,CAAC,QAAU,kBAAkB,YAAY;gCACnD,SAAS;oCACP;wCAAE,OAAO;wCAAY,OAAO;oCAAW;oCACvC;wCAAE,OAAO;wCAAY,OAAO;oCAAW;oCACvC;wCAAE,OAAO;wCAAS,OAAO;oCAAQ;oCACjC;wCAAE,OAAO;wCAAS,OAAO;oCAAQ;oCACjC;wCAAE,OAAO;wCAAS,OAAO;oCAAQ;iCAClC;gCACD,QAAQ;gCACR,OAAO,iBAAiB,QAAQ;;;;;;0CAGlC,8OAAC,yIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,UAAU,WAAW,IAAI;gCAChC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;gCAChE,OAAO,iBAAiB,WAAW;;;;;;;;;;;;;;;;;;YAMxC,iBAAiB,IAAI,kBACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CACV,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;YAQ/B,sBAAsB,CAAC,4BACtB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAyD;;;;;;8CAGvE,8OAAC;oCAAE,WAAU;8CAAkD;;;;;;;;;;;;;;;;;;;;;;;0BASvE,8OAAC;gBAAI,WAAU;0BACZ,sBAAsB,CAAC,2BACtB;;sCAEE,8OAAC;4BACC,SAAS;4BACT,UAAU,YAAY;4BACtB,WAAU;sCAET,YAAY,0BACX;;kDACE,8OAAC;wCAAE,WAAU;;;;;;oCAAyC;;6DAIxD;;kDACE,8OAAC;wCAAE,WAAU;;;;;;oCAAwB;;;;;;;;sCAO3C,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;;;;;;gCAA+B;;;;;;;;mCAKhD,kCAAkC,iBAClC,8OAAC;oBACC,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,YAAY,0BACX;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BACZ,gBAAgB,cAAc;;qDAGjC;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BACZ,gBAAgB,iBAAiB;;;;;;;;;;;;;;;;;;;AAQlD;uCAEe", "debugId": null}}, {"offset": {"line": 3673, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/CompanyProfile.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport TextInput from '@/components/common/TextInput';\nimport Select from '@/components/common/Select';\nimport { validateSection } from '@/utils/formValidation';\nimport { applicationFormDataService } from '@/services/applicationFormDataService';\nimport { IndependentStepProps } from '../types';\n\ninterface CompanyProfileProps extends IndependentStepProps {}\n\nconst CompanyProfile: React.FC<CompanyProfileProps> = ({\n  applicationId,\n  licenseTypeId,\n  licenseCategoryId,\n  isEditMode = false,\n  onStepComplete,\n  onStepError,\n  onNavigate\n}) => {\n  console.log('CompanyProfile component rendered with props:', {\n    applicationId,\n    licenseTypeId,\n    licenseCategoryId,\n    isEditMode\n  });\n  const [localData, setLocalData] = useState({\n    company_name: '',\n    business_registration_number: '',\n    tax_number: '',\n    company_type: '',\n    incorporation_date: '',\n    incorporation_place: '',\n    company_email: '',\n    company_phone: '',\n    company_address: '',\n    company_city: '',\n    company_district: '',\n    website: '',\n    number_of_employees: '',\n    annual_revenue: '',\n    business_description: ''\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n\n  // Load existing data when component mounts (for edit mode)\n  useEffect(() => {\n    const loadExistingData = async () => {\n      // Add more robust validation for applicationId\n      if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {\n        console.log('Loading existing company profile data for application:', applicationId);\n        setIsLoading(true);\n        try {\n          const existingData = await applicationFormDataService.getFormSection(applicationId, 'companyProfile');\n          if (existingData && existingData.section_data && Object.keys(existingData.section_data).length > 0) {\n            console.log('Loaded existing company profile data:', existingData.section_data);\n            setLocalData(prev => ({ ...prev, ...existingData.section_data }));\n          } else {\n            console.log('No existing company profile data found for application:', applicationId);\n          }\n        } catch (error) {\n          console.error('Error loading existing company profile data:', error);\n          // Don't call onStepError as it would hide the form\n          // Just log the error and continue with empty form\n          console.log('Continuing with empty form due to load error');\n        } finally {\n          setIsLoading(false);\n        }\n      } else {\n        console.log('Skipping company profile data load - not in edit mode or invalid applicationId:', { isEditMode, applicationId });\n      }\n    };\n\n    loadExistingData();\n  }, [applicationId, isEditMode]);\n\n  // Handle local data changes\n  const handleLocalChange = useCallback((field: string, value: any) => {\n    setLocalData((prev: any) => ({ ...prev, [field]: value }));\n    setHasUnsavedChanges(true);\n\n    // Clear validation error for this field if it exists\n    if (validationErrors && validationErrors[field]) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));\n    }\n\n    // Clear save error when user starts making changes\n    if (validationErrors.save) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, save: '' }));\n    }\n  }, [validationErrors]);\n\n  // Validate form data\n  const validateForm = () => {\n    const validation = validateSection(localData, 'companyProfile');\n    const errors = validation.errors || {};\n    setValidationErrors(errors);\n    return validation.isValid;\n  };\n\n  // Save data to backend\n  const saveData = async () => {\n    const validation = validateSection(localData, 'companyProfile');\n    const errors = validation.errors || {};\n    setValidationErrors(errors);\n\n    if (!validation.isValid) {\n      // Don't call onStepError as it hides the form\n      // Instead, let the form show validation errors inline\n      console.log('Validation failed:', errors);\n      return false;\n    }\n\n    setIsSaving(true);\n    try {\n      if (applicationId && applicationId !== 'new') {\n        await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'companyProfile', localData);\n        console.log('Company profile data saved for application:', applicationId);\n      } else {\n        console.warn('No application ID available for saving company profile');\n      }\n\n      setHasUnsavedChanges(false);\n      onStepComplete?.('company-profile', localData);\n      return true;\n    } catch (error: any) {\n      console.error('Error saving company profile data:', error);\n\n      // Extract meaningful error message\n      let errorMessage = 'Failed to save company profile information';\n      if (error.response?.data?.message) {\n        errorMessage = error.response.data.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      // Show error in the form itself instead of hiding the form\n      setValidationErrors(prev => ({ ...prev, save: errorMessage }));\n      return false;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Handle save button click\n  const handleSave = async () => {\n    await saveData();\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Company Profile\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Provide your company registration and business details.\n        </p>\n      </div>\n\n      {/* Basic Company Information */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n        <div className=\"md:col-span-2\">\n          <TextInput\n            label=\"Company Name\"\n            value={localData.company_name || ''}\n            onChange={(e) => handleLocalChange('company_name', e.target.value)}\n            required\n            error={validationErrors.company_name}\n          />\n        </div>\n\n        <TextInput\n          label=\"Business Registration Number\"\n          value={localData.business_registration_number || ''}\n          onChange={(e) => handleLocalChange('business_registration_number', e.target.value)}\n          placeholder=\"**********\"\n          required\n          error={validationErrors.business_registration_number}\n        />\n\n        <TextInput\n          label=\"Tax Number (TPIN)\"\n          value={localData.tax_number || ''}\n          onChange={(e) => handleLocalChange('tax_number', e.target.value)}\n          placeholder=\"TP123456789\"\n          required\n          error={validationErrors.tax_number}\n        />\n\n        <Select\n          label=\"Company Type\"\n          value={localData.company_type || ''}\n          onChange={(value) => handleLocalChange('company_type', value)}\n          options={[\n            { value: 'private_limited', label: 'Private Limited Company' },\n            { value: 'public_limited', label: 'Public Limited Company' },\n            { value: 'partnership', label: 'Partnership' },\n            { value: 'sole_proprietorship', label: 'Sole Proprietorship' },\n            { value: 'ngo', label: 'Non-Governmental Organization' },\n            { value: 'other', label: 'Other' }\n          ]}\n          required\n          error={validationErrors.company_type}\n        />\n\n        <TextInput\n          label=\"Date of Incorporation\"\n          type=\"date\"\n          value={localData.incorporation_date || ''}\n          onChange={(e) => handleLocalChange('incorporation_date', e.target.value)}\n          required\n          error={validationErrors.incorporation_date}\n        />\n\n        <TextInput\n          label=\"Place of Incorporation\"\n          value={localData.incorporation_place || ''}\n          onChange={(e) => handleLocalChange('incorporation_place', e.target.value)}\n          required\n          error={validationErrors.incorporation_place}\n        />\n      </div>\n\n      {/* Contact Information */}\n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n        <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          Company Contact Information\n        </h4>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <TextInput\n            label=\"Company Email\"\n            type=\"email\"\n            value={localData.company_email || ''}\n            onChange={(e) => handleLocalChange('company_email', e.target.value)}\n            required\n            error={validationErrors.company_email}\n          />\n\n          <TextInput\n            label=\"Company Phone\"\n            value={localData.company_phone || ''}\n            onChange={(e) => handleLocalChange('company_phone', e.target.value)}\n            placeholder=\"+265 1 234 567\"\n            required\n            error={validationErrors.company_phone}\n          />\n\n          <TextInput\n            label=\"Website\"\n            value={localData.website || ''}\n            onChange={(e) => handleLocalChange('website', e.target.value)}\n            placeholder=\"https://www.company.com\"\n            error={validationErrors.website}\n          />\n\n          <div className=\"md:col-span-2\">\n            <TextInput\n              label=\"Company Address\"\n              value={localData.company_address || ''}\n              onChange={(e) => handleLocalChange('company_address', e.target.value)}\n              required\n              error={validationErrors.company_address}\n            />\n          </div>\n\n          <TextInput\n            label=\"City\"\n            value={localData.company_city || ''}\n            onChange={(e) => handleLocalChange('company_city', e.target.value)}\n            required\n            error={validationErrors.company_city}\n          />\n\n          <Select\n            label=\"District\"\n            value={localData.company_district || ''}\n            onChange={(value) => handleLocalChange('company_district', value)}\n            options={[\n              { value: 'Blantyre', label: 'Blantyre' },\n              { value: 'Lilongwe', label: 'Lilongwe' },\n              { value: 'Mzuzu', label: 'Mzuzu' },\n              { value: 'Zomba', label: 'Zomba' },\n              { value: 'Other', label: 'Other' }\n            ]}\n            required\n            error={validationErrors.company_district}\n          />\n        </div>\n      </div>\n\n      {/* Business Details */}\n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n        <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          Business Details\n        </h4>\n        \n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <Select\n            label=\"Number of Employees\"\n            value={localData.number_of_employees || ''}\n            onChange={(value) => handleLocalChange('number_of_employees', value)}\n            options={[\n              { value: '1-10', label: '1-10 employees' },\n              { value: '11-50', label: '11-50 employees' },\n              { value: '51-100', label: '51-100 employees' },\n              { value: '101-500', label: '101-500 employees' },\n              { value: '500+', label: '500+ employees' }\n            ]}\n            required\n            error={validationErrors.number_of_employees}\n          />\n\n          <Select\n            label=\"Annual Revenue (MWK)\"\n            value={localData.annual_revenue || ''}\n            onChange={(value) => handleLocalChange('annual_revenue', value)}\n            options={[\n              { value: 'under_1m', label: 'Under 1 Million' },\n              { value: '1m_10m', label: '1-10 Million' },\n              { value: '10m_50m', label: '10-50 Million' },\n              { value: '50m_100m', label: '50-100 Million' },\n              { value: 'over_100m', label: 'Over 100 Million' }\n            ]}\n            required\n            error={validationErrors.annual_revenue}\n          />\n\n          <div className=\"md:col-span-2\">\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Business Description *\n            </label>\n            <textarea\n              value={localData.business_description || ''}\n              onChange={(e) => handleLocalChange('business_description', e.target.value)}\n              rows={4}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Describe your business activities and operations...\"\n            />\n            {validationErrors.business_description && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.business_description}\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {validationErrors.save && (\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3\"></i>\n            <div>\n              <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                Error Saving Company Profile\n              </h3>\n              <p className=\"text-red-700 dark:text-red-300 text-sm mt-1\">\n                {validationErrors.save}\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Save Button */}\n      <div className=\"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700\">\n        <button\n          onClick={handleSave}\n          disabled={isSaving || isLoading}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isSaving || isLoading ? (\n            <>\n              <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n              Saving...\n            </>\n          ) : (\n            <>\n              <i className=\"ri-save-line mr-2\"></i>\n              Save Company Profile\n            </>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default CompanyProfile;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAWA,MAAM,iBAAgD,CAAC,EACrD,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,aAAa,KAAK,EAClB,cAAc,EACd,WAAW,EACX,UAAU,EACX;IACC,QAAQ,GAAG,CAAC,iDAAiD;QAC3D;QACA;QACA;QACA;IACF;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,cAAc;QACd,8BAA8B;QAC9B,YAAY;QACZ,cAAc;QACd,oBAAoB;QACpB,qBAAqB;QACrB,eAAe;QACf,eAAe;QACf,iBAAiB;QACjB,cAAc;QACd,kBAAkB;QAClB,SAAS;QACT,qBAAqB;QACrB,gBAAgB;QAChB,sBAAsB;IACxB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,+CAA+C;YAC/C,IAAI,cAAc,iBAAiB,kBAAkB,SAAS,kBAAkB,eAAe,cAAc,IAAI,OAAO,IAAI;gBAC1H,QAAQ,GAAG,CAAC,0DAA0D;gBACtE,aAAa;gBACb,IAAI;oBACF,MAAM,eAAe,MAAM,6IAAA,CAAA,6BAA0B,CAAC,cAAc,CAAC,eAAe;oBACpF,IAAI,gBAAgB,aAAa,YAAY,IAAI,OAAO,IAAI,CAAC,aAAa,YAAY,EAAE,MAAM,GAAG,GAAG;wBAClG,QAAQ,GAAG,CAAC,yCAAyC,aAAa,YAAY;wBAC9E,aAAa,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,GAAG,aAAa,YAAY;4BAAC,CAAC;oBACjE,OAAO;wBACL,QAAQ,GAAG,CAAC,2DAA2D;oBACzE;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,gDAAgD;oBAC9D,mDAAmD;oBACnD,kDAAkD;oBAClD,QAAQ,GAAG,CAAC;gBACd,SAAU;oBACR,aAAa;gBACf;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,mFAAmF;oBAAE;oBAAY;gBAAc;YAC7H;QACF;QAEA;IACF,GAAG;QAAC;QAAe;KAAW;IAE9B,4BAA4B;IAC5B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QACpD,aAAa,CAAC,OAAc,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACxD,qBAAqB;QAErB,qDAAqD;QACrD,IAAI,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;YAC/C,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACjF;QAEA,mDAAmD;QACnD,IAAI,iBAAiB,IAAI,EAAE;YACzB,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAG,CAAC;QAC9E;IACF,GAAG;QAAC;KAAiB;IAErB,qBAAqB;IACrB,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,MAAM,SAAS,WAAW,MAAM,IAAI,CAAC;QACrC,oBAAoB;QACpB,OAAO,WAAW,OAAO;IAC3B;IAEA,uBAAuB;IACvB,MAAM,WAAW;QACf,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,MAAM,SAAS,WAAW,MAAM,IAAI,CAAC;QACrC,oBAAoB;QAEpB,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,8CAA8C;YAC9C,sDAAsD;YACtD,QAAQ,GAAG,CAAC,sBAAsB;YAClC,OAAO;QACT;QAEA,YAAY;QACZ,IAAI;YACF,IAAI,iBAAiB,kBAAkB,OAAO;gBAC5C,MAAM,6IAAA,CAAA,6BAA0B,CAAC,uBAAuB,CAAC,eAAe,kBAAkB;gBAC1F,QAAQ,GAAG,CAAC,+CAA+C;YAC7D,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YAEA,qBAAqB;YACrB,iBAAiB,mBAAmB;YACpC,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,sCAAsC;YAEpD,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC5C,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,eAAe,MAAM,OAAO;YAC9B;YAEA,2DAA2D;YAC3D,oBAAoB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAa,CAAC;YAC5D,OAAO;QACT,SAAU;YACR,YAAY;QACd;IACF;IAEA,2BAA2B;IAC3B,MAAM,aAAa;QACjB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;4BACR,OAAM;4BACN,OAAO,UAAU,YAAY,IAAI;4BACjC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;4BACjE,QAAQ;4BACR,OAAO,iBAAiB,YAAY;;;;;;;;;;;kCAIxC,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,4BAA4B,IAAI;wBACjD,UAAU,CAAC,IAAM,kBAAkB,gCAAgC,EAAE,MAAM,CAAC,KAAK;wBACjF,aAAY;wBACZ,QAAQ;wBACR,OAAO,iBAAiB,4BAA4B;;;;;;kCAGtD,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,UAAU,IAAI;wBAC/B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC/D,aAAY;wBACZ,QAAQ;wBACR,OAAO,iBAAiB,UAAU;;;;;;kCAGpC,8OAAC,sIAAA,CAAA,UAAM;wBACL,OAAM;wBACN,OAAO,UAAU,YAAY,IAAI;wBACjC,UAAU,CAAC,QAAU,kBAAkB,gBAAgB;wBACvD,SAAS;4BACP;gCAAE,OAAO;gCAAmB,OAAO;4BAA0B;4BAC7D;gCAAE,OAAO;gCAAkB,OAAO;4BAAyB;4BAC3D;gCAAE,OAAO;gCAAe,OAAO;4BAAc;4BAC7C;gCAAE,OAAO;gCAAuB,OAAO;4BAAsB;4BAC7D;gCAAE,OAAO;gCAAO,OAAO;4BAAgC;4BACvD;gCAAE,OAAO;gCAAS,OAAO;4BAAQ;yBAClC;wBACD,QAAQ;wBACR,OAAO,iBAAiB,YAAY;;;;;;kCAGtC,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,MAAK;wBACL,OAAO,UAAU,kBAAkB,IAAI;wBACvC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;wBACvE,QAAQ;wBACR,OAAO,iBAAiB,kBAAkB;;;;;;kCAG5C,8OAAC,yIAAA,CAAA,UAAS;wBACR,OAAM;wBACN,OAAO,UAAU,mBAAmB,IAAI;wBACxC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;wBACxE,QAAQ;wBACR,OAAO,iBAAiB,mBAAmB;;;;;;;;;;;;0BAK/C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAI1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,yIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,MAAK;gCACL,OAAO,UAAU,aAAa,IAAI;gCAClC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAClE,QAAQ;gCACR,OAAO,iBAAiB,aAAa;;;;;;0CAGvC,8OAAC,yIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,UAAU,aAAa,IAAI;gCAClC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAClE,aAAY;gCACZ,QAAQ;gCACR,OAAO,iBAAiB,aAAa;;;;;;0CAGvC,8OAAC,yIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,UAAU,OAAO,IAAI;gCAC5B,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;gCAC5D,aAAY;gCACZ,OAAO,iBAAiB,OAAO;;;;;;0CAGjC,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,yIAAA,CAAA,UAAS;oCACR,OAAM;oCACN,OAAO,UAAU,eAAe,IAAI;oCACpC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;oCACpE,QAAQ;oCACR,OAAO,iBAAiB,eAAe;;;;;;;;;;;0CAI3C,8OAAC,yIAAA,CAAA,UAAS;gCACR,OAAM;gCACN,OAAO,UAAU,YAAY,IAAI;gCACjC,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCACjE,QAAQ;gCACR,OAAO,iBAAiB,YAAY;;;;;;0CAGtC,8OAAC,sIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,UAAU,gBAAgB,IAAI;gCACrC,UAAU,CAAC,QAAU,kBAAkB,oBAAoB;gCAC3D,SAAS;oCACP;wCAAE,OAAO;wCAAY,OAAO;oCAAW;oCACvC;wCAAE,OAAO;wCAAY,OAAO;oCAAW;oCACvC;wCAAE,OAAO;wCAAS,OAAO;oCAAQ;oCACjC;wCAAE,OAAO;wCAAS,OAAO;oCAAQ;oCACjC;wCAAE,OAAO;wCAAS,OAAO;oCAAQ;iCAClC;gCACD,QAAQ;gCACR,OAAO,iBAAiB,gBAAgB;;;;;;;;;;;;;;;;;;0BAM9C,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAI1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,UAAU,mBAAmB,IAAI;gCACxC,UAAU,CAAC,QAAU,kBAAkB,uBAAuB;gCAC9D,SAAS;oCACP;wCAAE,OAAO;wCAAQ,OAAO;oCAAiB;oCACzC;wCAAE,OAAO;wCAAS,OAAO;oCAAkB;oCAC3C;wCAAE,OAAO;wCAAU,OAAO;oCAAmB;oCAC7C;wCAAE,OAAO;wCAAW,OAAO;oCAAoB;oCAC/C;wCAAE,OAAO;wCAAQ,OAAO;oCAAiB;iCAC1C;gCACD,QAAQ;gCACR,OAAO,iBAAiB,mBAAmB;;;;;;0CAG7C,8OAAC,sIAAA,CAAA,UAAM;gCACL,OAAM;gCACN,OAAO,UAAU,cAAc,IAAI;gCACnC,UAAU,CAAC,QAAU,kBAAkB,kBAAkB;gCACzD,SAAS;oCACP;wCAAE,OAAO;wCAAY,OAAO;oCAAkB;oCAC9C;wCAAE,OAAO;wCAAU,OAAO;oCAAe;oCACzC;wCAAE,OAAO;wCAAW,OAAO;oCAAgB;oCAC3C;wCAAE,OAAO;wCAAY,OAAO;oCAAiB;oCAC7C;wCAAE,OAAO;wCAAa,OAAO;oCAAmB;iCACjD;gCACD,QAAQ;gCACR,OAAO,iBAAiB,cAAc;;;;;;0CAGxC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,UAAU,oBAAoB,IAAI;wCACzC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;wCACzE,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;oCAEb,iBAAiB,oBAAoB,kBACpC,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;YAQ/C,iBAAiB,IAAI,kBACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CACV,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,YAAY,0BACX;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAyC;;qDAIxD;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAwB;;;;;;;;;;;;;;;;;;;AAQnD;uCAEe", "debugId": null}}, {"offset": {"line": 4321, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/Management.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport TextInput from '@/components/common/TextInput';\nimport { validateSection } from '@/utils/formValidation';\nimport { IndependentStepProps } from '../types';\nimport { applicationFormDataService } from '@/services/applicationFormDataService';\n\n// Stakeholder data structure matching backend entity\ninterface StakeholderData {\n  stakeholder_id?: string;\n  first_name: string;\n  last_name: string;\n  middle_name?: string;\n  nationality: string;\n  position: 'CEO' | 'SHAREHOLDER' | 'AUDITOR' | 'LAWYER';\n  profile: string;\n  contact_id?: string;\n  cv_document_id?: string;\n}\n\ninterface ManagementProps extends IndependentStepProps {}\n\nconst Management: React.FC<ManagementProps> = ({\n  applicationId,\n  licenseTypeId,\n  licenseCategoryId,\n  isEditMode = false,\n  onStepComplete,\n  onStepError,\n  onNavigate\n}) => {\n  const [localData, setLocalData] = useState({\n    stakeholders: [] as StakeholderData[],\n    organizational_structure: '',\n    key_personnel: '',\n    management_experience: '',\n    leadership_approach: '',\n    succession_planning: ''\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n\n  // Load existing data when component mounts (for edit mode)\n  useEffect(() => {\n    const loadExistingData = async () => {\n      // Add more robust validation for applicationId\n      if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {\n        console.log('Loading existing stakeholders data for application:', applicationId);\n        setIsLoading(true);\n        try {\n          // TODO: Replace with stakeholders API call when available\n          // For now, use the form data service as fallback\n          const existingData = await applicationFormDataService.getFormSection(applicationId, 'management');\n          if (existingData && existingData.section_data && Object.keys(existingData.section_data).length > 0) {\n            console.log('Loaded existing management data:', existingData.section_data);\n            setLocalData(prev => ({ ...prev, ...existingData.section_data }));\n          } else {\n            console.log('No existing management data found for application:', applicationId);\n          }\n        } catch (error) {\n          console.error('Error loading existing management data:', error);\n          // Don't call onStepError as it would hide the form\n          // Just log the error and continue with empty form\n          console.log('Continuing with empty form due to load error');\n        } finally {\n          setIsLoading(false);\n        }\n      } else {\n        console.log('Skipping management data load - not in edit mode or invalid applicationId:', { isEditMode, applicationId });\n      }\n    };\n\n    loadExistingData();\n  }, [applicationId, isEditMode]);\n\n  // Handle local data changes\n  const handleLocalChange = (field: string, value: any) => {\n    setLocalData(prev => ({ ...prev, [field]: value }));\n    setHasUnsavedChanges(true);\n    // Clear validation error when user starts typing\n    if (validationErrors[field]) {\n      setValidationErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  // Validation function\n  const validateForm = () => {\n    const validation = validateSection(localData, 'management');\n    setValidationErrors(validation.errors);\n    return validation.isValid;\n  };\n\n  // Handle save\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      // TODO: Replace with stakeholders API call when available\n      // For now, use the form data service as fallback\n      await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'management', localData);\n      setHasUnsavedChanges(false);\n      console.log('Management data saved successfully');\n\n      // Notify parent component of successful save\n      if (onStepComplete) {\n        onStepComplete('management', localData);\n      }\n    } catch (error) {\n      console.error('Error saving management data:', error);\n      if (onStepError) {\n        onStepError('management', { save: 'Failed to save management data. Please try again.' });\n      }\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Stakeholder management functions\n  const addStakeholder = () => {\n    const newStakeholder: StakeholderData = {\n      first_name: '',\n      last_name: '',\n      middle_name: '',\n      nationality: '',\n      position: 'CEO',\n      profile: ''\n    };\n    const updatedStakeholders = [...localData.stakeholders, newStakeholder];\n    handleLocalChange('stakeholders', updatedStakeholders);\n  };\n\n  const updateStakeholder = (index: number, field: keyof StakeholderData, value: string) => {\n    const updatedStakeholders = localData.stakeholders.map((stakeholder, i) =>\n      i === index ? { ...stakeholder, [field]: value } : stakeholder\n    );\n    handleLocalChange('stakeholders', updatedStakeholders);\n  };\n\n  const removeStakeholder = (index: number) => {\n    const updatedStakeholders = localData.stakeholders.filter((_, i) => i !== index);\n    handleLocalChange('stakeholders', updatedStakeholders);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Management Information\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Provide details about your organization's management structure and key personnel.\n        </p>\n      </div>\n\n      {/* Stakeholders */}\n      <div>\n        <div className=\"flex items-center justify-between mb-4\">\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n            Key Stakeholders\n          </label>\n          <button\n            type=\"button\"\n            onClick={addStakeholder}\n            className=\"inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n          >\n            <i className=\"ri-add-line mr-1\"></i>\n            Add Stakeholder\n          </button>\n        </div>\n\n        {localData.stakeholders.map((stakeholder, index) => (\n          <div key={index} className=\"border border-gray-300 dark:border-gray-600 rounded-lg p-4 mb-4\">\n            <div className=\"flex items-center justify-between mb-3\">\n              <h4 className=\"text-sm font-medium text-gray-900 dark:text-gray-100\">\n                Stakeholder {index + 1}\n              </h4>\n              <button\n                type=\"button\"\n                onClick={() => removeStakeholder(index)}\n                className=\"text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300\"\n              >\n                <i className=\"ri-delete-bin-line\"></i>\n              </button>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n              <TextInput\n                label=\"First Name\"\n                value={stakeholder.first_name}\n                onChange={(e) => updateStakeholder(index, 'first_name', e.target.value)}\n                required\n              />\n\n              <TextInput\n                label=\"Last Name\"\n                value={stakeholder.last_name}\n                onChange={(e) => updateStakeholder(index, 'last_name', e.target.value)}\n                required\n              />\n\n              <TextInput\n                label=\"Middle Name\"\n                value={stakeholder.middle_name || ''}\n                onChange={(e) => updateStakeholder(index, 'middle_name', e.target.value)}\n              />\n\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Position *\n                </label>\n                <select\n                  value={stakeholder.position}\n                  onChange={(e) => updateStakeholder(index, 'position', e.target.value as 'CEO' | 'SHAREHOLDER' | 'AUDITOR' | 'LAWYER')}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                  required\n                >\n                  <option value=\"CEO\">CEO</option>\n                  <option value=\"SHAREHOLDER\">Shareholder</option>\n                  <option value=\"AUDITOR\">Auditor</option>\n                  <option value=\"LAWYER\">Lawyer</option>\n                </select>\n              </div>\n\n              <TextInput\n                label=\"Nationality\"\n                value={stakeholder.nationality}\n                onChange={(e) => updateStakeholder(index, 'nationality', e.target.value)}\n                required\n              />\n\n              <div className=\"md:col-span-2\">\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Profile/Background *\n                </label>\n                <textarea\n                  value={stakeholder.profile}\n                  onChange={(e) => updateStakeholder(index, 'profile', e.target.value)}\n                  rows={3}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n                  placeholder=\"Professional background, qualifications, and experience...\"\n                  required\n                />\n              </div>\n            </div>\n          </div>\n        ))}\n\n        {localData.stakeholders.length === 0 && (\n          <div className=\"text-center py-6 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg\">\n            <p className=\"text-gray-500 dark:text-gray-400\">No stakeholders added yet.</p>\n            <button\n              type=\"button\"\n              onClick={addStakeholder}\n              className=\"mt-2 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-primary bg-primary/10 hover:bg-primary/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              <i className=\"ri-add-line mr-1\"></i>\n              Add First Stakeholder\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Organizational Structure */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Organizational Structure *\n        </label>\n        <textarea\n          value={localData.organizational_structure || ''}\n          onChange={(e) => handleLocalChange('organizational_structure', e.target.value)}\n          rows={4}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"Describe your organizational structure, reporting lines, and governance framework...\"\n        />\n        {validationErrors.organizational_structure && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {validationErrors.organizational_structure}\n          </p>\n        )}\n      </div>\n\n      {/* Key Personnel */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Key Personnel *\n        </label>\n        <textarea\n          value={localData.key_personnel || ''}\n          onChange={(e) => handleLocalChange('key_personnel', e.target.value)}\n          rows={3}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"List key personnel and their roles in the organization...\"\n        />\n        {validationErrors.key_personnel && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {validationErrors.key_personnel}\n          </p>\n        )}\n      </div>\n\n      {/* Save Button */}\n      <div className=\"flex justify-end pt-4\">\n        <button\n          type=\"button\"\n          onClick={handleSave}\n          disabled={isSaving || isLoading}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isSaving ? (\n            <>\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n              Saving...\n            </>\n          ) : (\n            <>\n              <i className=\"ri-save-line mr-2\"></i>\n              Save Management Info\n            </>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default Management;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAuBA,MAAM,aAAwC,CAAC,EAC7C,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,aAAa,KAAK,EAClB,cAAc,EACd,WAAW,EACX,UAAU,EACX;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,cAAc,EAAE;QAChB,0BAA0B;QAC1B,eAAe;QACf,uBAAuB;QACvB,qBAAqB;QACrB,qBAAqB;IACvB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,+CAA+C;YAC/C,IAAI,cAAc,iBAAiB,kBAAkB,SAAS,kBAAkB,eAAe,cAAc,IAAI,OAAO,IAAI;gBAC1H,QAAQ,GAAG,CAAC,uDAAuD;gBACnE,aAAa;gBACb,IAAI;oBACF,0DAA0D;oBAC1D,iDAAiD;oBACjD,MAAM,eAAe,MAAM,6IAAA,CAAA,6BAA0B,CAAC,cAAc,CAAC,eAAe;oBACpF,IAAI,gBAAgB,aAAa,YAAY,IAAI,OAAO,IAAI,CAAC,aAAa,YAAY,EAAE,MAAM,GAAG,GAAG;wBAClG,QAAQ,GAAG,CAAC,oCAAoC,aAAa,YAAY;wBACzE,aAAa,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,GAAG,aAAa,YAAY;4BAAC,CAAC;oBACjE,OAAO;wBACL,QAAQ,GAAG,CAAC,sDAAsD;oBACpE;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,2CAA2C;oBACzD,mDAAmD;oBACnD,kDAAkD;oBAClD,QAAQ,GAAG,CAAC;gBACd,SAAU;oBACR,aAAa;gBACf;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,8EAA8E;oBAAE;oBAAY;gBAAc;YACxH;QACF;QAEA;IACF,GAAG;QAAC;QAAe;KAAW;IAE9B,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC,OAAe;QACxC,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACjD,qBAAqB;QACrB,iDAAiD;QACjD,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACvD;IACF;IAEA,sBAAsB;IACtB,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,oBAAoB,WAAW,MAAM;QACrC,OAAO,WAAW,OAAO;IAC3B;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,YAAY;QACZ,IAAI;YACF,0DAA0D;YAC1D,iDAAiD;YACjD,MAAM,6IAAA,CAAA,6BAA0B,CAAC,uBAAuB,CAAC,eAAe,cAAc;YACtF,qBAAqB;YACrB,QAAQ,GAAG,CAAC;YAEZ,6CAA6C;YAC7C,IAAI,gBAAgB;gBAClB,eAAe,cAAc;YAC/B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;YAC/C,IAAI,aAAa;gBACf,YAAY,cAAc;oBAAE,MAAM;gBAAoD;YACxF;QACF,SAAU;YACR,YAAY;QACd;IACF;IAEA,mCAAmC;IACnC,MAAM,iBAAiB;QACrB,MAAM,iBAAkC;YACtC,YAAY;YACZ,WAAW;YACX,aAAa;YACb,aAAa;YACb,UAAU;YACV,SAAS;QACX;QACA,MAAM,sBAAsB;eAAI,UAAU,YAAY;YAAE;SAAe;QACvE,kBAAkB,gBAAgB;IACpC;IAEA,MAAM,oBAAoB,CAAC,OAAe,OAA8B;QACtE,MAAM,sBAAsB,UAAU,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,IACnE,MAAM,QAAQ;gBAAE,GAAG,WAAW;gBAAE,CAAC,MAAM,EAAE;YAAM,IAAI;QAErD,kBAAkB,gBAAgB;IACpC;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,sBAAsB,UAAU,YAAY,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAC1E,kBAAkB,gBAAgB;IACpC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAA6D;;;;;;0CAG9E,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;oBAKvC,UAAU,YAAY,CAAC,GAAG,CAAC,CAAC,aAAa,sBACxC,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;;gDAAuD;gDACtD,QAAQ;;;;;;;sDAEvB,8OAAC;4CACC,MAAK;4CACL,SAAS,IAAM,kBAAkB;4CACjC,WAAU;sDAEV,cAAA,8OAAC;gDAAE,WAAU;;;;;;;;;;;;;;;;;8CAIjB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,YAAY,UAAU;4CAC7B,UAAU,CAAC,IAAM,kBAAkB,OAAO,cAAc,EAAE,MAAM,CAAC,KAAK;4CACtE,QAAQ;;;;;;sDAGV,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,YAAY,SAAS;4CAC5B,UAAU,CAAC,IAAM,kBAAkB,OAAO,aAAa,EAAE,MAAM,CAAC,KAAK;4CACrE,QAAQ;;;;;;sDAGV,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,YAAY,WAAW,IAAI;4CAClC,UAAU,CAAC,IAAM,kBAAkB,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;sDAGzE,8OAAC;;8DACC,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,OAAO,YAAY,QAAQ;oDAC3B,UAAU,CAAC,IAAM,kBAAkB,OAAO,YAAY,EAAE,MAAM,CAAC,KAAK;oDACpE,WAAU;oDACV,QAAQ;;sEAER,8OAAC;4DAAO,OAAM;sEAAM;;;;;;sEACpB,8OAAC;4DAAO,OAAM;sEAAc;;;;;;sEAC5B,8OAAC;4DAAO,OAAM;sEAAU;;;;;;sEACxB,8OAAC;4DAAO,OAAM;sEAAS;;;;;;;;;;;;;;;;;;sDAI3B,8OAAC,yIAAA,CAAA,UAAS;4CACR,OAAM;4CACN,OAAO,YAAY,WAAW;4CAC9B,UAAU,CAAC,IAAM,kBAAkB,OAAO,eAAe,EAAE,MAAM,CAAC,KAAK;4CACvE,QAAQ;;;;;;sDAGV,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAM,WAAU;8DAAkE;;;;;;8DAGnF,8OAAC;oDACC,OAAO,YAAY,OAAO;oDAC1B,UAAU,CAAC,IAAM,kBAAkB,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK;oDACnE,MAAM;oDACN,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;;;;;;;;2BArEN;;;;;oBA4EX,UAAU,YAAY,CAAC,MAAM,KAAK,mBACjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCAAE,WAAU;;;;;;oCAAuB;;;;;;;;;;;;;;;;;;;0BAQ5C,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,wBAAwB,IAAI;wBAC7C,UAAU,CAAC,IAAM,kBAAkB,4BAA4B,EAAE,MAAM,CAAC,KAAK;wBAC7E,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,iBAAiB,wBAAwB,kBACxC,8OAAC;wBAAE,WAAU;kCACV,iBAAiB,wBAAwB;;;;;;;;;;;;0BAMhD,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,aAAa,IAAI;wBAClC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;wBAClE,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,iBAAiB,aAAa,kBAC7B,8OAAC;wBAAE,WAAU;kCACV,iBAAiB,aAAa;;;;;;;;;;;;0BAMrC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,yBACC;;0CACE,8OAAC;gCAAI,WAAU;;;;;;4BAAuE;;qDAIxF;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAwB;;;;;;;;;;;;;;;;;;;AAQnD;uCAEe", "debugId": null}}, {"offset": {"line": 4877, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/ProfessionalServices.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { validateSection } from '@/utils/formValidation';\n\ninterface ProfessionalServicesProps {\n  formData: any;\n  onChange: (field: string, value: any) => void;\n  onSave: (data: any) => Promise<string>;\n  errors: Record<string, string>;\n  applicationId?: string;\n  isLoading?: boolean;\n}\n\nconst ProfessionalServices: React.FC<ProfessionalServicesProps> = ({\n  formData,\n  onChange,\n  onSave,\n  errors,\n  applicationId,\n  isLoading = false\n}) => {\n  const [localData, setLocalData] = useState({\n    consultants: '',\n    service_providers: '',\n    technical_support: '',\n    maintenance_arrangements: '',\n    professional_partnerships: '',\n    outsourced_services: '',\n    quality_assurance: '',\n    training_programs: '',\n    ...formData\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n\n  // Sync with parent form data\n  useEffect(() => {\n    if (formData && Object.keys(formData).length > 0) {\n      setLocalData(prev => ({ ...prev, ...formData }));\n    }\n  }, [formData]);\n\n  // Handle local data changes\n  const handleLocalChange = (field: string, value: any) => {\n    setLocalData(prev => ({ ...prev, [field]: value }));\n    // Clear validation error when user starts typing\n    if (validationErrors[field]) {\n      setValidationErrors(prev => ({ ...prev, [field]: '' }));\n    }\n  };\n\n  // Validation function\n  const validateForm = () => {\n    const errors = validateSection('professionalServices', localData);\n    setValidationErrors(errors);\n    return Object.keys(errors).length === 0;\n  };\n\n  // Handle save\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      await onSave(localData);\n      console.log('Professional services data saved');\n    } catch (error) {\n      console.error('Error saving professional services data:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Auto-save on blur\n  const handleBlur = (field: string) => {\n    if (localData[field] && !validationErrors[field]) {\n      onChange(field, localData[field]);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Professional Services\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Provide information about external consultants, service providers, and support arrangements.\n        </p>\n      </div>\n\n      {/* External Consultants */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          External Consultants *\n        </label>\n        <textarea\n          value={localData.consultants || ''}\n          onChange={(e) => handleLocalChange('consultants', e.target.value)}\n          onBlur={() => handleBlur('consultants')}\n          rows={4}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"List any external consultants, their areas of expertise, and their role in your operations...\"\n        />\n        {(validationErrors.consultants || errors.consultants) && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {validationErrors.consultants || errors.consultants}\n          </p>\n        )}\n      </div>\n\n      {/* Service Providers */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Service Providers *\n        </label>\n        <textarea\n          value={localData.service_providers || ''}\n          onChange={(e) => handleLocalChange('service_providers', e.target.value)}\n          onBlur={() => handleBlur('service_providers')}\n          rows={4}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"Describe your service providers, their services, and contractual arrangements...\"\n        />\n        {(validationErrors.service_providers || errors.service_providers) && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {validationErrors.service_providers || errors.service_providers}\n          </p>\n        )}\n      </div>\n\n      {/* Technical Support */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Technical Support Arrangements *\n        </label>\n        <textarea\n          value={localData.technical_support || ''}\n          onChange={(e) => handleLocalChange('technical_support', e.target.value)}\n          onBlur={() => handleBlur('technical_support')}\n          rows={4}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"Describe your technical support arrangements, including internal and external resources...\"\n        />\n        {(validationErrors.technical_support || errors.technical_support) && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {validationErrors.technical_support || errors.technical_support}\n          </p>\n        )}\n      </div>\n\n      {/* Maintenance Arrangements */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Maintenance Arrangements *\n        </label>\n        <textarea\n          value={localData.maintenance_arrangements || ''}\n          onChange={(e) => handleLocalChange('maintenance_arrangements', e.target.value)}\n          onBlur={() => handleBlur('maintenance_arrangements')}\n          rows={4}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"Detail your maintenance arrangements for equipment, systems, and infrastructure...\"\n        />\n        {(validationErrors.maintenance_arrangements || errors.maintenance_arrangements) && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {validationErrors.maintenance_arrangements || errors.maintenance_arrangements}\n          </p>\n        )}\n      </div>\n\n      {/* Professional Partnerships */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Professional Partnerships\n        </label>\n        <textarea\n          value={localData.professional_partnerships || ''}\n          onChange={(e) => handleLocalChange('professional_partnerships', e.target.value)}\n          onBlur={() => handleBlur('professional_partnerships')}\n          rows={3}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"Describe any professional partnerships or strategic alliances...\"\n        />\n      </div>\n\n      {/* Outsourced Services */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Outsourced Services\n        </label>\n        <textarea\n          value={localData.outsourced_services || ''}\n          onChange={(e) => handleLocalChange('outsourced_services', e.target.value)}\n          onBlur={() => handleBlur('outsourced_services')}\n          rows={3}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"List any services that will be outsourced and the rationale...\"\n        />\n      </div>\n\n      {/* Quality Assurance */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Quality Assurance Measures\n        </label>\n        <textarea\n          value={localData.quality_assurance || ''}\n          onChange={(e) => handleLocalChange('quality_assurance', e.target.value)}\n          onBlur={() => handleBlur('quality_assurance')}\n          rows={3}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"Describe your quality assurance processes and standards...\"\n        />\n      </div>\n\n      {/* Training Programs */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Training Programs\n        </label>\n        <textarea\n          value={localData.training_programs || ''}\n          onChange={(e) => handleLocalChange('training_programs', e.target.value)}\n          onBlur={() => handleBlur('training_programs')}\n          rows={3}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"Describe training programs for staff and ongoing professional development...\"\n        />\n      </div>\n\n      {/* Save Button */}\n      <div className=\"flex justify-end pt-4\">\n        <button\n          type=\"button\"\n          onClick={handleSave}\n          disabled={isSaving || isLoading}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isSaving ? (\n            <>\n              <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n              Saving...\n            </>\n          ) : (\n            <>\n              <i className=\"ri-save-line mr-2\"></i>\n              Save Professional Services\n            </>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ProfessionalServices;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,uBAA4D,CAAC,EACjE,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,EACb,YAAY,KAAK,EAClB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,aAAa;QACb,mBAAmB;QACnB,mBAAmB;QACnB,0BAA0B;QAC1B,2BAA2B;QAC3B,qBAAqB;QACrB,mBAAmB;QACnB,mBAAmB;QACnB,GAAG,QAAQ;IACb;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,6BAA6B;IAC7B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,GAAG;YAChD,aAAa,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,GAAG,QAAQ;gBAAC,CAAC;QAChD;IACF,GAAG;QAAC;KAAS;IAEb,4BAA4B;IAC5B,MAAM,oBAAoB,CAAC,OAAe;QACxC,aAAa,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACjD,iDAAiD;QACjD,IAAI,gBAAgB,CAAC,MAAM,EAAE;YAC3B,oBAAoB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACvD;IACF;IAEA,sBAAsB;IACtB,MAAM,eAAe;QACnB,MAAM,SAAS,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,wBAAwB;QACvD,oBAAoB;QACpB,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK;IACxC;IAEA,cAAc;IACd,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,YAAY;QACZ,IAAI;YACF,MAAM,OAAO;YACb,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;QAC5D,SAAU;YACR,YAAY;QACd;IACF;IAEA,oBAAoB;IACpB,MAAM,aAAa,CAAC;QAClB,IAAI,SAAS,CAAC,MAAM,IAAI,CAAC,gBAAgB,CAAC,MAAM,EAAE;YAChD,SAAS,OAAO,SAAS,CAAC,MAAM;QAClC;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,WAAW,IAAI;wBAChC,UAAU,CAAC,IAAM,kBAAkB,eAAe,EAAE,MAAM,CAAC,KAAK;wBAChE,QAAQ,IAAM,WAAW;wBACzB,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,CAAC,iBAAiB,WAAW,IAAI,OAAO,WAAW,mBAClD,8OAAC;wBAAE,WAAU;kCACV,iBAAiB,WAAW,IAAI,OAAO,WAAW;;;;;;;;;;;;0BAMzD,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,iBAAiB,IAAI;wBACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACtE,QAAQ,IAAM,WAAW;wBACzB,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,CAAC,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB,mBAC9D,8OAAC;wBAAE,WAAU;kCACV,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;;;;;;;;;;;;0BAMrE,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,iBAAiB,IAAI;wBACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACtE,QAAQ,IAAM,WAAW;wBACzB,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,CAAC,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB,mBAC9D,8OAAC;wBAAE,WAAU;kCACV,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;;;;;;;;;;;;0BAMrE,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,wBAAwB,IAAI;wBAC7C,UAAU,CAAC,IAAM,kBAAkB,4BAA4B,EAAE,MAAM,CAAC,KAAK;wBAC7E,QAAQ,IAAM,WAAW;wBACzB,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,CAAC,iBAAiB,wBAAwB,IAAI,OAAO,wBAAwB,mBAC5E,8OAAC;wBAAE,WAAU;kCACV,iBAAiB,wBAAwB,IAAI,OAAO,wBAAwB;;;;;;;;;;;;0BAMnF,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,yBAAyB,IAAI;wBAC9C,UAAU,CAAC,IAAM,kBAAkB,6BAA6B,EAAE,MAAM,CAAC,KAAK;wBAC9E,QAAQ,IAAM,WAAW;wBACzB,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAKhB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,mBAAmB,IAAI;wBACxC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;wBACxE,QAAQ,IAAM,WAAW;wBACzB,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAKhB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,iBAAiB,IAAI;wBACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACtE,QAAQ,IAAM,WAAW;wBACzB,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAKhB,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,iBAAiB,IAAI;wBACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACtE,QAAQ,IAAM,WAAW;wBACzB,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,yBACC;;0CACE,8OAAC;gCAAI,WAAU;;;;;;4BAAuE;;qDAIxF;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAwB;;;;;;;;;;;;;;;;;;;AAQnD;uCAEe", "debugId": null}}, {"offset": {"line": 5291, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/ServiceScope.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { validateSection } from '@/utils/formValidation';\n\ninterface ServiceScopeProps {\n  formData: any;\n  onChange: (field: string, value: any) => void;\n  onSave: (data: any) => Promise<string>;\n  errors: Record<string, string>;\n  applicationId?: string;\n  isLoading?: boolean;\n}\n\nconst ServiceScope: React.FC<ServiceScopeProps> = ({\n  formData,\n  onChange,\n  onSave,\n  errors,\n  applicationId,\n  isLoading = false\n}) => {\n  const [localData, setLocalData] = useState({\n    services_offered: '',\n    geographic_coverage: '',\n    service_categories: '',\n    target_customers: '',\n    service_capacity: '',\n    ...formData\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n\n  // Sync formData to localData only when formData changes and is different\n  useEffect(() => {\n    if (formData && Object.keys(formData).length > 0) {\n      setLocalData((prev: any) => {\n        const newData = { ...prev, ...formData };\n        // Only update if there are actual changes to prevent infinite loops\n        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);\n        return hasChanges ? newData : prev;\n      });\n    }\n  }, [formData]);\n\n  const handleLocalChange = useCallback((field: string, value: any) => {\n    setLocalData((prev: any) => ({ ...prev, [field]: value }));\n\n    // Call onChange with the field and value\n    if (onChange) {\n      onChange(field, value);\n    }\n\n    // Clear validation error for this field if it exists\n    if (validationErrors && validationErrors[field]) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));\n    }\n  }, [onChange, validationErrors]);\n\n  const validateForm = () => {\n    const validation = validateSection(localData, 'serviceScope');\n    setValidationErrors(validation.errors);\n    return validation.isValid;\n  };\n\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      await onSave(localData);\n      console.log('Service scope saved');\n    } catch (error) {\n      console.error('Error saving service scope:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Service Scope\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Define the scope of services you plan to offer and your coverage area.\n        </p>\n      </div>\n\n      <div className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Services Offered *\n          </label>\n          <textarea\n            value={localData.services_offered || ''}\n            onChange={(e) => handleLocalChange('services_offered', e.target.value)}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe the services you plan to offer...\"\n          />\n          {(validationErrors.services_offered || errors.services_offered) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.services_offered || errors.services_offered}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Geographic Coverage *\n          </label>\n          <textarea\n            value={localData.geographic_coverage || ''}\n            onChange={(e) => handleLocalChange('geographic_coverage', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe the geographic areas you plan to serve...\"\n          />\n          {(validationErrors.geographic_coverage || errors.geographic_coverage) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.geographic_coverage || errors.geographic_coverage}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Service Categories *\n          </label>\n          <textarea\n            value={localData.service_categories || ''}\n            onChange={(e) => handleLocalChange('service_categories', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"List and categorize your services...\"\n          />\n          {(validationErrors.service_categories || errors.service_categories) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.service_categories || errors.service_categories}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Target Customers *\n          </label>\n          <textarea\n            value={localData.target_customers || ''}\n            onChange={(e) => handleLocalChange('target_customers', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe your target customer segments...\"\n          />\n          {(validationErrors.target_customers || errors.target_customers) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.target_customers || errors.target_customers}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Service Capacity *\n          </label>\n          <textarea\n            value={localData.service_capacity || ''}\n            onChange={(e) => handleLocalChange('service_capacity', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe your capacity to deliver services...\"\n          />\n          {(validationErrors.service_capacity || errors.service_capacity) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.service_capacity || errors.service_capacity}\n            </p>\n          )}\n        </div>\n      </div>\n\n      {/* Save Button */}\n      <div className=\"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700\">\n        <button\n          onClick={handleSave}\n          disabled={isSaving || isLoading}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isSaving || isLoading ? (\n            <>\n              <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n              Saving...\n            </>\n          ) : (\n            <>\n              <i className=\"ri-save-line mr-2\"></i>\n              Save Service Scope\n            </>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default ServiceScope;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,EACb,YAAY,KAAK,EAClB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,kBAAkB;QAClB,qBAAqB;QACrB,oBAAoB;QACpB,kBAAkB;QAClB,kBAAkB;QAClB,GAAG,QAAQ;IACb;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yEAAyE;IACzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,GAAG;YAChD,aAAa,CAAC;gBACZ,MAAM,UAAU;oBAAE,GAAG,IAAI;oBAAE,GAAG,QAAQ;gBAAC;gBACvC,oEAAoE;gBACpE,MAAM,aAAa,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;gBAChF,OAAO,aAAa,UAAU;YAChC;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QACpD,aAAa,CAAC,OAAc,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAExD,yCAAyC;QACzC,IAAI,UAAU;YACZ,SAAS,OAAO;QAClB;QAEA,qDAAqD;QACrD,IAAI,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;YAC/C,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACjF;IACF,GAAG;QAAC;QAAU;KAAiB;IAE/B,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,oBAAoB,WAAW,MAAM;QACrC,OAAO,WAAW,OAAO;IAC3B;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,YAAY;QACZ,IAAI;YACF,MAAM,OAAO;YACb,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,YAAY;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAK/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,gBAAgB,IAAI;gCACrC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACrE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB,mBAC5D,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB;;;;;;;;;;;;kCAKnE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,mBAAmB,IAAI;gCACxC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;gCACxE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,mBAAmB,IAAI,OAAO,mBAAmB,mBAClE,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,mBAAmB,IAAI,OAAO,mBAAmB;;;;;;;;;;;;kCAKzE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,kBAAkB,IAAI;gCACvC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gCACvE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB,mBAChE,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;kCAKvE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,gBAAgB,IAAI;gCACrC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACrE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB,mBAC5D,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB;;;;;;;;;;;;kCAKnE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,gBAAgB,IAAI;gCACrC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACrE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB,mBAC5D,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;0BAOrE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,YAAY,0BACX;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAyC;;qDAIxD;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAwB;;;;;;;;;;;;;;;;;;;AAQnD;uCAEe", "debugId": null}}, {"offset": {"line": 5632, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/BusinessPlan.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { validateSection } from '@/utils/formValidation';\n\ninterface BusinessPlanProps {\n  formData: any;\n  onChange: (field: string, value: any) => void;\n  onSave: (data: any) => Promise<string>;\n  errors: Record<string, string>;\n  applicationId?: string;\n  isLoading?: boolean;\n}\n\nconst BusinessPlan: React.FC<BusinessPlanProps> = ({\n  formData,\n  onChange,\n  onSave,\n  errors,\n  applicationId,\n  isLoading = false\n}) => {\n  const [localData, setLocalData] = useState({\n    executive_summary: '',\n    market_analysis: '',\n    financial_projections: '',\n    revenue_model: '',\n    investment_requirements: '',\n    implementation_timeline: '',\n    risk_analysis: '',\n    success_metrics: '',\n    ...formData\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n\n  // Sync formData to localData only when formData changes and is different\n  useEffect(() => {\n    if (formData && Object.keys(formData).length > 0) {\n      setLocalData((prev: any) => {\n        const newData = { ...prev, ...formData };\n        // Only update if there are actual changes to prevent infinite loops\n        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);\n        return hasChanges ? newData : prev;\n      });\n    }\n  }, [formData]);\n\n  const handleLocalChange = useCallback((field: string, value: any) => {\n    setLocalData((prev: any) => ({ ...prev, [field]: value }));\n\n    // Call onChange with the field and value\n    if (onChange) {\n      onChange(field, value);\n    }\n\n    // Clear validation error for this field if it exists\n    if (validationErrors && validationErrors[field]) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));\n    }\n  }, [onChange, validationErrors]);\n\n  const validateForm = () => {\n    const validation = validateSection(localData, 'businessPlan');\n    setValidationErrors(validation.errors);\n    return validation.isValid;\n  };\n\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      await onSave(localData);\n      console.log('Business plan saved');\n    } catch (error) {\n      console.error('Error saving business plan:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Business Plan\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Provide your business plan including financial projections and strategy.\n        </p>\n      </div>\n\n      <div className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Executive Summary *\n          </label>\n          <textarea\n            value={localData.executive_summary || ''}\n            onChange={(e) => handleLocalChange('executive_summary', e.target.value)}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Provide a summary of your business plan...\"\n          />\n          {(validationErrors.executive_summary || errors.executive_summary) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.executive_summary || errors.executive_summary}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Market Analysis *\n          </label>\n          <textarea\n            value={localData.market_analysis || ''}\n            onChange={(e) => handleLocalChange('market_analysis', e.target.value)}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe your market analysis and opportunities...\"\n          />\n          {(validationErrors.market_analysis || errors.market_analysis) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.market_analysis || errors.market_analysis}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Financial Projections *\n          </label>\n          <textarea\n            value={localData.financial_projections || ''}\n            onChange={(e) => handleLocalChange('financial_projections', e.target.value)}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Provide your financial projections for the next 3-5 years...\"\n          />\n          {(validationErrors.financial_projections || errors.financial_projections) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.financial_projections || errors.financial_projections}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Revenue Model *\n          </label>\n          <textarea\n            value={localData.revenue_model || ''}\n            onChange={(e) => handleLocalChange('revenue_model', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe how your business will generate revenue...\"\n          />\n          {(validationErrors.revenue_model || errors.revenue_model) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.revenue_model || errors.revenue_model}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Investment Requirements *\n          </label>\n          <textarea\n            value={localData.investment_requirements || ''}\n            onChange={(e) => handleLocalChange('investment_requirements', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe your funding and investment requirements...\"\n          />\n          {(validationErrors.investment_requirements || errors.investment_requirements) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.investment_requirements || errors.investment_requirements}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Implementation Timeline *\n          </label>\n          <textarea\n            value={localData.implementation_timeline || ''}\n            onChange={(e) => handleLocalChange('implementation_timeline', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Provide your implementation timeline and milestones...\"\n          />\n          {(validationErrors.implementation_timeline || errors.implementation_timeline) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.implementation_timeline || errors.implementation_timeline}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Risk Analysis *\n          </label>\n          <textarea\n            value={localData.risk_analysis || ''}\n            onChange={(e) => handleLocalChange('risk_analysis', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Identify and analyze potential risks and mitigation strategies...\"\n          />\n          {(validationErrors.risk_analysis || errors.risk_analysis) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.risk_analysis || errors.risk_analysis}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Success Metrics *\n          </label>\n          <textarea\n            value={localData.success_metrics || ''}\n            onChange={(e) => handleLocalChange('success_metrics', e.target.value)}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Define how you will measure success and performance...\"\n          />\n          {(validationErrors.success_metrics || errors.success_metrics) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.success_metrics || errors.success_metrics}\n            </p>\n          )}\n        </div>\n      </div>\n\n      {/* Save Button */}\n      <div className=\"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700\">\n        <button\n          onClick={handleSave}\n          disabled={isSaving || isLoading}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isSaving || isLoading ? (\n            <>\n              <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n              Saving...\n            </>\n          ) : (\n            <>\n              <i className=\"ri-save-line mr-2\"></i>\n              Save Business Plan\n            </>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default BusinessPlan;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,EACb,YAAY,KAAK,EAClB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,mBAAmB;QACnB,iBAAiB;QACjB,uBAAuB;QACvB,eAAe;QACf,yBAAyB;QACzB,yBAAyB;QACzB,eAAe;QACf,iBAAiB;QACjB,GAAG,QAAQ;IACb;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yEAAyE;IACzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,GAAG;YAChD,aAAa,CAAC;gBACZ,MAAM,UAAU;oBAAE,GAAG,IAAI;oBAAE,GAAG,QAAQ;gBAAC;gBACvC,oEAAoE;gBACpE,MAAM,aAAa,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;gBAChF,OAAO,aAAa,UAAU;YAChC;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QACpD,aAAa,CAAC,OAAc,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAExD,yCAAyC;QACzC,IAAI,UAAU;YACZ,SAAS,OAAO;QAClB;QAEA,qDAAqD;QACrD,IAAI,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;YAC/C,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACjF;IACF,GAAG;QAAC;QAAU;KAAiB;IAE/B,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,oBAAoB,WAAW,MAAM;QACrC,OAAO,WAAW,OAAO;IAC3B;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,YAAY;QACZ,IAAI;YACF,MAAM,OAAO;YACb,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,YAAY;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAK/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,iBAAiB,IAAI;gCACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;gCACtE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB,mBAC9D,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;;;;;;;;;;;;kCAKrE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,eAAe,IAAI;gCACpC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCACpE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,eAAe,IAAI,OAAO,eAAe,mBAC1D,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,eAAe,IAAI,OAAO,eAAe;;;;;;;;;;;;kCAKjE,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,qBAAqB,IAAI;gCAC1C,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,EAAE,MAAM,CAAC,KAAK;gCAC1E,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,qBAAqB,IAAI,OAAO,qBAAqB,mBACtE,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,qBAAqB,IAAI,OAAO,qBAAqB;;;;;;;;;;;;kCAK7E,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,aAAa,IAAI;gCAClC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAClE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,aAAa,IAAI,OAAO,aAAa,mBACtD,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,aAAa,IAAI,OAAO,aAAa;;;;;;;;;;;;kCAK7D,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,uBAAuB,IAAI;gCAC5C,UAAU,CAAC,IAAM,kBAAkB,2BAA2B,EAAE,MAAM,CAAC,KAAK;gCAC5E,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,uBAAuB,IAAI,OAAO,uBAAuB,mBAC1E,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,uBAAuB,IAAI,OAAO,uBAAuB;;;;;;;;;;;;kCAKjF,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,uBAAuB,IAAI;gCAC5C,UAAU,CAAC,IAAM,kBAAkB,2BAA2B,EAAE,MAAM,CAAC,KAAK;gCAC5E,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,uBAAuB,IAAI,OAAO,uBAAuB,mBAC1E,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,uBAAuB,IAAI,OAAO,uBAAuB;;;;;;;;;;;;kCAKjF,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,aAAa,IAAI;gCAClC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAClE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,aAAa,IAAI,OAAO,aAAa,mBACtD,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,aAAa,IAAI,OAAO,aAAa;;;;;;;;;;;;kCAK7D,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,eAAe,IAAI;gCACpC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gCACpE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,eAAe,IAAI,OAAO,eAAe,mBAC1D,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,eAAe,IAAI,OAAO,eAAe;;;;;;;;;;;;;;;;;;0BAOnE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,YAAY,0BACX;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAyC;;qDAIxD;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAwB;;;;;;;;;;;;;;;;;;;AAQnD;uCAEe", "debugId": null}}, {"offset": {"line": 6081, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/LegalHistory.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport { validateSection } from '@/utils/formValidation';\n\ninterface LegalHistoryProps {\n  formData: any;\n  onChange: (field: string, value: any) => void;\n  onSave: (data: any) => Promise<string>;\n  errors: Record<string, string>;\n  applicationId?: string;\n  isLoading?: boolean;\n}\n\nconst LegalHistory: React.FC<LegalHistoryProps> = ({\n  formData,\n  onChange,\n  onSave,\n  errors,\n  applicationId,\n  isLoading = false\n}) => {\n  const [localData, setLocalData] = useState({\n    criminal_history: false,\n    criminal_details: '',\n    bankruptcy_history: false,\n    bankruptcy_details: '',\n    regulatory_actions: false,\n    regulatory_details: '',\n    litigation_history: false,\n    litigation_details: '',\n    compliance_record: '',\n    previous_licenses: '',\n    declaration_accepted: false,\n    ...formData\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n\n  // Sync formData to localData only when formData changes and is different\n  useEffect(() => {\n    if (formData && Object.keys(formData).length > 0) {\n      setLocalData((prev: any) => {\n        const newData = { ...prev, ...formData };\n        // Only update if there are actual changes to prevent infinite loops\n        const hasChanges = Object.keys(formData).some(key => prev[key] !== formData[key]);\n        return hasChanges ? newData : prev;\n      });\n    }\n  }, [formData]);\n\n  const handleLocalChange = useCallback((field: string, value: any) => {\n    setLocalData((prev: any) => ({ ...prev, [field]: value }));\n\n    // Call onChange with the field and value\n    if (onChange) {\n      onChange(field, value);\n    }\n\n    // Clear validation error for this field if it exists\n    if (validationErrors && validationErrors[field]) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));\n    }\n  }, [onChange, validationErrors]);\n\n  const validateForm = () => {\n    const validation = validateSection(localData, 'legalHistory');\n    setValidationErrors(validation.errors);\n    return validation.isValid;\n  };\n\n  const handleSave = async () => {\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSaving(true);\n    try {\n      await onSave(localData);\n      console.log('Legal history saved');\n    } catch (error) {\n      console.error('Error saving legal history:', error);\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Legal History & Compliance\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Please provide information about your legal and compliance history.\n        </p>\n      </div>\n\n      {/* Criminal History */}\n      <div className=\"space-y-4\">\n        <div>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={localData.criminal_history || false}\n              onChange={(e) => handleLocalChange('criminal_history', e.target.checked)}\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded\"\n            />\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n              I have a criminal history\n            </span>\n          </label>\n        </div>\n\n        {localData.criminal_history && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Criminal History Details *\n            </label>\n            <textarea\n              value={localData.criminal_details || ''}\n              onChange={(e) => handleLocalChange('criminal_details', e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Please provide details of your criminal history...\"\n            />\n            {(validationErrors.criminal_details || errors.criminal_details) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.criminal_details || errors.criminal_details}\n              </p>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Bankruptcy History */}\n      <div className=\"space-y-4\">\n        <div>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={localData.bankruptcy_history || false}\n              onChange={(e) => handleLocalChange('bankruptcy_history', e.target.checked)}\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded\"\n            />\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n              I have a bankruptcy history\n            </span>\n          </label>\n        </div>\n\n        {localData.bankruptcy_history && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Bankruptcy History Details *\n            </label>\n            <textarea\n              value={localData.bankruptcy_details || ''}\n              onChange={(e) => handleLocalChange('bankruptcy_details', e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Please provide details of your bankruptcy history...\"\n            />\n            {(validationErrors.bankruptcy_details || errors.bankruptcy_details) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.bankruptcy_details || errors.bankruptcy_details}\n              </p>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Regulatory Actions */}\n      <div className=\"space-y-4\">\n        <div>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={localData.regulatory_actions || false}\n              onChange={(e) => handleLocalChange('regulatory_actions', e.target.checked)}\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded\"\n            />\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n              I have been subject to regulatory actions\n            </span>\n          </label>\n        </div>\n\n        {localData.regulatory_actions && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Regulatory Actions Details *\n            </label>\n            <textarea\n              value={localData.regulatory_details || ''}\n              onChange={(e) => handleLocalChange('regulatory_details', e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Please provide details of regulatory actions...\"\n            />\n            {(validationErrors.regulatory_details || errors.regulatory_details) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.regulatory_details || errors.regulatory_details}\n              </p>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Litigation History */}\n      <div className=\"space-y-4\">\n        <div>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              checked={localData.litigation_history || false}\n              onChange={(e) => handleLocalChange('litigation_history', e.target.checked)}\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded\"\n            />\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n              I have been involved in litigation\n            </span>\n          </label>\n        </div>\n\n        {localData.litigation_history && (\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Litigation History Details *\n            </label>\n            <textarea\n              value={localData.litigation_details || ''}\n              onChange={(e) => handleLocalChange('litigation_details', e.target.value)}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Please provide details of litigation history...\"\n            />\n            {(validationErrors.litigation_details || errors.litigation_details) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.litigation_details || errors.litigation_details}\n              </p>\n            )}\n          </div>\n        )}\n      </div>\n\n      {/* Compliance Record */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Compliance Record *\n        </label>\n        <textarea\n          value={localData.compliance_record || ''}\n          onChange={(e) => handleLocalChange('compliance_record', e.target.value)}\n          rows={3}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"Describe your compliance record and any relevant certifications...\"\n        />\n        {(validationErrors.compliance_record || errors.compliance_record) && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {validationErrors.compliance_record || errors.compliance_record}\n          </p>\n        )}\n      </div>\n\n      {/* Previous Licenses */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Previous Licenses\n        </label>\n        <textarea\n          value={localData.previous_licenses || ''}\n          onChange={(e) => handleLocalChange('previous_licenses', e.target.value)}\n          rows={3}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n          placeholder=\"List any previous licenses held or applied for...\"\n        />\n        {(validationErrors.previous_licenses || errors.previous_licenses) && (\n          <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n            {validationErrors.previous_licenses || errors.previous_licenses}\n          </p>\n        )}\n      </div>\n\n      {/* Declaration */}\n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n        <div className=\"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg\">\n          <label className=\"flex items-start\">\n            <input\n              type=\"checkbox\"\n              checked={localData.declaration_accepted || false}\n              onChange={(e) => handleLocalChange('declaration_accepted', e.target.checked)}\n              className=\"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1\"\n            />\n            <span className=\"ml-2 text-sm text-gray-700 dark:text-gray-300\">\n              I declare that all information provided is true and accurate to the best of my knowledge. \n              I understand that providing false information may result in the rejection of my application \n              or revocation of any license granted. *\n            </span>\n          </label>\n          {(validationErrors.declaration_accepted || errors.declaration_accepted) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.declaration_accepted || errors.declaration_accepted}\n            </p>\n          )}\n        </div>\n      </div>\n\n      {/* Save Button */}\n      <div className=\"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700\">\n        <button\n          onClick={handleSave}\n          disabled={isSaving || isLoading}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isSaving || isLoading ? (\n            <>\n              <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n              Saving...\n            </>\n          ) : (\n            <>\n              <i className=\"ri-save-line mr-2\"></i>\n              Save Legal History\n            </>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default LegalHistory;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,QAAQ,EACR,MAAM,EACN,MAAM,EACN,aAAa,EACb,YAAY,KAAK,EAClB;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,kBAAkB;QAClB,kBAAkB;QAClB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,oBAAoB;QACpB,mBAAmB;QACnB,mBAAmB;QACnB,sBAAsB;QACtB,GAAG,QAAQ;IACb;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,yEAAyE;IACzE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,OAAO,IAAI,CAAC,UAAU,MAAM,GAAG,GAAG;YAChD,aAAa,CAAC;gBACZ,MAAM,UAAU;oBAAE,GAAG,IAAI;oBAAE,GAAG,QAAQ;gBAAC;gBACvC,oEAAoE;gBACpE,MAAM,aAAa,OAAO,IAAI,CAAC,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,CAAC,IAAI;gBAChF,OAAO,aAAa,UAAU;YAChC;QACF;IACF,GAAG;QAAC;KAAS;IAEb,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QACpD,aAAa,CAAC,OAAc,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QAExD,yCAAyC;QACzC,IAAI,UAAU;YACZ,SAAS,OAAO;QAClB;QAEA,qDAAqD;QACrD,IAAI,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;YAC/C,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACjF;IACF,GAAG;QAAC;QAAU;KAAiB;IAE/B,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,oBAAoB,WAAW,MAAM;QACrC,OAAO,WAAW,OAAO;IAC3B;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,YAAY;QACZ,IAAI;YACF,MAAM,OAAO;YACb,QAAQ,GAAG,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+BAA+B;QAC/C,SAAU;YACR,YAAY;QACd;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCACC,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,UAAU,gBAAgB,IAAI;oCACvC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,OAAO;oCACvE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;;;;;;oBAMnE,UAAU,gBAAgB,kBACzB,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,gBAAgB,IAAI;gCACrC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;gCACrE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB,mBAC5D,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;0BAQvE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCACC,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,UAAU,kBAAkB,IAAI;oCACzC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,OAAO;oCACzE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;;;;;;oBAMnE,UAAU,kBAAkB,kBAC3B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,kBAAkB,IAAI;gCACvC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gCACvE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB,mBAChE,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;0BAQ3E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCACC,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,UAAU,kBAAkB,IAAI;oCACzC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,OAAO;oCACzE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;;;;;;oBAMnE,UAAU,kBAAkB,kBAC3B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,kBAAkB,IAAI;gCACvC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gCACvE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB,mBAChE,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;0BAQ3E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCACC,cAAA,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,UAAU,kBAAkB,IAAI;oCACzC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,OAAO;oCACzE,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;;;;;;oBAMnE,UAAU,kBAAkB,kBAC3B,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,kBAAkB,IAAI;gCACvC,UAAU,CAAC,IAAM,kBAAkB,sBAAsB,EAAE,MAAM,CAAC,KAAK;gCACvE,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB,mBAChE,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,kBAAkB,IAAI,OAAO,kBAAkB;;;;;;;;;;;;;;;;;;0BAQ3E,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,iBAAiB,IAAI;wBACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACtE,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,CAAC,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB,mBAC9D,8OAAC;wBAAE,WAAU;kCACV,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;;;;;;;;;;;;0BAMrE,8OAAC;;kCACC,8OAAC;wBAAM,WAAU;kCAAkE;;;;;;kCAGnF,8OAAC;wBACC,OAAO,UAAU,iBAAiB,IAAI;wBACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wBACtE,MAAM;wBACN,WAAU;wBACV,aAAY;;;;;;oBAEb,CAAC,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB,mBAC9D,8OAAC;wBAAE,WAAU;kCACV,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;;;;;;;;;;;;0BAMrE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAM,WAAU;;8CACf,8OAAC;oCACC,MAAK;oCACL,SAAS,UAAU,oBAAoB,IAAI;oCAC3C,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,OAAO;oCAC3E,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CAAgD;;;;;;;;;;;;wBAMjE,CAAC,iBAAiB,oBAAoB,IAAI,OAAO,oBAAoB,mBACpE,8OAAC;4BAAE,WAAU;sCACV,iBAAiB,oBAAoB,IAAI,OAAO,oBAAoB;;;;;;;;;;;;;;;;;0BAO7E,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,YAAY,0BACX;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAyC;;qDAIxD;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAwB;;;;;;;;;;;;;;;;;;;AAQnD;uCAEe", "debugId": null}}, {"offset": {"line": 6673, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/ReviewSubmit.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\n\ninterface ReviewSubmitProps {\n  formData: any;\n  allFormData: any;\n  onChange: (field: string, value: any) => void;\n  onSave: (data: any) => Promise<string>;\n  onSubmit: () => Promise<void>;\n  errors: Record<string, string>;\n  applicationId?: string;\n  isLoading?: boolean;\n  isSubmitting?: boolean;\n}\n\nconst ReviewSubmit: React.FC<ReviewSubmitProps> = ({\n  formData,\n  allFormData,\n  onChange,\n  onSave,\n  onSubmit,\n  errors,\n  applicationId,\n  isLoading = false,\n  isSubmitting = false\n}) => {\n  const [isReviewing, setIsReviewing] = useState(false);\n\n  const handleSubmit = async () => {\n    setIsReviewing(true);\n    try {\n      await onSubmit();\n    } catch (error) {\n      console.error('Error submitting application:', error);\n    } finally {\n      setIsReviewing(false);\n    }\n  };\n\n  const renderSectionSummary = (title: string, data: any) => {\n    if (!data || Object.keys(data).length === 0) {\n      return null;\n    }\n\n    return (\n      <div className=\"bg-gray-50 dark:bg-gray-800 p-4 rounded-lg\">\n        <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-3\">\n          {title}\n        </h4>\n        <div className=\"space-y-2\">\n          {Object.entries(data).map(([key, value]) => {\n            if (!value || value === '' || value === false) return null;\n            \n            const label = key.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase());\n            \n            return (\n              <div key={key} className=\"flex flex-col sm:flex-row\">\n                <span className=\"text-sm font-medium text-gray-600 dark:text-gray-400 sm:w-1/3\">\n                  {label}:\n                </span>\n                <span className=\"text-sm text-gray-900 dark:text-gray-100 sm:w-2/3\">\n                  {typeof value === 'boolean' ? (value ? 'Yes' : 'No') : String(value)}\n                </span>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Review & Submit Application\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Please review all information before submitting your application.\n        </p>\n        {applicationId && (\n          <div className=\"mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg\">\n            <p className=\"text-sm text-green-700 dark:text-green-300\">\n              <i className=\"ri-check-line mr-1\"></i>\n              Application ID: {applicationId}\n            </p>\n          </div>\n        )}\n      </div>\n\n      {/* Application Summary */}\n      <div className=\"space-y-6\">\n        {renderSectionSummary('Applicant Information', allFormData.applicantInfo)}\n        {renderSectionSummary('Company Profile', allFormData.companyProfile)}\n        {renderSectionSummary('Business Information', allFormData.businessInfo)}\n        {renderSectionSummary('Service Scope', allFormData.serviceScope)}\n        {renderSectionSummary('Business Plan', allFormData.businessPlan)}\n        {renderSectionSummary('Legal History', allFormData.legalHistory)}\n      </div>\n\n      {/* Final Declaration */}\n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n        <div className=\"bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg\">\n          <h4 className=\"text-md font-medium text-blue-900 dark:text-blue-100 mb-2\">\n            <i className=\"ri-information-line mr-2\"></i>\n            Important Information\n          </h4>\n          <ul className=\"text-sm text-blue-800 dark:text-blue-200 space-y-1\">\n            <li>• Your application will be reviewed by MACRA within 30 business days</li>\n            <li>• You will receive email notifications about the status of your application</li>\n            <li>• Additional documentation may be requested during the review process</li>\n            <li>• Application fees are non-refundable</li>\n            <li>• You can track your application status in your dashboard</li>\n          </ul>\n        </div>\n      </div>\n\n      {/* Submit Button */}\n      <div className=\"flex justify-center pt-6 border-t border-gray-200 dark:border-gray-700\">\n        <button\n          onClick={handleSubmit}\n          disabled={isSubmitting || isReviewing || isLoading}\n          className=\"inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isSubmitting || isReviewing ? (\n            <>\n              <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n              Submitting Application...\n            </>\n          ) : (\n            <>\n              <i className=\"ri-send-plane-line mr-2\"></i>\n              Submit Application\n            </>\n          )}\n        </button>\n      </div>\n\n      {/* Warning */}\n      <div className=\"bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg\">\n        <div className=\"flex\">\n          <i className=\"ri-alert-line text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5\"></i>\n          <div>\n            <h4 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">\n              Before Submitting\n            </h4>\n            <p className=\"text-sm text-yellow-700 dark:text-yellow-300 mt-1\">\n              Please ensure all information is accurate and complete. Once submitted, \n              you will not be able to modify your application without contacting MACRA support.\n            </p>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default ReviewSubmit;\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAgBA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,WAAW,EACX,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,MAAM,EACN,aAAa,EACb,YAAY,KAAK,EACjB,eAAe,KAAK,EACrB;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,eAAe;QACnB,eAAe;QACf,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,eAAe;QACjB;IACF;IAEA,MAAM,uBAAuB,CAAC,OAAe;QAC3C,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,MAAM,MAAM,KAAK,GAAG;YAC3C,OAAO;QACT;QAEA,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAG,WAAU;8BACX;;;;;;8BAEH,8OAAC;oBAAI,WAAU;8BACZ,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;wBACrC,IAAI,CAAC,SAAS,UAAU,MAAM,UAAU,OAAO,OAAO;wBAEtD,MAAM,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;wBAExE,qBACE,8OAAC;4BAAc,WAAU;;8CACvB,8OAAC;oCAAK,WAAU;;wCACb;wCAAM;;;;;;;8CAET,8OAAC;oCAAK,WAAU;8CACb,OAAO,UAAU,YAAa,QAAQ,QAAQ,OAAQ,OAAO;;;;;;;2BALxD;;;;;oBASd;;;;;;;;;;;;IAIR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;oBAG5D,+BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAE,WAAU;;8CACX,8OAAC;oCAAE,WAAU;;;;;;gCAAyB;gCACrB;;;;;;;;;;;;;;;;;;0BAOzB,8OAAC;gBAAI,WAAU;;oBACZ,qBAAqB,yBAAyB,YAAY,aAAa;oBACvE,qBAAqB,mBAAmB,YAAY,cAAc;oBAClE,qBAAqB,wBAAwB,YAAY,YAAY;oBACrE,qBAAqB,iBAAiB,YAAY,YAAY;oBAC9D,qBAAqB,iBAAiB,YAAY,YAAY;oBAC9D,qBAAqB,iBAAiB,YAAY,YAAY;;;;;;;0BAIjE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;oCAAE,WAAU;;;;;;gCAA+B;;;;;;;sCAG9C,8OAAC;4BAAG,WAAU;;8CACZ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;8CACJ,8OAAC;8CAAG;;;;;;;;;;;;;;;;;;;;;;;0BAMV,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU,gBAAgB,eAAe;oBACzC,WAAU;8BAET,gBAAgB,4BACf;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAyC;;qDAIxD;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAA8B;;;;;;;;;;;;;0BAQnD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAA2D;;;;;;8CAGzE,8OAAC;oCAAE,WAAU;8CAAoD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7E;uCAEe", "debugId": null}}, {"offset": {"line": 6999, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/components/customer/application/steps/BusinessInfo.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect, useCallback } from 'react';\nimport TextInput from '@/components/common/TextInput';\nimport Select from '@/components/common/Select';\nimport { validateSection } from '@/utils/formValidation';\nimport { applicationFormDataService } from '@/services/applicationFormDataService';\nimport { applicationProgressService } from '@/services/applicationProgressService';\nimport { IndependentStepProps } from '../types';\n\ninterface BusinessInfoProps extends IndependentStepProps {}\n\nconst BusinessInfo: React.FC<BusinessInfoProps> = ({\n  applicationId,\n  licenseTypeId,\n  licenseCategoryId,\n  isEditMode = false,\n  onStepComplete,\n  onStepError,\n  onNavigate\n}) => {\n  const [localData, setLocalData] = useState({\n    business_model: '',\n    operational_structure: '',\n    target_market: '',\n    competitive_advantage: '',\n    facilities_description: '',\n    equipment_description: '',\n    operational_areas: '',\n    service_delivery_model: '',\n    quality_assurance: '',\n    customer_support: ''\n  });\n\n  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});\n  const [isSaving, setIsSaving] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);\n\n  // Load existing data when component mounts (for edit mode)\n  useEffect(() => {\n    const loadExistingData = async () => {\n      // Add more robust validation for applicationId\n      if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {\n        console.log('Loading existing business info data for application:', applicationId);\n        setIsLoading(true);\n        try {\n          const existingData = await applicationFormDataService.getFormSection(applicationId, 'businessInfo');\n          if (existingData && existingData.section_data && Object.keys(existingData.section_data).length > 0) {\n            console.log('Loaded existing business info data:', existingData.section_data);\n            setLocalData(prev => ({ ...prev, ...existingData.section_data }));\n          } else {\n            console.log('No existing business info data found for application:', applicationId);\n          }\n        } catch (error) {\n          console.error('Error loading existing business info data:', error);\n          // Don't call onStepError as it would hide the form\n          // Just log the error and continue with empty form\n          console.log('Continuing with empty form due to load error');\n        } finally {\n          setIsLoading(false);\n        }\n      } else {\n        console.log('Skipping business info data load - not in edit mode or invalid applicationId:', { isEditMode, applicationId });\n      }\n    };\n\n    loadExistingData();\n  }, [applicationId, isEditMode]);\n\n  // Handle local data changes\n  const handleLocalChange = useCallback((field: string, value: any) => {\n    setLocalData((prev: any) => ({ ...prev, [field]: value }));\n    setHasUnsavedChanges(true);\n\n    // Clear validation error for this field if it exists\n    if (validationErrors && validationErrors[field]) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));\n    }\n\n    // Clear save error when user starts making changes\n    if (validationErrors.save) {\n      setValidationErrors((prev: Record<string, string>) => ({ ...prev, save: '' }));\n    }\n  }, [validationErrors]);\n\n  // Validate form data\n  const validateForm = () => {\n    const validation = validateSection(localData, 'businessInfo');\n    const errors = validation.errors || {};\n    setValidationErrors(errors);\n    return validation.isValid;\n  };\n\n  // Save data to backend\n  const saveData = async () => {\n    const validation = validateSection(localData, 'businessInfo');\n    const errors = validation.errors || {};\n    setValidationErrors(errors);\n\n    if (!validation.isValid) {\n      // Don't call onStepError as it hides the form\n      // Instead, let the form show validation errors inline\n      console.log('Validation failed:', errors);\n      return false;\n    }\n\n    setIsSaving(true);\n    try {\n      if (applicationId && applicationId !== 'new') {\n        await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'businessInfo', localData);\n\n        // Mark step as completed in progress tracking\n        await applicationProgressService.markStepCompleted(applicationId, 'business-info', localData);\n\n        console.log('Business info data saved for application:', applicationId);\n      } else {\n        console.warn('No application ID available for saving business info');\n      }\n\n      setHasUnsavedChanges(false);\n      onStepComplete?.('business-info', localData);\n      return true;\n    } catch (error: any) {\n      console.error('Error saving business info data:', error);\n\n      // Extract meaningful error message\n      let errorMessage = 'Failed to save business information';\n      if (error.response?.data?.message) {\n        errorMessage = error.response.data.message;\n      } else if (error.message) {\n        errorMessage = error.message;\n      }\n\n      // Show error in the form itself instead of hiding the form\n      setValidationErrors(prev => ({ ...prev, save: errorMessage }));\n      return false;\n    } finally {\n      setIsSaving(false);\n    }\n  };\n\n  // Handle save button click\n  const handleSave = async () => {\n    await saveData();\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"border-b border-gray-200 dark:border-gray-700 pb-4\">\n        <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n          Business Information\n        </h3>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n          Describe your business operations, structure, and service delivery model.\n        </p>\n      </div>\n\n      {/* Business Model */}\n      <div className=\"space-y-6\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Business Model *\n          </label>\n          <textarea\n            value={localData.business_model || ''}\n            onChange={(e) => handleLocalChange('business_model', e.target.value)}\n            onBlur={() => handleBlur('business_model')}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe your business model, revenue streams, and value proposition...\"\n          />\n          {(validationErrors.business_model || errors.business_model) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.business_model || errors.business_model}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Operational Structure *\n          </label>\n          <textarea\n            value={localData.operational_structure || ''}\n            onChange={(e) => handleLocalChange('operational_structure', e.target.value)}\n            onBlur={() => handleBlur('operational_structure')}\n            rows={4}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe your organizational structure, departments, and operational processes...\"\n          />\n          {(validationErrors.operational_structure || errors.operational_structure) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.operational_structure || errors.operational_structure}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Target Market *\n          </label>\n          <textarea\n            value={localData.target_market || ''}\n            onChange={(e) => handleLocalChange('target_market', e.target.value)}\n            onBlur={() => handleBlur('target_market')}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"Describe your target customers and market segments...\"\n          />\n          {(validationErrors.target_market || errors.target_market) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.target_market || errors.target_market}\n            </p>\n          )}\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n            Competitive Advantage *\n          </label>\n          <textarea\n            value={localData.competitive_advantage || ''}\n            onChange={(e) => handleLocalChange('competitive_advantage', e.target.value)}\n            onBlur={() => handleBlur('competitive_advantage')}\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n            placeholder=\"What makes your business unique and competitive in the market...\"\n          />\n          {(validationErrors.competitive_advantage || errors.competitive_advantage) && (\n            <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n              {validationErrors.competitive_advantage || errors.competitive_advantage}\n            </p>\n          )}\n        </div>\n      </div>\n\n      {/* Facilities and Equipment */}\n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n        <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          Facilities and Equipment\n        </h4>\n        \n        <div className=\"space-y-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Facilities Description *\n            </label>\n            <textarea\n              value={localData.facilities_description || ''}\n              onChange={(e) => handleLocalChange('facilities_description', e.target.value)}\n              onBlur={() => handleBlur('facilities_description')}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Describe your office locations, facilities, and infrastructure...\"\n            />\n            {(validationErrors.facilities_description || errors.facilities_description) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.facilities_description || errors.facilities_description}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Equipment Description *\n            </label>\n            <textarea\n              value={localData.equipment_description || ''}\n              onChange={(e) => handleLocalChange('equipment_description', e.target.value)}\n              onBlur={() => handleBlur('equipment_description')}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"List and describe your equipment, technology, and tools...\"\n            />\n            {(validationErrors.equipment_description || errors.equipment_description) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.equipment_description || errors.equipment_description}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Operational Areas *\n            </label>\n            <textarea\n              value={localData.operational_areas || ''}\n              onChange={(e) => handleLocalChange('operational_areas', e.target.value)}\n              onBlur={() => handleBlur('operational_areas')}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Describe the geographic areas where you operate or plan to operate...\"\n            />\n            {(validationErrors.operational_areas || errors.operational_areas) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.operational_areas || errors.operational_areas}\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Service Delivery */}\n      <div className=\"border-t border-gray-200 dark:border-gray-700 pt-6\">\n        <h4 className=\"text-md font-medium text-gray-900 dark:text-gray-100 mb-4\">\n          Service Delivery\n        </h4>\n        \n        <div className=\"space-y-6\">\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Service Delivery Model *\n            </label>\n            <textarea\n              value={localData.service_delivery_model || ''}\n              onChange={(e) => handleLocalChange('service_delivery_model', e.target.value)}\n              onBlur={() => handleBlur('service_delivery_model')}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Describe how you deliver services to customers...\"\n            />\n            {(validationErrors.service_delivery_model || errors.service_delivery_model) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.service_delivery_model || errors.service_delivery_model}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Quality Assurance *\n            </label>\n            <textarea\n              value={localData.quality_assurance || ''}\n              onChange={(e) => handleLocalChange('quality_assurance', e.target.value)}\n              onBlur={() => handleBlur('quality_assurance')}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Describe your quality control and assurance processes...\"\n            />\n            {(validationErrors.quality_assurance || errors.quality_assurance) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.quality_assurance || errors.quality_assurance}\n              </p>\n            )}\n          </div>\n\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n              Customer Support *\n            </label>\n            <textarea\n              value={localData.customer_support || ''}\n              onChange={(e) => handleLocalChange('customer_support', e.target.value)}\n              onBlur={() => handleBlur('customer_support')}\n              rows={3}\n              className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100\"\n              placeholder=\"Describe your customer support and service processes...\"\n            />\n            {(validationErrors.customer_support || errors.customer_support) && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">\n                {validationErrors.customer_support || errors.customer_support}\n              </p>\n            )}\n          </div>\n        </div>\n      </div>\n\n      {/* Error Display */}\n      {validationErrors.save && (\n        <div className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\">\n          <div className=\"flex items-center\">\n            <i className=\"ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3\"></i>\n            <div>\n              <h3 className=\"text-sm font-medium text-red-800 dark:text-red-200\">\n                Error Saving Business Information\n              </h3>\n              <p className=\"text-red-700 dark:text-red-300 text-sm mt-1\">\n                {validationErrors.save}\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Save Button */}\n      <div className=\"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700\">\n        <button\n          onClick={handleSave}\n          disabled={isSaving || isLoading}\n          className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed\"\n        >\n          {isSaving || isLoading ? (\n            <>\n              <i className=\"ri-loader-4-line animate-spin mr-2\"></i>\n              Saving...\n            </>\n          ) : (\n            <>\n              <i className=\"ri-save-line mr-2\"></i>\n              Save Business Information\n            </>\n          )}\n        </button>\n      </div>\n    </div>\n  );\n};\n\nexport default BusinessInfo;\n\n// Create placeholder components for the remaining steps\nexport { default as ServiceScope } from './ServiceScope';\nexport { default as BusinessPlan } from './BusinessPlan';\nexport { default as LegalHistory } from './LegalHistory';\nexport { default as ReviewSubmit } from './ReviewSubmit';\n"], "names": [], "mappings": ";;;;AAEA;AAGA;AACA;AACA;AAqZA,wDAAwD;AACxD;AACA;AACA;AACA;AAhaA;;;;;;AAYA,MAAM,eAA4C,CAAC,EACjD,aAAa,EACb,aAAa,EACb,iBAAiB,EACjB,aAAa,KAAK,EAClB,cAAc,EACd,WAAW,EACX,UAAU,EACX;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,gBAAgB;QAChB,uBAAuB;QACvB,eAAe;QACf,uBAAuB;QACvB,wBAAwB;QACxB,uBAAuB;QACvB,mBAAmB;QACnB,wBAAwB;QACxB,mBAAmB;QACnB,kBAAkB;IACpB;IAEA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B,CAAC;IAClF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,2DAA2D;IAC3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,+CAA+C;YAC/C,IAAI,cAAc,iBAAiB,kBAAkB,SAAS,kBAAkB,eAAe,cAAc,IAAI,OAAO,IAAI;gBAC1H,QAAQ,GAAG,CAAC,wDAAwD;gBACpE,aAAa;gBACb,IAAI;oBACF,MAAM,eAAe,MAAM,6IAAA,CAAA,6BAA0B,CAAC,cAAc,CAAC,eAAe;oBACpF,IAAI,gBAAgB,aAAa,YAAY,IAAI,OAAO,IAAI,CAAC,aAAa,YAAY,EAAE,MAAM,GAAG,GAAG;wBAClG,QAAQ,GAAG,CAAC,uCAAuC,aAAa,YAAY;wBAC5E,aAAa,CAAA,OAAQ,CAAC;gCAAE,GAAG,IAAI;gCAAE,GAAG,aAAa,YAAY;4BAAC,CAAC;oBACjE,OAAO;wBACL,QAAQ,GAAG,CAAC,yDAAyD;oBACvE;gBACF,EAAE,OAAO,OAAO;oBACd,QAAQ,KAAK,CAAC,8CAA8C;oBAC5D,mDAAmD;oBACnD,kDAAkD;oBAClD,QAAQ,GAAG,CAAC;gBACd,SAAU;oBACR,aAAa;gBACf;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC,iFAAiF;oBAAE;oBAAY;gBAAc;YAC3H;QACF;QAEA;IACF,GAAG;QAAC;QAAe;KAAW;IAE9B,4BAA4B;IAC5B,MAAM,oBAAoB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC,OAAe;QACpD,aAAa,CAAC,OAAc,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAM,CAAC;QACxD,qBAAqB;QAErB,qDAAqD;QACrD,IAAI,oBAAoB,gBAAgB,CAAC,MAAM,EAAE;YAC/C,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,CAAC,MAAM,EAAE;gBAAG,CAAC;QACjF;QAEA,mDAAmD;QACnD,IAAI,iBAAiB,IAAI,EAAE;YACzB,oBAAoB,CAAC,OAAiC,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAG,CAAC;QAC9E;IACF,GAAG;QAAC;KAAiB;IAErB,qBAAqB;IACrB,MAAM,eAAe;QACnB,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,MAAM,UAAS,WAAW,MAAM,IAAI,CAAC;QACrC,oBAAoB;QACpB,OAAO,WAAW,OAAO;IAC3B;IAEA,uBAAuB;IACvB,MAAM,WAAW;QACf,MAAM,aAAa,CAAA,GAAA,8HAAA,CAAA,kBAAe,AAAD,EAAE,WAAW;QAC9C,MAAM,UAAS,WAAW,MAAM,IAAI,CAAC;QACrC,oBAAoB;QAEpB,IAAI,CAAC,WAAW,OAAO,EAAE;YACvB,8CAA8C;YAC9C,sDAAsD;YACtD,QAAQ,GAAG,CAAC,sBAAsB;YAClC,OAAO;QACT;QAEA,YAAY;QACZ,IAAI;YACF,IAAI,iBAAiB,kBAAkB,OAAO;gBAC5C,MAAM,6IAAA,CAAA,6BAA0B,CAAC,uBAAuB,CAAC,eAAe,gBAAgB;gBAExF,8CAA8C;gBAC9C,MAAM,6IAAA,CAAA,6BAA0B,CAAC,iBAAiB,CAAC,eAAe,iBAAiB;gBAEnF,QAAQ,GAAG,CAAC,6CAA6C;YAC3D,OAAO;gBACL,QAAQ,IAAI,CAAC;YACf;YAEA,qBAAqB;YACrB,iBAAiB,iBAAiB;YAClC,OAAO;QACT,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,oCAAoC;YAElD,mCAAmC;YACnC,IAAI,eAAe;YACnB,IAAI,MAAM,QAAQ,EAAE,MAAM,SAAS;gBACjC,eAAe,MAAM,QAAQ,CAAC,IAAI,CAAC,OAAO;YAC5C,OAAO,IAAI,MAAM,OAAO,EAAE;gBACxB,eAAe,MAAM,OAAO;YAC9B;YAEA,2DAA2D;YAC3D,oBAAoB,CAAA,OAAQ,CAAC;oBAAE,GAAG,IAAI;oBAAE,MAAM;gBAAa,CAAC;YAC5D,OAAO;QACT,SAAU;YACR,YAAY;QACd;IACF;IAEA,2BAA2B;IAC3B,MAAM,aAAa;QACjB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAuD;;;;;;kCAGrE,8OAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,cAAc,IAAI;gCACnC,UAAU,CAAC,IAAM,kBAAkB,kBAAkB,EAAE,MAAM,CAAC,KAAK;gCACnE,QAAQ,IAAM,WAAW;gCACzB,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,cAAc,IAAI,OAAO,cAAc,mBACxD,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,cAAc,IAAI,OAAO,cAAc;;;;;;;;;;;;kCAK/D,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,qBAAqB,IAAI;gCAC1C,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,EAAE,MAAM,CAAC,KAAK;gCAC1E,QAAQ,IAAM,WAAW;gCACzB,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,qBAAqB,IAAI,OAAO,qBAAqB,mBACtE,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,qBAAqB,IAAI,OAAO,qBAAqB;;;;;;;;;;;;kCAK7E,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,aAAa,IAAI;gCAClC,UAAU,CAAC,IAAM,kBAAkB,iBAAiB,EAAE,MAAM,CAAC,KAAK;gCAClE,QAAQ,IAAM,WAAW;gCACzB,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,aAAa,IAAI,OAAO,aAAa,mBACtD,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,aAAa,IAAI,OAAO,aAAa;;;;;;;;;;;;kCAK7D,8OAAC;;0CACC,8OAAC;gCAAM,WAAU;0CAAkE;;;;;;0CAGnF,8OAAC;gCACC,OAAO,UAAU,qBAAqB,IAAI;gCAC1C,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,EAAE,MAAM,CAAC,KAAK;gCAC1E,QAAQ,IAAM,WAAW;gCACzB,MAAM;gCACN,WAAU;gCACV,aAAY;;;;;;4BAEb,CAAC,iBAAiB,qBAAqB,IAAI,OAAO,qBAAqB,mBACtE,8OAAC;gCAAE,WAAU;0CACV,iBAAiB,qBAAqB,IAAI,OAAO,qBAAqB;;;;;;;;;;;;;;;;;;0BAO/E,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAI1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,UAAU,sBAAsB,IAAI;wCAC3C,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;wCAC3E,QAAQ,IAAM,WAAW;wCACzB,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;oCAEb,CAAC,iBAAiB,sBAAsB,IAAI,OAAO,sBAAsB,mBACxE,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,sBAAsB,IAAI,OAAO,sBAAsB;;;;;;;;;;;;0CAK/E,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,UAAU,qBAAqB,IAAI;wCAC1C,UAAU,CAAC,IAAM,kBAAkB,yBAAyB,EAAE,MAAM,CAAC,KAAK;wCAC1E,QAAQ,IAAM,WAAW;wCACzB,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;oCAEb,CAAC,iBAAiB,qBAAqB,IAAI,OAAO,qBAAqB,mBACtE,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,qBAAqB,IAAI,OAAO,qBAAqB;;;;;;;;;;;;0CAK7E,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,UAAU,iBAAiB,IAAI;wCACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wCACtE,QAAQ,IAAM,WAAW;wCACzB,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;oCAEb,CAAC,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB,mBAC9D,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;0BAQzE,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAI1E,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,UAAU,sBAAsB,IAAI;wCAC3C,UAAU,CAAC,IAAM,kBAAkB,0BAA0B,EAAE,MAAM,CAAC,KAAK;wCAC3E,QAAQ,IAAM,WAAW;wCACzB,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;oCAEb,CAAC,iBAAiB,sBAAsB,IAAI,OAAO,sBAAsB,mBACxE,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,sBAAsB,IAAI,OAAO,sBAAsB;;;;;;;;;;;;0CAK/E,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,UAAU,iBAAiB,IAAI;wCACtC,UAAU,CAAC,IAAM,kBAAkB,qBAAqB,EAAE,MAAM,CAAC,KAAK;wCACtE,QAAQ,IAAM,WAAW;wCACzB,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;oCAEb,CAAC,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB,mBAC9D,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,iBAAiB,IAAI,OAAO,iBAAiB;;;;;;;;;;;;0CAKrE,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAkE;;;;;;kDAGnF,8OAAC;wCACC,OAAO,UAAU,gBAAgB,IAAI;wCACrC,UAAU,CAAC,IAAM,kBAAkB,oBAAoB,EAAE,MAAM,CAAC,KAAK;wCACrE,QAAQ,IAAM,WAAW;wCACzB,MAAM;wCACN,WAAU;wCACV,aAAY;;;;;;oCAEb,CAAC,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB,mBAC5D,8OAAC;wCAAE,WAAU;kDACV,iBAAiB,gBAAgB,IAAI,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;YAQtE,iBAAiB,IAAI,kBACpB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;;;;;;sCACb,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAqD;;;;;;8CAGnE,8OAAC;oCAAE,WAAU;8CACV,iBAAiB,IAAI;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,SAAS;oBACT,UAAU,YAAY;oBACtB,WAAU;8BAET,YAAY,0BACX;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAyC;;qDAIxD;;0CACE,8OAAC;gCAAE,WAAU;;;;;;4BAAwB;;;;;;;;;;;;;;;;;;;AAQnD;uCAEe", "debugId": null}}, {"offset": {"line": 7721, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Memory%20Business%20Solutioins/Projects/MACRA/project/Macra-Digital-Portal-Frontend/src/app/customer/applications/apply/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useRouter, useSearchParams } from 'next/navigation';\nimport CustomerLayout from '@/components/customer/CustomerLayout';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { licenseCategoryService, LicenseCategory } from '@/services/licenseCategoryService';\nimport { applicationService } from '@/services/applicationService';\nimport { applicantService } from '@/services/applicantService';\n\n// Import step components\nimport ApplicantInfo from '@/components/customer/application/steps/ApplicantInfo';\nimport CompanyProfile from '@/components/customer/application/steps/CompanyProfile';\nimport Management from '@/components/customer/application/steps/Management';\nimport ProfessionalServices from '@/components/customer/application/steps/ProfessionalServices';\nimport BusinessInfo from '@/components/customer/application/steps/BusinessInfo';\nimport ServiceScope from '@/components/customer/application/steps/ServiceScope';\nimport BusinessPlan from '@/components/customer/application/steps/BusinessPlan';\nimport LegalHistory from '@/components/customer/application/steps/LegalHistory';\nimport ReviewSubmit from '@/components/customer/application/steps/ReviewSubmit';\n\n// Define application steps\nconst APPLICATION_STEPS = [\n  { id: 'applicant-info', name: 'Applicant Information', component: ApplicantInfo },\n  { id: 'company-profile', name: 'Company Profile', component: CompanyProfile },\n  { id: 'management', name: 'Management Team', component: Management },\n  { id: 'professional-services', name: 'Professional Services', component: ProfessionalServices },\n  { id: 'business-info', name: 'Business Information', component: BusinessInfo },\n  { id: 'service-scope', name: 'Service Scope', component: ServiceScope },\n  { id: 'business-plan', name: 'Business Plan', component: BusinessPlan },\n  { id: 'legal-history', name: 'Legal History', component: LegalHistory },\n  { id: 'review-submit', name: 'Review & Submit', component: ReviewSubmit },\n];\n\nconst ApplicationFormPage: React.FC = () => {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const { isAuthenticated, loading: authLoading } = useAuth();\n\n  // Get query parameters\n  const licenseCategoryId = searchParams.get('license_category_id');\n  const applicationId = searchParams.get('application_id');\n  const stepParam = searchParams.get('step') || 'applicant-info';\n\n  // State\n  const [licenseCategory, setLicenseCategory] = useState<LicenseCategory | null>(null);\n  const [currentStepIndex, setCurrentStepIndex] = useState(0);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  // Find current step index\n  useEffect(() => {\n    const stepIndex = APPLICATION_STEPS.findIndex(step => step.id === stepParam);\n    setCurrentStepIndex(stepIndex >= 0 ? stepIndex : 0);\n  }, [stepParam]);\n\n  // Authentication check\n  useEffect(() => {\n    if (!authLoading && !isAuthenticated) {\n      router.push('/customer/auth/login');\n      return;\n    }\n  }, [isAuthenticated, authLoading, router]);\n\n  // Validate required parameters and load data\n  useEffect(() => {\n    const loadApplicationData = async () => {\n      if (!licenseCategoryId) {\n        setError('License category is required');\n        setLoading(false);\n        return;\n      }\n\n      try {\n        setLoading(true);\n        setError(null);\n\n        // Load license category data\n        const category = await licenseCategoryService.getLicenseCategory(licenseCategoryId);\n        if (!category) {\n          setError('License category not found');\n          setLoading(false);\n          return;\n        }\n\n        setLicenseCategory(category);\n\n        // If editing existing application, validate application exists\n        if (applicationId) {\n          try {\n            const application = await applicationService.getApplicationById(applicationId);\n            if (!application) {\n              setError('Application not found');\n              setLoading(false);\n              return;\n            }\n            // Validate that application belongs to the specified license category\n            if (application.license_category_id !== licenseCategoryId) {\n              setError('Application does not match the specified license category');\n              setLoading(false);\n              return;\n            }\n          } catch (err) {\n            console.error('Error loading application:', err);\n            setError('Failed to load application data');\n            setLoading(false);\n            return;\n          }\n        }\n\n        setLoading(false);\n      } catch (err) {\n        console.error('Error loading application data:', err);\n        setError('Failed to load application data');\n        setLoading(false);\n      }\n    };\n\n    if (isAuthenticated && !authLoading) {\n      loadApplicationData();\n    }\n  }, [licenseCategoryId, applicationId, isAuthenticated, authLoading]);\n\n  // Navigation functions\n  const updateUrlStep = (stepId: string) => {\n    const params = new URLSearchParams(searchParams.toString());\n    params.set('step', stepId);\n    router.push(`/customer/applications/apply?${params.toString()}`);\n  };\n\n  const handleNextStep = () => {\n    if (currentStepIndex < APPLICATION_STEPS.length - 1) {\n      const nextStep = APPLICATION_STEPS[currentStepIndex + 1];\n      updateUrlStep(nextStep.id);\n    }\n  };\n\n  const handlePreviousStep = () => {\n    if (currentStepIndex > 0) {\n      const prevStep = APPLICATION_STEPS[currentStepIndex - 1];\n      updateUrlStep(prevStep.id);\n    }\n  };\n\n  const handleStepClick = (stepIndex: number) => {\n    const step = APPLICATION_STEPS[stepIndex];\n    updateUrlStep(step.id);\n  };\n\n  const handleBackToApplications = () => {\n    router.push('/customer/applications');\n  };\n\n  // Loading state\n  if (authLoading || loading) {\n    return (\n      <CustomerLayout>\n        <div className=\"flex items-center justify-center min-h-96\">\n          <div className=\"text-center\">\n            <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4\"></div>\n            <p className=\"text-gray-600 dark:text-gray-400\">Loading application form...</p>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  // Error state\n  if (error) {\n    return (\n      <CustomerLayout>\n        <div className=\"flex items-center justify-center min-h-96\">\n          <div className=\"text-center\">\n            <div className=\"mb-4\">\n              <i className=\"ri-error-warning-line text-4xl text-red-500\"></i>\n            </div>\n            <h3 className=\"text-lg font-medium text-gray-900 dark:text-gray-100 mb-2\">\n              Error Loading Application\n            </h3>\n            <p className=\"text-gray-600 dark:text-gray-400 mb-6\">\n              {error}\n            </p>\n            <button\n              onClick={handleBackToApplications}\n              className=\"inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary\"\n            >\n              <i className=\"ri-arrow-left-line mr-2\"></i>\n              Back to Applications\n            </button>\n          </div>\n        </div>\n      </CustomerLayout>\n    );\n  }\n\n  const currentStep = APPLICATION_STEPS[currentStepIndex];\n  const CurrentStepComponent = currentStep.component;\n\n  return (\n    <CustomerLayout>\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Header */}\n        <div className=\"mb-8\">\n          <button\n            onClick={handleBackToApplications}\n            className=\"inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mb-4\"\n          >\n            <i className=\"ri-arrow-left-line mr-1\"></i>\n            Back to Applications\n          </button>\n          <h1 className=\"text-3xl font-bold text-gray-900 dark:text-gray-100\">\n            {licenseCategory?.name} Application\n          </h1>\n          <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n            {applicationId ? 'Edit your application' : 'Complete your license application'}\n          </p>\n        </div>\n\n        {/* Progress Steps */}\n        <div className=\"mb-8\">\n          <nav aria-label=\"Progress\">\n            <ol className=\"flex items-center\">\n              {APPLICATION_STEPS.map((step, index) => (\n                <li key={step.id} className={`relative ${index !== APPLICATION_STEPS.length - 1 ? 'pr-8 sm:pr-20' : ''}`}>\n                  {index !== APPLICATION_STEPS.length - 1 && (\n                    <div className=\"absolute inset-0 flex items-center\" aria-hidden=\"true\">\n                      <div className=\"h-0.5 w-full bg-gray-200 dark:bg-gray-700\" />\n                    </div>\n                  )}\n                  <button\n                    onClick={() => handleStepClick(index)}\n                    className={`relative w-8 h-8 flex items-center justify-center rounded-full border-2 ${\n                      index === currentStepIndex\n                        ? 'border-primary bg-primary text-white'\n                        : index < currentStepIndex\n                        ? 'border-primary bg-primary text-white'\n                        : 'border-gray-300 bg-white text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400'\n                    } hover:border-primary transition-colors`}\n                  >\n                    {index < currentStepIndex ? (\n                      <i className=\"ri-check-line text-sm\"></i>\n                    ) : (\n                      <span className=\"text-sm font-medium\">{index + 1}</span>\n                    )}\n                  </button>\n                  <span className=\"absolute top-10 left-1/2 transform -translate-x-1/2 text-xs font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap\">\n                    {step.name}\n                  </span>\n                </li>\n              ))}\n            </ol>\n          </nav>\n        </div>\n\n        {/* Current Step Content */}\n        <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700\">\n          <div className=\"px-6 py-4 border-b border-gray-200 dark:border-gray-700\">\n            <h2 className=\"text-lg font-medium text-gray-900 dark:text-gray-100\">\n              {currentStep.name}\n            </h2>\n          </div>\n\n          <div className=\"p-6\">\n            <CurrentStepComponent\n              licenseCategoryId={licenseCategoryId!}\n              applicationId={applicationId}\n              licenseTypeId={licenseCategory?.license_type_id}\n              isEditMode={!!applicationId}\n              onNext={handleNextStep}\n              onPrevious={handlePreviousStep}\n              isFirstStep={currentStepIndex === 0}\n              isLastStep={currentStepIndex === APPLICATION_STEPS.length - 1}\n            />\n          </div>\n        </div>\n      </div>\n    </CustomerLayout>\n  );\n};\n\nexport default ApplicationFormPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAGA,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAnBA;;;;;;;;;;;;;;;;;AAqBA,2BAA2B;AAC3B,MAAM,oBAAoB;IACxB;QAAE,IAAI;QAAkB,MAAM;QAAyB,WAAW,uKAAA,CAAA,UAAa;IAAC;IAChF;QAAE,IAAI;QAAmB,MAAM;QAAmB,WAAW,wKAAA,CAAA,UAAc;IAAC;IAC5E;QAAE,IAAI;QAAc,MAAM;QAAmB,WAAW,oKAAA,CAAA,UAAU;IAAC;IACnE;QAAE,IAAI;QAAyB,MAAM;QAAyB,WAAW,8KAAA,CAAA,UAAoB;IAAC;IAC9F;QAAE,IAAI;QAAiB,MAAM;QAAwB,WAAW,sLAAA,CAAA,UAAY;IAAC;IAC7E;QAAE,IAAI;QAAiB,MAAM;QAAiB,WAAW,sKAAA,CAAA,UAAY;IAAC;IACtE;QAAE,IAAI;QAAiB,MAAM;QAAiB,WAAW,sKAAA,CAAA,UAAY;IAAC;IACtE;QAAE,IAAI;QAAiB,MAAM;QAAiB,WAAW,sKAAA,CAAA,UAAY;IAAC;IACtE;QAAE,IAAI;QAAiB,MAAM;QAAmB,WAAW,sKAAA,CAAA,UAAY;IAAC;CACzE;AAED,MAAM,sBAAgC;IACpC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,EAAE,eAAe,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAExD,uBAAuB;IACvB,MAAM,oBAAoB,aAAa,GAAG,CAAC;IAC3C,MAAM,gBAAgB,aAAa,GAAG,CAAC;IACvC,MAAM,YAAY,aAAa,GAAG,CAAC,WAAW;IAE9C,QAAQ;IACR,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAA0B;IAC/E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,0BAA0B;IAC1B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,YAAY,kBAAkB,SAAS,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK;QAClE,oBAAoB,aAAa,IAAI,YAAY;IACnD,GAAG;QAAC;KAAU;IAEd,uBAAuB;IACvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,eAAe,CAAC,iBAAiB;YACpC,OAAO,IAAI,CAAC;YACZ;QACF;IACF,GAAG;QAAC;QAAiB;QAAa;KAAO;IAEzC,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,sBAAsB;YAC1B,IAAI,CAAC,mBAAmB;gBACtB,SAAS;gBACT,WAAW;gBACX;YACF;YAEA,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,6BAA6B;gBAC7B,MAAM,WAAW,MAAM,yIAAA,CAAA,yBAAsB,CAAC,kBAAkB,CAAC;gBACjE,IAAI,CAAC,UAAU;oBACb,SAAS;oBACT,WAAW;oBACX;gBACF;gBAEA,mBAAmB;gBAEnB,+DAA+D;gBAC/D,IAAI,eAAe;oBACjB,IAAI;wBACF,MAAM,cAAc,MAAM,qIAAA,CAAA,qBAAkB,CAAC,kBAAkB,CAAC;wBAChE,IAAI,CAAC,aAAa;4BAChB,SAAS;4BACT,WAAW;4BACX;wBACF;wBACA,sEAAsE;wBACtE,IAAI,YAAY,mBAAmB,KAAK,mBAAmB;4BACzD,SAAS;4BACT,WAAW;4BACX;wBACF;oBACF,EAAE,OAAO,KAAK;wBACZ,QAAQ,KAAK,CAAC,8BAA8B;wBAC5C,SAAS;wBACT,WAAW;wBACX;oBACF;gBACF;gBAEA,WAAW;YACb,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,mCAAmC;gBACjD,SAAS;gBACT,WAAW;YACb;QACF;QAEA,IAAI,mBAAmB,CAAC,aAAa;YACnC;QACF;IACF,GAAG;QAAC;QAAmB;QAAe;QAAiB;KAAY;IAEnE,uBAAuB;IACvB,MAAM,gBAAgB,CAAC;QACrB,MAAM,SAAS,IAAI,gBAAgB,aAAa,QAAQ;QACxD,OAAO,GAAG,CAAC,QAAQ;QACnB,OAAO,IAAI,CAAC,CAAC,6BAA6B,EAAE,OAAO,QAAQ,IAAI;IACjE;IAEA,MAAM,iBAAiB;QACrB,IAAI,mBAAmB,kBAAkB,MAAM,GAAG,GAAG;YACnD,MAAM,WAAW,iBAAiB,CAAC,mBAAmB,EAAE;YACxD,cAAc,SAAS,EAAE;QAC3B;IACF;IAEA,MAAM,qBAAqB;QACzB,IAAI,mBAAmB,GAAG;YACxB,MAAM,WAAW,iBAAiB,CAAC,mBAAmB,EAAE;YACxD,cAAc,SAAS,EAAE;QAC3B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,iBAAiB,CAAC,UAAU;QACzC,cAAc,KAAK,EAAE;IACvB;IAEA,MAAM,2BAA2B;QAC/B,OAAO,IAAI,CAAC;IACd;IAEA,gBAAgB;IAChB,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAE,WAAU;sCAAmC;;;;;;;;;;;;;;;;;;;;;;IAK1D;IAEA,cAAc;IACd,IAAI,OAAO;QACT,qBACE,8OAAC,gJAAA,CAAA,UAAc;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;;;;;;;;;;sCAEf,8OAAC;4BAAG,WAAU;sCAA4D;;;;;;sCAG1E,8OAAC;4BAAE,WAAU;sCACV;;;;;;sCAEH,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;;;;;;;;;;;;;;;;;;IAOvD;IAEA,MAAM,cAAc,iBAAiB,CAAC,iBAAiB;IACvD,MAAM,uBAAuB,YAAY,SAAS;IAElD,qBACE,8OAAC,gJAAA,CAAA,UAAc;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BACC,SAAS;4BACT,WAAU;;8CAEV,8OAAC;oCAAE,WAAU;;;;;;gCAA8B;;;;;;;sCAG7C,8OAAC;4BAAG,WAAU;;gCACX,iBAAiB;gCAAK;;;;;;;sCAEzB,8OAAC;4BAAE,WAAU;sCACV,gBAAgB,0BAA0B;;;;;;;;;;;;8BAK/C,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,cAAW;kCACd,cAAA,8OAAC;4BAAG,WAAU;sCACX,kBAAkB,GAAG,CAAC,CAAC,MAAM,sBAC5B,8OAAC;oCAAiB,WAAW,CAAC,SAAS,EAAE,UAAU,kBAAkB,MAAM,GAAG,IAAI,kBAAkB,IAAI;;wCACrG,UAAU,kBAAkB,MAAM,GAAG,mBACpC,8OAAC;4CAAI,WAAU;4CAAqC,eAAY;sDAC9D,cAAA,8OAAC;gDAAI,WAAU;;;;;;;;;;;sDAGnB,8OAAC;4CACC,SAAS,IAAM,gBAAgB;4CAC/B,WAAW,CAAC,wEAAwE,EAClF,UAAU,mBACN,yCACA,QAAQ,mBACR,yCACA,kGACL,uCAAuC,CAAC;sDAExC,QAAQ,iCACP,8OAAC;gDAAE,WAAU;;;;;qEAEb,8OAAC;gDAAK,WAAU;0DAAuB,QAAQ;;;;;;;;;;;sDAGnD,8OAAC;4CAAK,WAAU;sDACb,KAAK,IAAI;;;;;;;mCAvBL,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;8BAgCxB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX,YAAY,IAAI;;;;;;;;;;;sCAIrB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,mBAAmB;gCACnB,eAAe;gCACf,eAAe,iBAAiB;gCAChC,YAAY,CAAC,CAAC;gCACd,QAAQ;gCACR,YAAY;gCACZ,aAAa,qBAAqB;gCAClC,YAAY,qBAAqB,kBAAkB,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO1E;uCAEe", "debugId": null}}]}