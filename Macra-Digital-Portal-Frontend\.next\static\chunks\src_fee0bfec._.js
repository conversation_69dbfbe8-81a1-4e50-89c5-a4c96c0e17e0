(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/components/LogoutButton.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LogoutButton)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
function LogoutButton({ variant = 'primary', size = 'md', className = '', showConfirmation = true, redirectTo = '/auth/login', children }) {
    _s();
    const [isLoggingOut, setIsLoggingOut] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [showConfirm, setShowConfirm] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const { logout, user } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const handleLogout = async ()=>{
        if (showConfirmation && !showConfirm) {
            setShowConfirm(true);
            return;
        }
        setIsLoggingOut(true);
        try {
            // Call logout from context
            logout();
            // Small delay to ensure cleanup is complete
            await new Promise((resolve)=>setTimeout(resolve, 100));
            if (pathname.includes('customer')) {
                // Redirect to specified page
                router.push('/customer/auth/login');
            } else {
                router.push('/auth/login');
            }
        } catch (error) {
            console.error('Logout error:', error);
        } finally{
            setIsLoggingOut(false);
            setShowConfirm(false);
        }
    };
    const handleCancel = ()=>{
        setShowConfirm(false);
    };
    // Base styles for different variants
    const getVariantStyles = ()=>{
        switch(variant){
            case 'primary':
                return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';
            case 'secondary':
                return 'bg-white hover:bg-gray-50 text-red-600 border border-red-600';
            case 'text':
                return 'bg-transparent hover:bg-red-50 text-red-600 border-none';
            case 'icon':
                return 'bg-transparent hover:bg-red-50 text-red-600 border-none p-2';
            default:
                return 'bg-red-600 hover:bg-red-700 text-white border border-red-600';
        }
    };
    // Size styles
    const getSizeStyles = ()=>{
        switch(size){
            case 'sm':
                return 'px-3 py-1.5 text-sm';
            case 'md':
                return 'px-4 py-2 text-base';
            case 'lg':
                return 'px-6 py-3 text-lg';
            default:
                return 'px-4 py-2 text-base';
        }
    };
    const baseStyles = 'inline-flex items-center justify-center font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed';
    const buttonStyles = `${baseStyles} ${getVariantStyles()} ${variant !== 'icon' ? getSizeStyles() : ''} ${className}`;
    // Confirmation dialog
    if (showConfirm) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white rounded-lg p-6 max-w-sm mx-4 shadow-xl",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center mb-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-shrink-0",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    className: "h-6 w-6 text-red-600",
                                    fill: "none",
                                    viewBox: "0 0 24 24",
                                    stroke: "currentColor",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/LogoutButton.tsx",
                                        lineNumber: 108,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/LogoutButton.tsx",
                                    lineNumber: 107,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 106,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "ml-3",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-lg font-medium text-gray-900",
                                    children: "Confirm Logout"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/LogoutButton.tsx",
                                    lineNumber: 112,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 111,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 105,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mb-4",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-gray-500",
                            children: [
                                "Are you sure you want to logout",
                                user?.first_name ? `, ${user.first_name}` : '',
                                "? You will need to login again to access your account."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/LogoutButton.tsx",
                            lineNumber: 117,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 116,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex space-x-3",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleLogout,
                                disabled: isLoggingOut,
                                className: "flex-1 bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 disabled:opacity-50",
                                children: isLoggingOut ? 'Logging out...' : 'Yes, Logout'
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 123,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                onClick: handleCancel,
                                className: "flex-1 bg-gray-200 hover:bg-gray-300 text-gray-800 px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200",
                                children: "Cancel"
                            }, void 0, false, {
                                fileName: "[project]/src/components/LogoutButton.tsx",
                                lineNumber: 130,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 122,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/LogoutButton.tsx",
                lineNumber: 104,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/LogoutButton.tsx",
            lineNumber: 103,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
        onClick: handleLogout,
        disabled: isLoggingOut,
        className: buttonStyles,
        title: "Logout",
        children: children || /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
            children: variant === 'icon' ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                className: "h-5 w-5",
                fill: "none",
                viewBox: "0 0 24 24",
                stroke: "currentColor",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                }, void 0, false, {
                    fileName: "[project]/src/components/LogoutButton.tsx",
                    lineNumber: 153,
                    columnNumber: 15
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/LogoutButton.tsx",
                lineNumber: 152,
                columnNumber: 13
            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                        className: "h-4 w-4 mr-2",
                        fill: "none",
                        viewBox: "0 0 24 24",
                        stroke: "currentColor",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                            strokeLinecap: "round",
                            strokeLinejoin: "round",
                            strokeWidth: 2,
                            d: "M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                        }, void 0, false, {
                            fileName: "[project]/src/components/LogoutButton.tsx",
                            lineNumber: 158,
                            columnNumber: 17
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/LogoutButton.tsx",
                        lineNumber: 157,
                        columnNumber: 15
                    }, this),
                    isLoggingOut ? 'Logging out...' : 'Logout'
                ]
            }, void 0, true)
        }, void 0, false)
    }, void 0, false, {
        fileName: "[project]/src/components/LogoutButton.tsx",
        lineNumber: 143,
        columnNumber: 5
    }, this);
}
_s(LogoutButton, "GK6euFXnNSahEQDYKCx2YSuMdTI=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"]
    ];
});
_c = LogoutButton;
var _c;
__turbopack_context__.k.register(_c, "LogoutButton");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/CustomerLayout.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/LoadingContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LogoutButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/LogoutButton.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
const CustomerLayout = ({ children, breadcrumbs })=>{
    _s();
    const [isMobileSidebarOpen, setIsMobileSidebarOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isUserDropdownOpen, setIsUserDropdownOpen] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const pathname = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user, logout } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const { showLoader } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"])();
    // Memoize navigation items to prevent unnecessary re-renders
    const navigationItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CustomerLayout.useMemo[navigationItems]": ()=>[
                {
                    name: 'Dashboard',
                    href: '/customer',
                    icon: 'ri-dashboard-line',
                    current: pathname === '/customer'
                },
                {
                    name: 'My Licenses',
                    href: '/customer/my-licenses',
                    icon: 'ri-key-line',
                    current: pathname === '/customer/my-licenses'
                },
                {
                    name: 'New Applications',
                    href: '/customer/applications',
                    icon: 'ri-file-list-3-line',
                    current: pathname === '/customer/applications'
                },
                {
                    name: 'Payments',
                    href: '/customer/payments',
                    icon: 'ri-bank-card-line',
                    current: pathname === '/customer/payments'
                },
                {
                    name: 'Documents',
                    href: '/customer/documents',
                    icon: 'ri-file-text-line',
                    current: pathname === '/customer/documents'
                },
                {
                    name: 'Procurement',
                    href: '/customer/procurement',
                    icon: 'ri-auction-line',
                    current: pathname === '/customer/procurement'
                },
                {
                    name: 'Request Resource',
                    href: '/customer/resources',
                    icon: 'ri-hand-heart-line',
                    current: pathname === '/customer/resources'
                }
            ]
    }["CustomerLayout.useMemo[navigationItems]"], [
        pathname
    ]);
    const supportItems = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useMemo"])({
        "CustomerLayout.useMemo[supportItems]": ()=>[
                {
                    name: 'Consumer Affairs',
                    href: '/customer/consumer-affairs',
                    icon: 'ri-shield-user-line'
                },
                {
                    name: 'Help Center',
                    href: '/customer/help',
                    icon: 'ri-question-line'
                }
            ]
    }["CustomerLayout.useMemo[supportItems]"], []);
    // Prefetch customer pages on mount for faster navigation
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CustomerLayout.useEffect": ()=>{
            const prefetchPages = {
                "CustomerLayout.useEffect.prefetchPages": ()=>{
                    const customerPages = [
                        '/customer',
                        '/customer/applications',
                        '/customer/applications/standards',
                        '/customer/payments',
                        '/customer/my-licenses',
                        '/customer/procurement',
                        '/customer/profile',
                        '/customer/consumer-affairs',
                        '/customer/resources',
                        '/customer/help'
                    ];
                    customerPages.forEach({
                        "CustomerLayout.useEffect.prefetchPages": (page)=>{
                            router.prefetch(page);
                        }
                    }["CustomerLayout.useEffect.prefetchPages"]);
                }
            }["CustomerLayout.useEffect.prefetchPages"];
            // Delay prefetching to not interfere with initial page load
            const timer = setTimeout(prefetchPages, 1000);
            return ({
                "CustomerLayout.useEffect": ()=>clearTimeout(timer)
            })["CustomerLayout.useEffect"];
        }
    }["CustomerLayout.useEffect"], [
        router
    ]);
    const toggleMobileSidebar = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[toggleMobileSidebar]": ()=>{
            setIsMobileSidebarOpen(!isMobileSidebarOpen);
        }
    }["CustomerLayout.useCallback[toggleMobileSidebar]"], [
        isMobileSidebarOpen
    ]);
    const handleNavClick = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[handleNavClick]": (href, name)=>{
            const pageMessages = {
                '/customer': 'Loading Dashboard...',
                '/customer/licenses': 'Loading My Licenses...',
                '/customer/applications': 'Loading Applications...',
                '/customer/applications/standards': 'Loading Standards License Options...',
                '/customer/payments': 'Loading Payments...',
                '/customer/documents': 'Loading Documents...',
                '/customer/procurement': 'Loading Procurement...',
                '/customer/resources': 'Loading Resources...',
                '/customer/consumer-affairs': 'Loading Consumer Affairs...',
                '/customer/help': 'Loading Help Center...',
                '/customer/profile': 'Loading Profile...',
                '/customer/settings': 'Loading Settings...'
            };
            const message = pageMessages[href] || `Loading ${name}...`;
            showLoader(message);
            setIsMobileSidebarOpen(false);
        }
    }["CustomerLayout.useCallback[handleNavClick]"], [
        showLoader
    ]);
    const handleNavHover = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[handleNavHover]": (href)=>{
            // Prefetch on hover for instant navigation
            router.prefetch(href);
        }
    }["CustomerLayout.useCallback[handleNavHover]"], [
        router
    ]);
    const toggleUserDropdown = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "CustomerLayout.useCallback[toggleUserDropdown]": ()=>{
            setIsUserDropdownOpen(!isUserDropdownOpen);
        }
    }["CustomerLayout.useCallback[toggleUserDropdown]"], [
        isUserDropdownOpen
    ]);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "flex h-screen overflow-hidden",
        children: [
            isMobileSidebarOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden",
                onClick: ()=>setIsMobileSidebarOpen(false)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                lineNumber: 152,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("aside", {
                className: `
        fixed lg:static inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-lg transform transition-transform duration-300 ease-in-out gpu-accelerated
        ${isMobileSidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `,
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex flex-col h-full",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "h-16 flex items-center px-6 border-b border-gray-200 dark:border-gray-700",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: "/images/macra-logo.png",
                                    alt: "MACRA Logo",
                                    className: "max-h-12 w-auto",
                                    width: 120,
                                    height: 48,
                                    priority: true
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 167,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                lineNumber: 166,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 165,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                            className: "mt-6 px-4 side-nav",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "space-y-1",
                                    children: navigationItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                            href: item.href,
                                            onClick: ()=>handleNavClick(item.href, item.name),
                                            onMouseEnter: ()=>handleNavHover(item.href),
                                            className: `
                    flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated
                    ${item.current ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border-r-2 border-red-600 dark:border-red-400' : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100'}
                  `,
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: `w-5 h-5 flex items-center justify-center mr-3 ${item.current ? 'text-red-600 dark:text-red-400' : ''}`,
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: item.icon
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 197,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 196,
                                                    columnNumber: 19
                                                }, this),
                                                item.name
                                            ]
                                        }, item.name, true, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 183,
                                            columnNumber: 17
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 181,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "mt-8",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                            className: "px-4 text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wider",
                                            children: "Support"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 206,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "mt-2 space-y-1",
                                            children: supportItems.map((item)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                    href: item.href,
                                                    onClick: ()=>handleNavClick(item.href, item.name),
                                                    onMouseEnter: ()=>handleNavHover(item.href),
                                                    className: "flex items-center px-4 py-2.5 text-sm font-medium rounded-md transition-colors duration-200 gpu-accelerated text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 hover:text-gray-900 dark:hover:text-gray-100",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                            className: "w-5 h-5 flex items-center justify-center mr-3",
                                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                                className: item.icon
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                                lineNumber: 219,
                                                                columnNumber: 23
                                                            }, this)
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                            lineNumber: 218,
                                                            columnNumber: 21
                                                        }, this),
                                                        item.name
                                                    ]
                                                }, item.name, true, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 211,
                                                    columnNumber: 19
                                                }, this))
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 209,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 205,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 179,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute bottom-0 w-64 p-4 border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex items-center space-x-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        className: "h-10 w-10 rounded-full object-cover",
                                        src: user?.profile_image || "https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp",
                                        alt: "Profile",
                                        width: 40,
                                        height: 40
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 231,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                        href: "/customer/profile",
                                        className: "flex-1 min-w-0",
                                        onClick: ()=>handleNavClick('/customer/profile', 'Profile'),
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-sm font-medium text-gray-900 dark:text-gray-100 truncate",
                                                children: user ? `${user.first_name} ${user.last_name}` : 'Customer'
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                lineNumber: 243,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                className: "text-xs text-gray-500 dark:text-gray-400 truncate"
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                lineNumber: 246,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 238,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                lineNumber: 230,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 229,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                    lineNumber: 163,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                lineNumber: 159,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex-1 flex flex-col overflow-hidden",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("header", {
                        className: "bg-white dark:bg-gray-800 shadow-sm z-10",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex items-center justify-between h-16 px-4 sm:px-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    type: "button",
                                    onClick: toggleMobileSidebar,
                                    className: "md:hidden text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 focus:outline-none",
                                    "aria-label": "Open mobile menu",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-6 h-6 flex items-center justify-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                            className: "ri-menu-line ri-lg"
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 267,
                                            columnNumber: 17
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 266,
                                        columnNumber: 15
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 260,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex-1",
                                    children: breadcrumbs && breadcrumbs.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                                        className: "flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400",
                                        children: breadcrumbs.map((item, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"].Fragment, {
                                                children: [
                                                    index > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "ri-arrow-right-s-line"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 276,
                                                        columnNumber: 37
                                                    }, this),
                                                    item.href ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                        href: item.href,
                                                        className: "hover:text-primary",
                                                        children: item.label
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 278,
                                                        columnNumber: 25
                                                    }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-gray-900 dark:text-gray-100",
                                                        children: item.label
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 282,
                                                        columnNumber: 25
                                                    }, this)
                                                ]
                                            }, index, true, {
                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                lineNumber: 275,
                                                columnNumber: 21
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                        lineNumber: 273,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 271,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            type: "button",
                                            className: "flex-shrink-0 p-1 mr-4 text-gray-500 dark:text-gray-400 rounded-full hover:text-gray-700 dark:hover:text-gray-300 focus:outline-none relative",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "sr-only",
                                                    children: "View notifications"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 295,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "w-6 h-6 flex items-center justify-center",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                        className: "ri-notification-3-line ri-lg"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 297,
                                                        columnNumber: 19
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 296,
                                                    columnNumber: 17
                                                }, this),
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-500 ring-2 ring-white dark:ring-gray-800"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 299,
                                                    columnNumber: 17
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 291,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "relative",
                                            children: [
                                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                    type: "button",
                                                    onClick: toggleUserDropdown,
                                                    className: "flex items-center max-w-xs text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                                                    children: [
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "sr-only",
                                                            children: "Open user menu"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                            lineNumber: 308,
                                                            columnNumber: 19
                                                        }, this),
                                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            className: "h-8 w-8 rounded-full",
                                                            src: user?.profile_image || "https://banner2.cleanpng.com/20180728/sac/9128e319522f66aba9edf95180a86e7e.webp",
                                                            alt: "Profile",
                                                            width: 32,
                                                            height: 32
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                            lineNumber: 309,
                                                            columnNumber: 19
                                                        }, this)
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 303,
                                                    columnNumber: 17
                                                }, this),
                                                isUserDropdownOpen && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                    className: "absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white dark:bg-gray-800 ring-1 ring-black ring-opacity-5 dark:ring-gray-700 z-50",
                                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "py-1",
                                                        children: [
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                href: "/customer/profile",
                                                                className: "block px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",
                                                                onClick: ()=>{
                                                                    handleNavClick('/customer/profile', 'Profile');
                                                                    setIsUserDropdownOpen(false);
                                                                },
                                                                children: "Your Profile"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                                lineNumber: 321,
                                                                columnNumber: 23
                                                            }, this),
                                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$LogoutButton$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                                variant: "text",
                                                                size: "sm",
                                                                className: "w-full justify-start text-left text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700",
                                                                showConfirmation: true,
                                                                children: "Sign out"
                                                            }, void 0, false, {
                                                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                                lineNumber: 332,
                                                                columnNumber: 23
                                                            }, this)
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                        lineNumber: 320,
                                                        columnNumber: 21
                                                    }, this)
                                                }, void 0, false, {
                                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                                    lineNumber: 319,
                                                    columnNumber: 19
                                                }, this)
                                            ]
                                        }, void 0, true, {
                                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                            lineNumber: 302,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                    lineNumber: 290,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 259,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                        lineNumber: 258,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("main", {
                        className: "flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "py-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",
                                children: children
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                                lineNumber: 351,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                            lineNumber: 350,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                        lineNumber: 349,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/CustomerLayout.tsx",
                lineNumber: 256,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/CustomerLayout.tsx",
        lineNumber: 149,
        columnNumber: 5
    }, this);
};
_s(CustomerLayout, "YYYJNDbRlkZoLk1sqCOGBpf4yIY=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["usePathname"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$LoadingContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useLoading"]
    ];
});
_c = CustomerLayout;
const __TURBOPACK__default__export__ = CustomerLayout;
var _c;
__turbopack_context__.k.register(_c, "CustomerLayout");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/cacheService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Cache service for API responses to reduce rate limiting
__turbopack_context__.s({
    "CACHE_KEYS": (()=>CACHE_KEYS),
    "CACHE_TTL": (()=>CACHE_TTL),
    "cacheService": (()=>cacheService),
    "default": (()=>__TURBOPACK__default__export__)
});
class CacheService {
    cache = new Map();
    defaultTTL = 5 * 60 * 1000;
    /**
   * Set cache item with TTL
   */ set(key, data, ttl = this.defaultTTL) {
        const now = Date.now();
        const item = {
            data,
            timestamp: now,
            expiresAt: now + ttl
        };
        this.cache.set(key, item);
        console.log(`Cache SET: ${key} (expires in ${ttl}ms)`);
    }
    /**
   * Get cache item if not expired
   */ get(key) {
        const item = this.cache.get(key);
        if (!item) {
            console.log(`Cache MISS: ${key}`);
            return null;
        }
        const now = Date.now();
        if (now > item.expiresAt) {
            console.log(`Cache EXPIRED: ${key}`);
            this.cache.delete(key);
            return null;
        }
        console.log(`Cache HIT: ${key} (age: ${now - item.timestamp}ms)`);
        return item.data;
    }
    /**
   * Check if cache has valid item
   */ has(key) {
        return this.get(key) !== null;
    }
    /**
   * Delete cache item
   */ delete(key) {
        console.log(`Cache DELETE: ${key}`);
        return this.cache.delete(key);
    }
    /**
   * Clear all cache
   */ clear() {
        console.log('Cache CLEAR: All items');
        this.cache.clear();
    }
    /**
   * Get cache stats
   */ getStats() {
        return {
            size: this.cache.size,
            keys: Array.from(this.cache.keys())
        };
    }
    /**
   * Clean expired items
   */ cleanup() {
        const now = Date.now();
        let cleaned = 0;
        for (const [key, item] of this.cache.entries()){
            if (now > item.expiresAt) {
                this.cache.delete(key);
                cleaned++;
            }
        }
        if (cleaned > 0) {
            console.log(`Cache CLEANUP: Removed ${cleaned} expired items`);
        }
    }
    /**
   * Get or set pattern - fetch data if not cached
   */ async getOrSet(key, fetcher, ttl = this.defaultTTL) {
        // Try to get from cache first
        const cached = this.get(key);
        if (cached !== null) {
            return cached;
        }
        // Fetch fresh data
        console.log(`Cache FETCH: ${key}`);
        const data = await fetcher();
        // Store in cache
        this.set(key, data, ttl);
        return data;
    }
    /**
   * Invalidate cache by pattern
   */ invalidatePattern(pattern) {
        const regex = new RegExp(pattern);
        let invalidated = 0;
        for (const key of this.cache.keys()){
            if (regex.test(key)) {
                this.cache.delete(key);
                invalidated++;
            }
        }
        if (invalidated > 0) {
            console.log(`Cache INVALIDATE: Removed ${invalidated} items matching pattern: ${pattern}`);
        }
    }
}
const cacheService = new CacheService();
const CACHE_KEYS = {
    LICENSE_TYPES: 'license-types',
    LICENSE_CATEGORIES: 'license-categories',
    LICENSE_CATEGORIES_BY_TYPE: (typeId)=>`license-categories-type-${typeId}`,
    USER_APPLICATIONS: 'user-applications',
    APPLICATION: (id)=>`application-${id}`
};
const CACHE_TTL = {
    SHORT: 2 * 60 * 1000,
    MEDIUM: 5 * 60 * 1000,
    LONG: 15 * 60 * 1000,
    VERY_LONG: 60 * 60 * 1000
};
// Auto cleanup every 5 minutes
setInterval(()=>{
    cacheService.cleanup();
}, 5 * 60 * 1000);
const __TURBOPACK__default__export__ = cacheService;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/licenseCategoryService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "addCodesToCategories": (()=>addCodesToCategories),
    "findCategoryByCode": (()=>findCategoryByCode),
    "findCategoryById": (()=>findCategoryById),
    "generateCategoryCode": (()=>generateCategoryCode),
    "licenseCategoryService": (()=>licenseCategoryService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/cacheService.ts [app-client] (ecmascript)");
;
;
const generateCategoryCode = (name)=>{
    return name.toLowerCase().replace(/[^a-z0-9\s]/g, '') // Remove special characters
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
    .substring(0, 50); // Limit length
};
const addCodesToCategories = (categories)=>{
    return categories.map((category)=>({
            ...category,
            code: generateCategoryCode(category.name),
            children: category.children ? addCodesToCategories(category.children) : undefined
        }));
};
const findCategoryByCode = (categories, code)=>{
    for (const category of categories){
        if (category.code === code) {
            return category;
        }
        if (category.children) {
            const found = findCategoryByCode(category.children, code);
            if (found) return found;
        }
    }
    return null;
};
const findCategoryById = (categories, id)=>{
    for (const category of categories){
        if (category.license_category_id === id) {
            return category;
        }
        if (category.children) {
            const found = findCategoryById(category.children, id);
            if (found) return found;
        }
    }
    return null;
};
const licenseCategoryService = {
    // Get all license categories with pagination
    async getLicenseCategories (query = {}) {
        const params = new URLSearchParams();
        if (query.page) params.set('page', query.page.toString());
        if (query.limit) params.set('limit', query.limit.toString());
        if (query.search) params.set('search', query.search);
        if (query.sortBy) {
            query.sortBy.forEach((sort)=>params.append('sortBy', sort));
        }
        if (query.searchBy) {
            query.searchBy.forEach((search)=>params.append('searchBy', search));
        }
        if (query.filter) {
            Object.entries(query.filter).forEach(([key, value])=>{
                if (Array.isArray(value)) {
                    value.forEach((v)=>params.append(`filter.${key}`, v));
                } else {
                    params.set(`filter.${key}`, value);
                }
            });
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories?${params.toString()}`);
        return response.data;
    },
    // Get license category by ID
    async getLicenseCategory (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/${id}`);
        return response.data;
    },
    // Get license categories by license type
    async getLicenseCategoriesByType (licenseTypeId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`);
        return response.data;
    },
    // Create new license category
    async createLicenseCategory (licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/license-categories', licenseCategoryData);
        return response.data;
    },
    // Update license category
    async updateLicenseCategory (id, licenseCategoryData) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/license-categories/${id}`, licenseCategoryData);
        return response.data;
    },
    // Delete license category
    async deleteLicenseCategory (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/license-categories/${id}`);
        return response.data;
    },
    // Get all license categories (simple list for dropdowns) with caching
    async getAllLicenseCategories () {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_KEYS"].LICENSE_CATEGORIES, async ()=>{
            console.log('Fetching license categories from API...');
            // Reduce limit to avoid rate limiting
            const response = await this.getLicenseCategories({
                limit: 100
            });
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].LONG // Cache for 15 minutes
        );
    },
    // Get hierarchical tree of categories for a license type with caching
    async getCategoryTree (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`category-tree-${licenseTypeId}`, async ()=>{
            console.log(`Fetching category tree for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/tree`);
            return addCodesToCategories(response.data);
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get root categories (no parent) for a license type with caching
    async getRootCategories (licenseTypeId) {
        return __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["cacheService"].getOrSet(`root-categories-${licenseTypeId}`, async ()=>{
            console.log(`Fetching root categories for license type: ${licenseTypeId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/root`);
            return response.data;
        }, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$cacheService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["CACHE_TTL"].MEDIUM // Cache for 5 minutes
        );
    },
    // Get license categories for parent selection dropdown
    async getCategoriesForParentSelection (licenseTypeId, excludeId) {
        try {
            const params = excludeId ? {
                excludeId
            } : {};
            console.log('🚀 Fetching parent categories for license type:', licenseTypeId, 'with params:', params);
            // Try the new endpoint first
            try {
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/for-parent-selection`, {
                    params
                });
                if (response.data && Array.isArray(response.data.data)) {
                    console.log('✅ Valid array response with', response.data.data.length, 'items');
                    return response.data.data;
                } else {
                    console.warn('⚠️ API returned non-array data:', response.data);
                    return [];
                }
            } catch (newEndpointError) {
                console.warn('🔄 New endpoint failed, trying fallback:', newEndpointError);
                // Fallback to existing endpoint
                const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/by-license-type/${licenseTypeId}`);
                console.log('🔄 Fallback response:', response.data);
                if (response.data && Array.isArray(response.data)) {
                    // Filter out the excluded category if specified
                    let categories = response.data;
                    if (excludeId) {
                        categories = categories.filter((cat)=>cat.license_category_id !== excludeId);
                    }
                    console.log('✅ Fallback successful with', categories.length, 'items');
                    return categories;
                } else {
                    console.warn('⚠️ Fallback also returned non-array data:', response.data);
                    return [];
                }
            }
        } catch (error) {
            return [];
        }
    },
    // Get potential parent categories for a license type
    async getPotentialParents (licenseTypeId, excludeId) {
        const params = excludeId ? {
            excludeId
        } : {};
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/license-categories/license-type/${licenseTypeId}/potential-parents`, {
            params
        });
        return response.data;
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/applicationService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "applicationService": (()=>applicationService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
;
;
const applicationService = {
    // Get all applications with pagination and filters
    async getApplications (params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.sortBy) queryParams.append('sortBy', params.sortBy);
        if (params?.sortOrder) queryParams.append('sortOrder', params.sortOrder);
        // Add filters
        if (params?.filters?.licenseTypeId) {
            queryParams.append('filter.license_category.license_type_id', params.filters.licenseTypeId);
        }
        if (params?.filters?.licenseCategoryId) {
            queryParams.append('filter.license_category_id', params.filters.licenseCategoryId);
        }
        if (params?.filters?.status) {
            queryParams.append('filter.status', params.filters.status);
        }
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications?${queryParams.toString()}`);
        return response.data;
    },
    // Get applications by license type (through license category)
    async getApplicationsByLicenseType (licenseTypeId, params) {
        const queryParams = new URLSearchParams();
        if (params?.page) queryParams.append('page', params.page.toString());
        if (params?.limit) queryParams.append('limit', params.limit.toString());
        if (params?.search) queryParams.append('search', params.search);
        if (params?.status) queryParams.append('filter.status', params.status);
        // Filter by license type through license category
        queryParams.append('filter.license_category.license_type_id', licenseTypeId);
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications?${queryParams.toString()}`);
        return response.data;
    },
    // Get single application by ID
    async getApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${id}`);
        return response.data;
    },
    // Get applications by applicant
    async getApplicationsByApplicant (applicantId) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/by-applicant/${applicantId}`);
        return response.data;
    },
    // Get applications by status
    async getApplicationsByStatus (status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/by-status/${status}`);
        return response.data;
    },
    // Update application status
    async updateApplicationStatus (id, status) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}/status?status=${status}`);
        return response.data;
    },
    // Update application progress
    async updateApplicationProgress (id, currentStep, progressPercentage) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}/progress?currentStep=${currentStep}&progressPercentage=${progressPercentage}`);
        return response.data;
    },
    // Get application statistics
    async getApplicationStats () {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/applications/stats');
        return response.data;
    },
    // Create new application
    async createApplication (data) {
        console.log('ApplicationService.createApplication called with:', data);
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/applications', data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error('ApplicationService.createApplication error:', error);
            console.error('Error response:', error?.response?.data);
            throw error;
        }
    },
    // Update application
    async updateApplication (id, data) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${id}`, data);
        return response.data;
    },
    // Delete application
    async deleteApplication (id) {
        const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/applications/${id}`);
        return response.data;
    },
    // Create new application with applicant data
    async createApplicationWithApplicant (data) {
        try {
            console.log('Creating application with data:', data);
            // Generate proper application number (format: APP-YYYYMMDD-HHMMSS-XXX)
            const now = new Date();
            const dateStr = now.toISOString().slice(0, 10).replace(/-/g, '');
            const timeStr = now.toTimeString().slice(0, 8).replace(/:/g, '');
            const randomStr = Math.random().toString(36).substr(2, 3).toUpperCase();
            const applicationNumber = `APP-${dateStr}-${timeStr}-${randomStr}`;
            // Validate user_id is a proper UUID
            const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
            if (!uuidRegex.test(data.user_id)) {
                throw new Error(`Invalid user_id format: ${data.user_id}. Expected UUID format.`);
            }
            console.log('Creating application with:', {
                application_number: applicationNumber,
                applicant_id: data.user_id,
                license_category_id: data.license_category_id,
                status: 'draft'
            });
            // Create application using user_id as applicant_id
            // In most systems, the authenticated user is the applicant
            const application = await this.createApplication({
                application_number: applicationNumber,
                applicant_id: data.user_id,
                license_category_id: data.license_category_id,
                current_step: 1,
                progress_percentage: 14 // ~1/7 steps completed
            });
            console.log('Application created:', application);
            // Save applicant form data separately if needed
            try {
                const { applicationFormDataService } = await __turbopack_context__.r("[project]/src/services/applicationFormDataService.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                await applicationFormDataService.saveFormSection(application.application_id, 'applicantInfo', data.applicant_data);
                console.log('Applicant form data saved');
            } catch (formDataError) {
                console.warn('Could not save form data separately, continuing...', formDataError);
            // Continue even if form data saving fails
            }
            console.log('Application created successfully');
            return application;
        } catch (error) {
            console.error('Error creating application with applicant:', error);
            throw error;
        }
    },
    // Save application section data
    async saveApplicationSection (applicationId, sectionName, sectionData) {
        try {
            console.log(`Saving section ${sectionName} for application ${applicationId}:`, sectionData);
            // Try to save using form data service, but continue if it fails
            let completedSections = 1; // At least one section is being saved
            try {
                const { applicationFormDataService } = await __turbopack_context__.r("[project]/src/services/applicationFormDataService.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                // Save section data using form data service
                await applicationFormDataService.saveOrUpdateFormSection(applicationId, sectionName, sectionData);
                // Get all form data to calculate progress
                const allFormData = await applicationFormDataService.getApplicationFormData(applicationId);
                completedSections = Object.keys(allFormData).length;
                console.log(`Form data saved using form data service`);
            } catch (formDataError) {
                console.warn('Form data service not available, using basic progress tracking:', formDataError);
                // Fallback: estimate progress based on section name
                const sectionOrder = [
                    'applicantInfo',
                    'companyProfile',
                    'businessInfo',
                    'serviceScope',
                    'businessPlan',
                    'legalHistory',
                    'reviewSubmit'
                ];
                const sectionIndex = sectionOrder.indexOf(sectionName);
                completedSections = sectionIndex >= 0 ? sectionIndex + 1 : 1;
            }
            // Calculate progress based on completed sections
            const totalSections = 7; // Total number of form sections
            const progressPercentage = Math.round(completedSections / totalSections * 100);
            // Update the application progress
            await this.updateApplication(applicationId, {
                progress_percentage: progressPercentage,
                current_step: completedSections
            });
            console.log(`Section ${sectionName} saved successfully with ${progressPercentage}% progress`);
        } catch (error) {
            console.error(`Error saving section ${sectionName}:`, error);
            throw error;
        }
    },
    // Get application section data
    async getApplicationSection (applicationId, sectionName) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applications/${applicationId}/sections/${sectionName}`);
            return response.data;
        } catch (error) {
            console.error(`Error fetching section ${sectionName}:`, error);
            throw error;
        }
    },
    // Submit application for review
    async submitApplication (applicationId) {
        try {
            console.log('Submitting application:', applicationId);
            // Update application status to submitted and set submission date
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}`, {
                status: 'submitted',
                submitted_at: new Date().toISOString(),
                progress_percentage: 100,
                current_step: 7
            });
            console.log('Application submitted successfully:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error submitting application:', error);
            throw error;
        }
    },
    // Get user's applications
    async getUserApplications () {
        try {
            console.log('Fetching user applications...');
            // Get all applications - the backend should filter by authenticated user
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/applications');
            console.log('Applications API response:', response.data);
            // Handle different response structures
            let applications = [];
            if (response.data?.data) {
                applications = Array.isArray(response.data.data) ? response.data.data : [];
            } else if (Array.isArray(response.data)) {
                applications = response.data;
            } else if (response.data) {
                // Single application or other structure
                applications = [
                    response.data
                ];
            }
            console.log('Processed applications:', applications);
            return applications;
        } catch (error) {
            console.error('Error fetching user applications:', error);
            console.error('Error details:', {
                message: error?.message,
                response: error?.response?.data,
                status: error?.response?.status
            });
            throw error;
        }
    },
    // Save application as draft
    async saveAsDraft (applicationId, formData) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applications/${applicationId}`, {
                form_data: formData,
                status: 'draft'
            });
            return response.data;
        } catch (error) {
            console.error('Error saving application as draft:', error);
            throw error;
        }
    },
    // Validate application before submission
    async validateApplication (applicationId) {
        try {
            // Get form data from the form data service
            let formData = {};
            try {
                const { applicationFormDataService } = await __turbopack_context__.r("[project]/src/services/applicationFormDataService.ts [app-client] (ecmascript, async loader)")(__turbopack_context__.i);
                formData = await applicationFormDataService.getApplicationFormData(applicationId);
            } catch (formDataError) {
                console.warn('Could not retrieve form data for validation:', formDataError);
            // Continue with empty form data
            }
            const errors = [];
            const requiredSections = [
                'applicantInfo',
                'companyProfile',
                'businessInfo',
                'legalHistory'
            ];
            // Check if all required sections are completed
            for (const section of requiredSections){
                if (!formData[section] || Object.keys(formData[section]).length === 0) {
                    errors.push(`${section} section is incomplete`);
                }
            }
            return {
                isValid: errors.length === 0,
                errors
            };
        } catch (error) {
            console.error('Error validating application:', error);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/TextInput.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
;
const TextInput = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ label, error, helperText, required = false, className = '', containerClassName = '', id, ...props }, ref)=>{
    const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`;
    const baseInputClasses = `
    w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-offset-2 
    disabled:opacity-50 disabled:cursor-not-allowed
    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600
    transition-colors duration-200
  `;
    const inputClasses = error ? `${baseInputClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600` : `${baseInputClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `space-y-1 ${containerClassName}`,
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: inputId,
                className: "block text-sm font-medium text-gray-700 dark:text-gray-300",
                children: [
                    label,
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500 ml-1",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/TextInput.tsx",
                        lineNumber: 46,
                        columnNumber: 24
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/TextInput.tsx",
                lineNumber: 41,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                ref: ref,
                id: inputId,
                className: `${inputClasses} ${className}`,
                ...props
            }, void 0, false, {
                fileName: "[project]/src/components/common/TextInput.tsx",
                lineNumber: 50,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-red-600 dark:text-red-400 flex items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-error-warning-line mr-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/TextInput.tsx",
                        lineNumber: 59,
                        columnNumber: 11
                    }, this),
                    error
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/TextInput.tsx",
                lineNumber: 58,
                columnNumber: 9
            }, this),
            helperText && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-gray-500 dark:text-gray-400",
                children: helperText
            }, void 0, false, {
                fileName: "[project]/src/components/common/TextInput.tsx",
                lineNumber: 65,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/TextInput.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
});
_c1 = TextInput;
TextInput.displayName = 'TextInput';
const __TURBOPACK__default__export__ = TextInput;
var _c, _c1;
__turbopack_context__.k.register(_c, "TextInput$forwardRef");
__turbopack_context__.k.register(_c1, "TextInput");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/common/Select.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
'use client';
;
;
const Select = /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["forwardRef"])(_c = ({ label, error, helperText, required = false, options = [], placeholder = 'Select an option...', className = '', containerClassName = '', onChange, id, value, ...props }, ref)=>{
    const selectId = id || `select-${Math.random().toString(36).substr(2, 9)}`;
    const baseSelectClasses = `
    w-full px-3 py-2 border rounded-md shadow-sm 
    focus:outline-none focus:ring-2 focus:ring-offset-2 
    disabled:opacity-50 disabled:cursor-not-allowed
    dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600
    transition-colors duration-200
    appearance-none bg-white
    bg-no-repeat bg-right bg-[length:16px_16px]
    pr-10
  `;
    const selectClasses = error ? `${baseSelectClasses} border-red-300 focus:border-red-500 focus:ring-red-500 dark:border-red-600` : `${baseSelectClasses} border-gray-300 focus:border-primary focus:ring-primary dark:focus:border-primary dark:focus:ring-primary`;
    const handleChange = (e)=>{
        if (onChange) {
            onChange(e.target.value);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: `space-y-1 ${containerClassName}`,
        children: [
            label && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                htmlFor: selectId,
                className: "block text-sm font-medium text-gray-700 dark:text-gray-300",
                children: [
                    label,
                    required && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                        className: "text-red-500 ml-1",
                        children: "*"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 68,
                        columnNumber: 24
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 63,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                        ref: ref,
                        id: selectId,
                        value: value || '',
                        onChange: handleChange,
                        className: `${selectClasses} ${className}`,
                        ...props,
                        children: [
                            placeholder && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                value: "",
                                disabled: true,
                                children: placeholder
                            }, void 0, false, {
                                fileName: "[project]/src/components/common/Select.tsx",
                                lineNumber: 82,
                                columnNumber: 13
                            }, this),
                            options.map((option)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                    value: option.value,
                                    disabled: option.disabled,
                                    children: option.label
                                }, option.value, false, {
                                    fileName: "[project]/src/components/common/Select.tsx",
                                    lineNumber: 88,
                                    columnNumber: 13
                                }, this))
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 73,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-arrow-down-s-line text-gray-400 dark:text-gray-500"
                        }, void 0, false, {
                            fileName: "[project]/src/components/common/Select.tsx",
                            lineNumber: 100,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 99,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 72,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-red-600 dark:text-red-400 flex items-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                        className: "ri-error-warning-line mr-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/common/Select.tsx",
                        lineNumber: 106,
                        columnNumber: 11
                    }, this),
                    error
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 105,
                columnNumber: 9
            }, this),
            helperText && !error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                className: "text-sm text-gray-500 dark:text-gray-400",
                children: helperText
            }, void 0, false, {
                fileName: "[project]/src/components/common/Select.tsx",
                lineNumber: 112,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/common/Select.tsx",
        lineNumber: 61,
        columnNumber: 5
    }, this);
});
_c1 = Select;
Select.displayName = 'Select';
const __TURBOPACK__default__export__ = Select;
var _c, _c1;
__turbopack_context__.k.register(_c, "Select$forwardRef");
__turbopack_context__.k.register(_c1, "Select");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/formValidation.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Form validation utilities
__turbopack_context__.s({
    "commonRules": (()=>commonRules),
    "getFirstError": (()=>getFirstError),
    "hasErrors": (()=>hasErrors),
    "patterns": (()=>patterns),
    "validateArrayField": (()=>validateArrayField),
    "validateField": (()=>validateField),
    "validateFile": (()=>validateFile),
    "validateForm": (()=>validateForm),
    "validateSection": (()=>validateSection)
});
const validateField = (value, rules)=>{
    // Required validation
    if (rules.required && (!value || typeof value === 'string' && value.trim() === '')) {
        return 'This field is required';
    }
    // Skip other validations if field is empty and not required
    if (!value || typeof value === 'string' && value.trim() === '') {
        return null;
    }
    // String validations
    if (typeof value === 'string') {
        // Min length validation
        if (rules.minLength && value.length < rules.minLength) {
            return `Must be at least ${rules.minLength} characters`;
        }
        // Max length validation
        if (rules.maxLength && value.length > rules.maxLength) {
            return `Must be no more than ${rules.maxLength} characters`;
        }
        // Pattern validation
        if (rules.pattern && !rules.pattern.test(value)) {
            return 'Invalid format';
        }
    }
    // Custom validation
    if (rules.custom) {
        return rules.custom(value);
    }
    return null;
};
const validateForm = (data, rules)=>{
    const errors = {};
    Object.keys(rules).forEach((field)=>{
        const error = validateField(data[field], rules[field]);
        if (error) {
            errors[field] = error;
        }
    });
    return errors;
};
const patterns = {
    email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    phone: /^(\+265|0)[0-9]{8,9}$/,
    url: /^https?:\/\/.+/,
    alphanumeric: /^[a-zA-Z0-9]+$/,
    alphabetic: /^[a-zA-Z\s]+$/,
    numeric: /^[0-9]+$/,
    percentage: /^(100|[1-9]?[0-9])$/
};
const commonRules = {
    required: {
        required: true
    },
    email: {
        required: true,
        pattern: patterns.email,
        custom: (value)=>{
            if (value && !patterns.email.test(value)) {
                return 'Please enter a valid email address';
            }
            return null;
        }
    },
    phone: {
        required: true,
        pattern: patterns.phone,
        custom: (value)=>{
            if (value && !patterns.phone.test(value)) {
                return 'Please enter a valid Malawi phone number (e.g., +265123456789 or 0123456789)';
            }
            return null;
        }
    },
    businessRegistration: {
        required: true,
        minLength: 5,
        custom: (value)=>{
            if (value && value.length < 5) {
                return 'Business registration number must be at least 5 characters';
            }
            return null;
        }
    },
    percentage: {
        pattern: patterns.percentage,
        custom: (value)=>{
            if (value && !patterns.percentage.test(value)) {
                return 'Please enter a valid percentage (0-100)';
            }
            return null;
        }
    }
};
const hasErrors = (errors)=>{
    return Object.keys(errors).length > 0;
};
const getFirstError = (errors)=>{
    const firstKey = Object.keys(errors)[0];
    return firstKey ? errors[firstKey] : null;
};
const validateArrayField = (array, rules, minItems, maxItems)=>{
    const arrayErrors = {};
    // Check array length
    if (minItems && array.length < minItems) {
        arrayErrors._array = {
            length: `Must have at least ${minItems} items`
        };
    }
    if (maxItems && array.length > maxItems) {
        arrayErrors._array = {
            length: `Must have no more than ${maxItems} items`
        };
    }
    // Validate each item in array
    array.forEach((item, index)=>{
        const itemErrors = validateForm(item, rules);
        if (hasErrors(itemErrors)) {
            arrayErrors[index] = itemErrors;
        }
    });
    return arrayErrors;
};
const validateFile = (file, required = false, maxSize = 10, allowedTypes = [
    '.pdf'
])=>{
    if (required && !file) {
        return 'File is required';
    }
    if (!file) {
        return null;
    }
    // Check file size
    if (file.size > maxSize * 1024 * 1024) {
        return `File size must be less than ${maxSize}MB`;
    }
    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
        return `File type must be: ${allowedTypes.join(', ')}`;
    }
    return null;
};
const validateSection = (data, sectionName)=>{
    const errors = {};
    switch(sectionName){
        case 'applicantInfo':
            // Required fields for applicant info
            const applicantRequiredFields = [
                'applicant_type',
                'first_name',
                'last_name',
                'email',
                'phone',
                'national_id',
                'date_of_birth',
                'nationality',
                'gender',
                'postal_address',
                'physical_address',
                'city',
                'district'
            ];
            applicantRequiredFields.forEach((field)=>{
                if (!data[field] || typeof data[field] === 'string' && data[field].trim() === '') {
                    errors[field] = `${field.replace(/_/g, ' ')} is required`;
                }
            });
            // Email validation
            if (data.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.email)) {
                errors.email = 'Please enter a valid email address';
            }
            // Phone validation
            if (data.phone && !/^(\+265|0)?[1-9]\d{7,8}$/.test(data.phone)) {
                errors.phone = 'Please enter a valid phone number';
            }
            break;
        case 'companyProfile':
            const companyRequiredFields = [
                'company_name',
                'business_registration_number',
                'tax_number',
                'company_type',
                'incorporation_date',
                'incorporation_place',
                'company_email',
                'company_phone',
                'company_address',
                'company_city',
                'company_district',
                'number_of_employees',
                'annual_revenue',
                'business_description'
            ];
            companyRequiredFields.forEach((field)=>{
                if (!data[field] || typeof data[field] === 'string' && data[field].trim() === '') {
                    errors[field] = `${field.replace(/_/g, ' ')} is required`;
                }
            });
            // Email validation
            if (data.company_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(data.company_email)) {
                errors.company_email = 'Please enter a valid email address';
            }
            break;
        case 'businessInfo':
            const businessRequiredFields = [
                'business_model',
                'operational_structure',
                'target_market',
                'competitive_advantage',
                'facilities_description',
                'equipment_description',
                'operational_areas',
                'service_delivery_model',
                'quality_assurance',
                'customer_support'
            ];
            businessRequiredFields.forEach((field)=>{
                if (!data[field] || typeof data[field] === 'string' && data[field].trim() === '') {
                    errors[field] = `${field.replace(/_/g, ' ')} is required`;
                }
            });
            break;
        case 'serviceScope':
            const serviceScopeRequiredFields = [
                'services_offered',
                'geographic_coverage',
                'service_categories',
                'target_customers',
                'service_capacity'
            ];
            serviceScopeRequiredFields.forEach((field)=>{
                if (!data[field] || typeof data[field] === 'string' && data[field].trim() === '') {
                    errors[field] = `${field.replace(/_/g, ' ')} is required`;
                }
            });
            break;
        case 'businessPlan':
            const businessPlanRequiredFields = [
                'executive_summary',
                'market_analysis',
                'financial_projections',
                'revenue_model',
                'investment_requirements',
                'implementation_timeline',
                'risk_analysis',
                'success_metrics'
            ];
            businessPlanRequiredFields.forEach((field)=>{
                if (!data[field] || typeof data[field] === 'string' && data[field].trim() === '') {
                    errors[field] = `${field.replace(/_/g, ' ')} is required`;
                }
            });
            break;
        case 'legalHistory':
            // Required fields
            if (!data.compliance_record || data.compliance_record.trim() === '') {
                errors.compliance_record = 'Compliance record is required';
            }
            // Declaration must be accepted
            if (!data.declaration_accepted) {
                errors.declaration_accepted = 'You must accept the declaration to proceed';
            }
            // Conditional validations
            if (data.criminal_history && (!data.criminal_details || data.criminal_details.trim() === '')) {
                errors.criminal_details = 'Please provide details of your criminal history';
            }
            if (data.bankruptcy_history && (!data.bankruptcy_details || data.bankruptcy_details.trim() === '')) {
                errors.bankruptcy_details = 'Please provide details of your bankruptcy history';
            }
            if (data.regulatory_actions && (!data.regulatory_details || data.regulatory_details.trim() === '')) {
                errors.regulatory_details = 'Please provide details of regulatory actions';
            }
            if (data.litigation_history && (!data.litigation_details || data.litigation_details.trim() === '')) {
                errors.litigation_details = 'Please provide details of litigation history';
            }
            break;
        default:
            break;
    }
    return {
        isValid: Object.keys(errors).length === 0,
        errors
    };
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/applicationFormDataService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "applicationFormDataService": (()=>applicationFormDataService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/authUtils.ts [app-client] (ecmascript)");
;
;
const applicationFormDataService = {
    // Save form section data
    async saveFormSection (applicationId, sectionName, sectionData) {
        // Validate inputs before making API call
        if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {
            throw new Error(`Invalid applicationId provided to saveFormSection: ${applicationId}`);
        }
        if (!sectionName || sectionName.trim() === '') {
            throw new Error(`Invalid sectionName provided to saveFormSection: ${sectionName}`);
        }
        try {
            console.log(`Saving form section ${sectionName} for application ${applicationId}:`, sectionData);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/application-form-data', {
                application_id: applicationId,
                section_name: sectionName,
                section_data: sectionData,
                completed: true
            });
            console.log(`Form section ${sectionName} saved successfully:`, response.data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error(`Error saving form section ${sectionName}:`, error);
            throw error;
        }
    },
    // Update existing form section data
    async updateFormSection (applicationId, sectionName, sectionData) {
        // Validate inputs before making API call
        if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {
            throw new Error(`Invalid applicationId provided to updateFormSection: ${applicationId}`);
        }
        if (!sectionName || sectionName.trim() === '') {
            throw new Error(`Invalid sectionName provided to updateFormSection: ${sectionName}`);
        }
        try {
            console.log(`Updating form section ${sectionName} for application ${applicationId}:`, sectionData);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/application-form-data/${applicationId}/${sectionName}`, {
                section_data: sectionData,
                completed: true
            });
            console.log(`Form section ${sectionName} updated successfully:`, response.data);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            console.error(`Error updating form section ${sectionName}:`, error);
            throw error;
        }
    },
    // Get form section data
    async getFormSection (applicationId, sectionName) {
        // Validate inputs before making API call
        if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {
            console.warn('Invalid applicationId provided to getFormSection:', applicationId);
            return null;
        }
        if (!sectionName || sectionName.trim() === '') {
            console.warn('Invalid sectionName provided to getFormSection:', sectionName);
            return null;
        }
        try {
            console.log(`Fetching form section ${sectionName} for application ${applicationId}`);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/application-form-data/${applicationId}/${sectionName}`);
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
        } catch (error) {
            if (error?.response?.status === 404) {
                console.log(`Form section ${sectionName} not found for application ${applicationId} - this is normal for new applications`);
                return null; // Section doesn't exist yet
            }
            console.error(`Error fetching form section ${sectionName} for application ${applicationId}:`, error);
            throw error;
        }
    },
    // Get all form data for an application
    async getApplicationFormData (applicationId) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/application-form-data/${applicationId}`);
            const processedResponse = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$authUtils$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["processApiResponse"])(response);
            // Convert array of sections to object
            const formData = {};
            if (Array.isArray(processedResponse)) {
                processedResponse.forEach((section)=>{
                    formData[section.section_name] = section.section_data;
                });
            }
            return formData;
        } catch (error) {
            console.error('Error fetching application form data:', error);
            return {}; // Return empty object if no data found
        }
    },
    // Delete form section
    async deleteFormSection (applicationId, sectionName) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/application-form-data/${applicationId}/${sectionName}`);
            return response.data;
        } catch (error) {
            console.error(`Error deleting form section ${sectionName}:`, error);
            throw error;
        }
    },
    // Save or update form section (upsert)
    async saveOrUpdateFormSection (applicationId, sectionName, sectionData) {
        // Validate inputs before proceeding
        if (!applicationId || applicationId === 'undefined' || applicationId === 'new' || applicationId.trim() === '') {
            throw new Error(`Invalid applicationId provided to saveOrUpdateFormSection: ${applicationId}`);
        }
        if (!sectionName || sectionName.trim() === '') {
            throw new Error(`Invalid sectionName provided to saveOrUpdateFormSection: ${sectionName}`);
        }
        try {
            console.log(`Saving/updating form section ${sectionName} for application ${applicationId}`);
            // Try to get existing section first
            const existingSection = await this.getFormSection(applicationId, sectionName);
            if (existingSection) {
                // Update existing section
                console.log(`Updating existing section ${sectionName}`);
                return await this.updateFormSection(applicationId, sectionName, sectionData);
            } else {
                // Create new section
                console.log(`Creating new section ${sectionName}`);
                return await this.saveFormSection(applicationId, sectionName, sectionData);
            }
        } catch (error) {
            console.error(`Error saving/updating form section ${sectionName}:`, error);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/applicantService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "applicantService": (()=>applicantService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/apiClient.ts [app-client] (ecmascript)");
;
const applicantService = {
    // Create new applicant
    async createApplicant (data) {
        try {
            console.log('Creating applicant with data:', data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].post('/applicants', data);
            // Handle different response formats
            if (response.data) {
                // Check if it's a standard success response format
                if (response.data.success !== undefined && response.data.data) {
                    console.log('Standard response format detected');
                    return response.data.data;
                } else if (response.data.applicant_id || response.data.id) {
                    console.log('Direct data format detected');
                    return response.data;
                } else {
                    console.log('Fallback: treating response.data as applicant');
                    return response.data;
                }
            }
            throw new Error('Invalid response format from applicant creation');
        } catch (error) {
            console.error('Error creating applicant:', error);
            console.error('Error details:', error?.response?.data);
            throw error;
        }
    },
    // Get applicant by ID
    async getApplicant (id) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get(`/applicants/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching applicant:', error);
            throw error;
        }
    },
    // Update applicant
    async updateApplicant (id, data) {
        try {
            console.log('Updating applicant:', id, data);
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].put(`/applicants/${id}`, data);
            console.log('Applicant updated successfully:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error updating applicant:', error);
            throw error;
        }
    },
    // Get applicants by user (if user can have multiple applicants)
    async getApplicantsByUser () {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].get('/applicants/by-user');
            return response.data;
        } catch (error) {
            console.error('Error fetching user applicants:', error);
            throw error;
        }
    },
    // Delete applicant
    async deleteApplicant (id) {
        try {
            const response = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$apiClient$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["apiClient"].delete(`/applicants/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting applicant:', error);
            throw error;
        }
    }
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/config/licenseTypeStepConfig.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * License Type Step Configuration System
 * Defines which form steps are required for each license type
 */ __turbopack_context__.s({
    "LICENSE_TYPE_STEP_CONFIGS": (()=>LICENSE_TYPE_STEP_CONFIGS),
    "calculateProgress": (()=>calculateProgress),
    "getLicenseTypeStepConfig": (()=>getLicenseTypeStepConfig),
    "getNextStep": (()=>getNextStep),
    "getOptionalSteps": (()=>getOptionalSteps),
    "getPreviousStep": (()=>getPreviousStep),
    "getRequiredSteps": (()=>getRequiredSteps),
    "getStepByIndex": (()=>getStepByIndex),
    "getStepByRoute": (()=>getStepByRoute),
    "getStepIndex": (()=>getStepIndex),
    "getTotalSteps": (()=>getTotalSteps),
    "setLicenseTypeUUIDToCodeMap": (()=>setLicenseTypeUUIDToCodeMap)
});
// Base steps that can be used across license types
const BASE_STEPS = {
    applicantInfo: {
        id: 'applicant-info',
        name: 'Applicant Information',
        component: 'ApplicantInfo',
        route: 'applicant-info',
        required: true,
        description: 'Personal or company information of the applicant',
        estimatedTime: '5'
    },
    companyProfile: {
        id: 'company-profile',
        name: 'Company Profile',
        component: 'CompanyProfile',
        route: 'company-profile',
        required: true,
        description: 'Company structure, shareholders, and directors',
        estimatedTime: '10'
    },
    management: {
        id: 'management',
        name: 'Management Structure',
        component: 'Management',
        route: 'management',
        required: false,
        description: 'Management team and organizational structure',
        estimatedTime: '8'
    },
    professionalServices: {
        id: 'professional-services',
        name: 'Professional Services',
        component: 'ProfessionalServices',
        route: 'professional-services',
        required: false,
        description: 'External consultants and service providers',
        estimatedTime: '6'
    },
    businessInfo: {
        id: 'business-info',
        name: 'Business Information',
        component: 'BusinessInfo',
        route: 'business-info',
        required: true,
        description: 'Business description and operational plan',
        estimatedTime: '7'
    },
    serviceScope: {
        id: 'service-scope',
        name: 'Service Scope',
        component: 'ServiceScope',
        route: 'service-scope',
        required: true,
        description: 'Services offered and geographic coverage',
        estimatedTime: '8'
    },
    businessPlan: {
        id: 'business-plan',
        name: 'Business Plan',
        component: 'BusinessPlan',
        route: 'business-plan',
        required: true,
        description: 'Market analysis and financial projections',
        estimatedTime: '15'
    },
    legalHistory: {
        id: 'legal-history',
        name: 'Legal History',
        component: 'LegalHistory',
        route: 'legal-history',
        required: true,
        description: 'Legal compliance and regulatory history',
        estimatedTime: '5'
    },
    reviewSubmit: {
        id: 'review-submit',
        name: 'Review & Submit',
        component: 'ReviewSubmit',
        route: 'review-submit',
        required: true,
        description: 'Review all information and submit application',
        estimatedTime: '10'
    }
};
const LICENSE_TYPE_STEP_CONFIGS = {
    telecommunications: {
        licenseTypeId: 'telecommunications',
        name: 'Telecommunications License',
        description: 'License for telecommunications service providers including ISPs, mobile operators, and fixed-line services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.businessInfo,
            BASE_STEPS.serviceScope,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '74 minutes',
        requirements: [
            'Business registration certificate',
            'Tax compliance certificate',
            'Technical specifications',
            'Financial statements',
            'Management CVs',
            'Network coverage plans'
        ]
    },
    postal_services: {
        licenseTypeId: 'postal_services',
        name: 'Postal Services License',
        description: 'License for postal and courier service providers',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.businessInfo,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '42 minutes',
        requirements: [
            'Business registration certificate',
            'Fleet inventory',
            'Service coverage map',
            'Insurance certificates',
            'Premises documentation'
        ]
    },
    standards_compliance: {
        licenseTypeId: 'standards_compliance',
        name: 'Standards Compliance License',
        description: 'License for standards compliance and certification services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.businessInfo,
            BASE_STEPS.serviceScope,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '59 minutes',
        requirements: [
            'Accreditation certificates',
            'Technical competency proof',
            'Quality management system',
            'Laboratory facilities documentation',
            'Staff qualifications'
        ]
    },
    broadcasting: {
        licenseTypeId: 'broadcasting',
        name: 'Broadcasting License',
        description: 'License for radio and television broadcasting services',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.businessInfo,
            BASE_STEPS.serviceScope,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '63 minutes',
        requirements: [
            'Broadcasting equipment specifications',
            'Content programming plan',
            'Studio facility documentation',
            'Transmission coverage maps',
            'Local content compliance plan'
        ]
    },
    spectrum_management: {
        licenseTypeId: 'spectrum_management',
        name: 'Spectrum Management License',
        description: 'License for radio frequency spectrum management and allocation',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.professionalServices,
            BASE_STEPS.businessInfo,
            BASE_STEPS.serviceScope,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '74 minutes',
        requirements: [
            'Spectrum usage plan',
            'Technical interference analysis',
            'Equipment type approval',
            'Frequency coordination agreements',
            'Monitoring capabilities documentation'
        ]
    },
    clf: {
        licenseTypeId: 'clf',
        name: 'CLF License',
        description: 'Consumer Lending and Finance license',
        steps: [
            BASE_STEPS.applicantInfo,
            BASE_STEPS.companyProfile,
            BASE_STEPS.management,
            BASE_STEPS.businessInfo,
            BASE_STEPS.businessPlan,
            BASE_STEPS.legalHistory,
            BASE_STEPS.reviewSubmit
        ],
        estimatedTotalTime: '50 minutes',
        requirements: [
            'Financial institution license',
            'Capital adequacy documentation',
            'Risk management framework',
            'Consumer protection policies',
            'Anti-money laundering procedures'
        ]
    }
};
// License type name to config key mapping
const LICENSE_TYPE_NAME_MAPPING = {
    'telecommunications': 'telecommunications',
    'postal services': 'postal_services',
    'postal_services': 'postal_services',
    'standards compliance': 'standards_compliance',
    'standards_compliance': 'standards_compliance',
    'broadcasting': 'broadcasting',
    'spectrum management': 'spectrum_management',
    'spectrum_management': 'spectrum_management',
    'clf': 'clf',
    'consumer lending and finance': 'clf'
};
const getLicenseTypeStepConfig = (licenseTypeId)=>{
    console.log('getLicenseTypeStepConfig called with:', licenseTypeId);
    console.log('Available configs:', Object.keys(LICENSE_TYPE_STEP_CONFIGS));
    console.log('UUID to code map:', licenseTypeUUIDToCodeMap);
    // First try direct lookup
    let config = LICENSE_TYPE_STEP_CONFIGS[licenseTypeId];
    if (config) {
        console.log('Found config via direct lookup:', config.name);
        return config;
    }
    // Try normalized lookup
    const normalizedId = licenseTypeId.toLowerCase().replace(/[^a-z0-9]/g, '_');
    config = LICENSE_TYPE_STEP_CONFIGS[normalizedId];
    if (config) {
        console.log('Found config via normalized lookup:', config.name);
        return config;
    }
    // Try name mapping
    const mappedKey = LICENSE_TYPE_NAME_MAPPING[normalizedId];
    if (mappedKey) {
        console.log('Found config via name mapping:', mappedKey);
        return LICENSE_TYPE_STEP_CONFIGS[mappedKey];
    }
    // If licenseTypeId looks like a UUID, try to get the code from license types
    if (isUUID(licenseTypeId)) {
        console.log('Detected UUID, trying to get code...');
        const code = getLicenseTypeCodeFromUUID(licenseTypeId);
        console.log('Got code from UUID:', code);
        if (code) {
            const foundConfig = LICENSE_TYPE_STEP_CONFIGS[code];
            if (foundConfig) {
                console.log('Found config via UUID mapping:', foundConfig.name);
                return foundConfig;
            }
        }
    }
    console.log('No config found for license type:', licenseTypeId);
    return null;
};
// Helper function to check if a string is a UUID
const isUUID = (str)=>{
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
};
// Helper function to get license type code from UUID
// This will be populated by the license type service
let licenseTypeUUIDToCodeMap = {};
const setLicenseTypeUUIDToCodeMap = (map)=>{
    licenseTypeUUIDToCodeMap = map;
};
const getLicenseTypeCodeFromUUID = (uuid)=>{
    return licenseTypeUUIDToCodeMap[uuid] || null;
};
const getStepByRoute = (licenseTypeId, stepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return null;
    return config.steps.find((step)=>step.route === stepRoute) || null;
};
const getStepByIndex = (licenseTypeId, stepIndex)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config || stepIndex < 0 || stepIndex >= config.steps.length) return null;
    return config.steps[stepIndex];
};
const getStepIndex = (licenseTypeId, stepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return -1;
    return config.steps.findIndex((step)=>step.route === stepRoute);
};
const getTotalSteps = (licenseTypeId)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config ? config.steps.length : 0;
};
const getRequiredSteps = (licenseTypeId)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config ? config.steps.filter((step)=>step.required) : [];
};
const getOptionalSteps = (licenseTypeId)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    return config ? config.steps.filter((step)=>!step.required) : [];
};
const calculateProgress = (licenseTypeId, completedSteps)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return 0;
    const totalSteps = config.steps.length;
    const completed = completedSteps.length;
    return Math.round(completed / totalSteps * 100);
};
const getNextStep = (licenseTypeId, currentStepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return null;
    const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);
    if (currentIndex === -1 || currentIndex >= config.steps.length - 1) return null;
    return config.steps[currentIndex + 1];
};
const getPreviousStep = (licenseTypeId, currentStepRoute)=>{
    const config = getLicenseTypeStepConfig(licenseTypeId);
    if (!config) return null;
    const currentIndex = getStepIndex(licenseTypeId, currentStepRoute);
    if (currentIndex <= 0) return null;
    return config.steps[currentIndex - 1];
};
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/services/applicationProgressService.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
/**
 * Application Progress Service
 * Manages step completion tracking and progress calculation for license applications
 */ __turbopack_context__.s({
    "applicationProgressService": (()=>applicationProgressService)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/config/licenseTypeStepConfig.ts [app-client] (ecmascript)");
;
class ApplicationProgressService {
    progressCache = new Map();
    /**
   * Initialize progress tracking for a new application
   */ async initializeProgress(applicationId, licenseTypeId) {
        const licenseConfig = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$config$2f$licenseTypeStepConfig$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getLicenseTypeStepConfig"])(licenseTypeId);
        if (!licenseConfig) {
            throw new Error(`Invalid license type: ${licenseTypeId}`);
        }
        const steps = licenseConfig.steps.map((step)=>({
                stepId: step.id,
                stepName: step.name,
                completed: false
            }));
        const progress = {
            applicationId,
            licenseTypeId,
            totalSteps: steps.length,
            completedSteps: 0,
            progressPercentage: 0,
            steps,
            lastUpdated: new Date()
        };
        this.progressCache.set(applicationId, progress);
        await this.saveProgressToStorage(progress);
        return progress;
    }
    /**
   * Mark a step as completed
   */ async markStepCompleted(applicationId, stepId, data) {
        let progress = await this.getProgress(applicationId);
        if (!progress) {
            throw new Error(`No progress found for application: ${applicationId}`);
        }
        // Update the specific step
        const stepIndex = progress.steps.findIndex((step)=>step.stepId === stepId);
        if (stepIndex === -1) {
            throw new Error(`Step not found: ${stepId}`);
        }
        if (!progress.steps[stepIndex].completed) {
            progress.steps[stepIndex].completed = true;
            progress.steps[stepIndex].completedAt = new Date();
            progress.steps[stepIndex].data = data;
            // Recalculate progress
            progress.completedSteps = progress.steps.filter((step)=>step.completed).length;
            progress.progressPercentage = Math.round(progress.completedSteps / progress.totalSteps * 100);
            progress.lastUpdated = new Date();
            // Update cache and storage
            this.progressCache.set(applicationId, progress);
            await this.saveProgressToStorage(progress);
        }
        return progress;
    }
    /**
   * Mark a step as incomplete (for editing)
   */ async markStepIncomplete(applicationId, stepId) {
        let progress = await this.getProgress(applicationId);
        if (!progress) {
            throw new Error(`No progress found for application: ${applicationId}`);
        }
        // Update the specific step
        const stepIndex = progress.steps.findIndex((step)=>step.stepId === stepId);
        if (stepIndex === -1) {
            throw new Error(`Step not found: ${stepId}`);
        }
        if (progress.steps[stepIndex].completed) {
            progress.steps[stepIndex].completed = false;
            progress.steps[stepIndex].completedAt = undefined;
            progress.steps[stepIndex].data = undefined;
            // Recalculate progress
            progress.completedSteps = progress.steps.filter((step)=>step.completed).length;
            progress.progressPercentage = Math.round(progress.completedSteps / progress.totalSteps * 100);
            progress.lastUpdated = new Date();
            // Update cache and storage
            this.progressCache.set(applicationId, progress);
            await this.saveProgressToStorage(progress);
        }
        return progress;
    }
    /**
   * Get current progress for an application
   */ async getProgress(applicationId) {
        // Check cache first
        if (this.progressCache.has(applicationId)) {
            return this.progressCache.get(applicationId);
        }
        // Load from storage
        const progress = await this.loadProgressFromStorage(applicationId);
        if (progress) {
            this.progressCache.set(applicationId, progress);
        }
        return progress;
    }
    /**
   * Get completed step IDs for an application
   */ async getCompletedStepIds(applicationId) {
        const progress = await this.getProgress(applicationId);
        if (!progress) return [];
        return progress.steps.filter((step)=>step.completed).map((step)=>step.stepId);
    }
    /**
   * Check if a specific step is completed
   */ async isStepCompleted(applicationId, stepId) {
        const progress = await this.getProgress(applicationId);
        if (!progress) return false;
        const step = progress.steps.find((s)=>s.stepId === stepId);
        return step?.completed || false;
    }
    /**
   * Get next incomplete step
   */ async getNextIncompleteStep(applicationId) {
        const progress = await this.getProgress(applicationId);
        if (!progress) return null;
        return progress.steps.find((step)=>!step.completed) || null;
    }
    /**
   * Calculate overall application completion status
   */ async getApplicationStatus(applicationId) {
        const progress = await this.getProgress(applicationId);
        if (!progress) return 'not_started';
        if (progress.completedSteps === 0) return 'not_started';
        if (progress.completedSteps === progress.totalSteps) return 'completed';
        return 'in_progress';
    }
    /**
   * Save progress to localStorage (in a real app, this would be an API call)
   */ async saveProgressToStorage(progress) {
        try {
            const key = `application_progress_${progress.applicationId}`;
            localStorage.setItem(key, JSON.stringify({
                ...progress,
                lastUpdated: progress.lastUpdated.toISOString()
            }));
        } catch (error) {
            console.error('Error saving progress to storage:', error);
        }
    }
    /**
   * Load progress from localStorage (in a real app, this would be an API call)
   */ async loadProgressFromStorage(applicationId) {
        try {
            const key = `application_progress_${applicationId}`;
            const stored = localStorage.getItem(key);
            if (!stored) return null;
            const parsed = JSON.parse(stored);
            return {
                ...parsed,
                lastUpdated: new Date(parsed.lastUpdated),
                steps: parsed.steps.map((step)=>({
                        ...step,
                        completedAt: step.completedAt ? new Date(step.completedAt) : undefined
                    }))
            };
        } catch (error) {
            console.error('Error loading progress from storage:', error);
            return null;
        }
    }
    /**
   * Clear progress cache (useful for testing or when switching applications)
   */ clearCache() {
        this.progressCache.clear();
    }
    /**
   * Delete progress for an application
   */ async deleteProgress(applicationId) {
        this.progressCache.delete(applicationId);
        try {
            const key = `application_progress_${applicationId}`;
            localStorage.removeItem(key);
        } catch (error) {
            console.error('Error deleting progress from storage:', error);
        }
    }
}
const applicationProgressService = new ApplicationProgressService();
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/application/steps/ApplicantInfo.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/TextInput.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/common/Select.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/formValidation.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationFormDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationFormDataService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicantService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicantService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationProgressService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationProgressService.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
const ApplicantInfo = ({ applicationId, licenseTypeId, licenseCategoryId, isEditMode = false, onNext, onPrevious, isFirstStep = true, isLastStep = false, onStepComplete, onStepError, onNavigate })=>{
    _s();
    // Debug logging for props
    console.log('ApplicantInfo component props:', {
        applicationId,
        licenseTypeId,
        licenseCategoryId,
        isEditMode
    });
    const [localData, setLocalData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        applicant_type: '',
        first_name: '',
        last_name: '',
        middle_name: '',
        email: '',
        phone: '',
        national_id: '',
        date_of_birth: '',
        nationality: 'Malawian',
        gender: '',
        postal_address: '',
        physical_address: '',
        city: '',
        district: '',
        postal_code: ''
    });
    const [validationErrors, setValidationErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasUnsavedChanges, setHasUnsavedChanges] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [applicationCreated, setApplicationCreated] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [createdApplicationId, setCreatedApplicationId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Load existing data when component mounts (for edit mode)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApplicantInfo.useEffect": ()=>{
            const loadExistingData = {
                "ApplicantInfo.useEffect.loadExistingData": async ()=>{
                    // Add more robust validation for applicationId
                    if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {
                        console.log('Loading existing data for application:', applicationId);
                        setIsLoading(true);
                        try {
                            // Load data from multiple sources since ApplicantInfo saves to different places
                            const dataPromises = [];
                            // 1. Load form data (individual person fields)
                            dataPromises.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationFormDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationFormDataService"].getFormSection(applicationId, 'applicantInfo').then({
                                "ApplicantInfo.useEffect.loadExistingData": (data)=>({
                                        source: 'formData',
                                        data
                                    })
                            }["ApplicantInfo.useEffect.loadExistingData"]).catch({
                                "ApplicantInfo.useEffect.loadExistingData": (error)=>{
                                    console.warn('Could not load form data:', error);
                                    return {
                                        source: 'formData',
                                        data: null
                                    };
                                }
                            }["ApplicantInfo.useEffect.loadExistingData"]));
                            // 2. Load application data (includes applicant relation)
                            dataPromises.push(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationService"].getApplication(applicationId).then({
                                "ApplicantInfo.useEffect.loadExistingData": (data)=>({
                                        source: 'application',
                                        data
                                    })
                            }["ApplicantInfo.useEffect.loadExistingData"]).catch({
                                "ApplicantInfo.useEffect.loadExistingData": (error)=>{
                                    console.warn('Could not load application data:', error);
                                    return {
                                        source: 'application',
                                        data: null
                                    };
                                }
                            }["ApplicantInfo.useEffect.loadExistingData"]));
                            const results = await Promise.all(dataPromises);
                            // Merge data from different sources
                            let mergedData = {
                                ...localData
                            };
                            for (const result of results){
                                if (result.data) {
                                    if (result.source === 'formData') {
                                        // Type guard: check if it's form data with section_data
                                        const formData = result.data;
                                        if (formData && formData.section_data) {
                                            console.log('Loaded form data:', formData.section_data);
                                            mergedData = {
                                                ...mergedData,
                                                ...formData.section_data
                                            };
                                        }
                                    } else if (result.source === 'application') {
                                        // Type guard: check if it's application data with applicant
                                        const applicationData = result.data;
                                        if (applicationData && applicationData.applicant) {
                                            const applicant = applicationData.applicant;
                                            console.log('Loaded application with applicant:', applicant);
                                            // Map applicant business data to form fields where applicable
                                            if (applicant.name && !mergedData.first_name && !mergedData.last_name) {
                                                // If no individual names, try to split business name
                                                const nameParts = applicant.name.split(' ');
                                                if (nameParts.length >= 2) {
                                                    mergedData.first_name = nameParts[0];
                                                    mergedData.last_name = nameParts.slice(1).join(' ');
                                                } else {
                                                    mergedData.first_name = applicant.name;
                                                }
                                            }
                                            if (applicant.email && !mergedData.email) {
                                                mergedData.email = applicant.email;
                                            }
                                            if (applicant.phone && !mergedData.phone) {
                                                mergedData.phone = applicant.phone;
                                            }
                                            if (applicant.place_incorporation && !mergedData.city) {
                                                mergedData.city = applicant.place_incorporation;
                                            }
                                        }
                                    }
                                }
                            }
                            console.log('Merged applicant data from all sources:', mergedData);
                            setLocalData(mergedData);
                        } catch (error) {
                            console.error('Error loading existing applicant data:', error);
                            // Don't call onStepError as it would hide the form
                            // Just log the error and continue with empty form
                            console.log('Continuing with empty form due to load error');
                        } finally{
                            setIsLoading(false);
                        }
                    } else {
                        console.log('Skipping data load - not in edit mode or invalid applicationId:', {
                            isEditMode,
                            applicationId
                        });
                    }
                }
            }["ApplicantInfo.useEffect.loadExistingData"];
            loadExistingData();
        }
    }["ApplicantInfo.useEffect"], [
        applicationId,
        isEditMode
    ]);
    // Handle local changes
    const handleLocalChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ApplicantInfo.useCallback[handleLocalChange]": (field, value)=>{
            setLocalData({
                "ApplicantInfo.useCallback[handleLocalChange]": (prev)=>({
                        ...prev,
                        [field]: value
                    })
            }["ApplicantInfo.useCallback[handleLocalChange]"]);
            setHasUnsavedChanges(true);
            // Clear validation error for this field if it exists
            if (validationErrors && validationErrors[field]) {
                setValidationErrors({
                    "ApplicantInfo.useCallback[handleLocalChange]": (prev)=>({
                            ...prev,
                            [field]: ''
                        })
                }["ApplicantInfo.useCallback[handleLocalChange]"]);
            }
            // Clear save error when user starts making changes
            if (validationErrors.save) {
                setValidationErrors({
                    "ApplicantInfo.useCallback[handleLocalChange]": (prev)=>({
                            ...prev,
                            save: ''
                        })
                }["ApplicantInfo.useCallback[handleLocalChange]"]);
            }
        }
    }["ApplicantInfo.useCallback[handleLocalChange]"], [
        validationErrors
    ]);
    // Validate form data
    const validateForm = ()=>{
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSection"])(localData, 'applicantInfo');
        setValidationErrors(validation.errors);
        return validation.isValid;
    };
    // Save data to backend
    const saveData = async ()=>{
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSection"])(localData, 'applicantInfo');
        setValidationErrors(validation.errors);
        if (!validation.isValid) {
            // Don't call onStepError as it hides the form
            // Instead, let the form show validation errors inline
            console.log('Validation failed:', validation.errors);
            return false;
        }
        setIsSaving(true);
        try {
            if (isEditMode && applicationId && applicationId !== 'new') {
                // Update existing application data
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationFormDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationFormDataService"].saveOrUpdateFormSection(applicationId, 'applicantInfo', localData);
                // Mark step as completed in progress tracking
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationProgressService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationProgressService"].markStepCompleted(applicationId, 'applicant-info', localData);
                console.log('Applicant info updated for application:', applicationId);
            } else {
                // Create new applicant and application
                // Map individual person data to business applicant structure
                const applicantPayload = {
                    name: `${localData.first_name} ${localData.last_name}`.trim(),
                    email: localData.email,
                    phone: localData.phone,
                    business_registration_number: `BRN-${Date.now()}`,
                    tpin: `TPIN-${Date.now()}`,
                    website: 'https://example.com',
                    date_incorporation: new Date().toISOString(),
                    place_incorporation: localData.city || 'Malawi'
                };
                const createdApplicant = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicantService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicantService"].createApplicant(applicantPayload);
                console.log('Created applicant response:', createdApplicant);
                console.log('Applicant ID from response:', createdApplicant?.applicant_id);
                // Validate that we got a valid applicant ID
                if (!createdApplicant || !createdApplicant.applicant_id) {
                    throw new Error('Failed to create applicant: No applicant ID returned');
                }
                const applicationPayload = {
                    application_number: `APP-${Date.now()}`,
                    applicant_id: createdApplicant.applicant_id,
                    license_category_id: licenseCategoryId,
                    status: 'draft'
                };
                console.log('Creating application with payload:', applicationPayload);
                const createdApplication = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationService"].createApplication(applicationPayload);
                console.log('Created application response:', createdApplication);
                console.log('Application ID from response:', createdApplication?.application_id);
                // Validate that we got a valid application ID
                if (!createdApplication || !createdApplication.application_id) {
                    throw new Error('Failed to create application: No application ID returned');
                }
                const newApplicationId = createdApplication.application_id;
                console.log('Using application ID:', newApplicationId);
                // Save form data
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationFormDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationFormDataService"].saveOrUpdateFormSection(newApplicationId, 'applicantInfo', localData);
                console.log('New application created:', newApplicationId);
                // Initialize progress tracking for the new application
                if (licenseTypeId) {
                    await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationProgressService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationProgressService"].initializeProgress(newApplicationId, licenseTypeId);
                }
                // Mark this step as completed
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationProgressService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationProgressService"].markStepCompleted(newApplicationId, 'applicant-info', localData);
                console.log('Applicant info step completed, passing application ID to parent:', newApplicationId);
                // Set application created state
                setApplicationCreated(true);
                setCreatedApplicationId(newApplicationId);
                // Pass the application ID to the parent - let the parent handle navigation
                onStepComplete?.('applicant-info', {
                    ...localData,
                    applicationId: newApplicationId
                });
                // Don't navigate here - show continue button instead
                return true;
            }
            setHasUnsavedChanges(false);
            onStepComplete?.('applicant-info', localData);
            return true;
        } catch (error) {
            console.error('Error saving applicant info:', error);
            console.error('Error details:', {
                message: error.message,
                response: error.response?.data,
                status: error.response?.status
            });
            // Extract meaningful error message
            let errorMessage = 'Failed to save applicant information';
            if (error.message && error.message.includes('Invalid applicationId')) {
                errorMessage = 'Error creating application record. Please try again.';
            } else if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.message) {
                errorMessage = error.message;
            }
            // Don't call onStepError as it hides the form - instead show inline error
            // onStepError?.('applicant-info', { save: errorMessage });
            // Show error in the form itself
            setValidationErrors({
                save: errorMessage
            });
            return false;
        } finally{
            setIsSaving(false);
        }
    };
    // Handle save button click
    const handleSave = async ()=>{
        await saveData();
    };
    // Handle continue to next step
    const handleContinueToNextStep = ()=>{
        console.log('Continue to next step clicked');
        console.log('Created application ID:', createdApplicationId);
        console.log('License category ID:', licenseCategoryId);
        if (createdApplicationId && onNext) {
            // Update the URL with the application ID and move to next step
            const params = new URLSearchParams(window.location.search);
            params.set('application_id', createdApplicationId);
            const newUrl = `/customer/applications/apply?${params.toString()}`;
            window.history.replaceState({}, '', newUrl);
            // Call the onNext callback to move to the next step
            onNext();
        } else if (onNext) {
            // If no application ID yet, just move to next step
            onNext();
        } else {
            console.error('No navigation callback available');
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-200 dark:border-gray-700 pb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                        children: "Applicant Information"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 348,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                        children: "Please provide your personal information. This will create your application record."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 351,
                        columnNumber: 9
                    }, this),
                    !applicationId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-blue-700 dark:text-blue-300",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-information-line mr-1"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                    lineNumber: 357,
                                    columnNumber: 15
                                }, this),
                                "Your application will be created when you save this step."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                            lineNumber: 356,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 355,
                        columnNumber: 11
                    }, this),
                    applicationId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-green-700 dark:text-green-300",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-check-line mr-1"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                    lineNumber: 365,
                                    columnNumber: 15
                                }, this),
                                "Application created: ",
                                applicationId.slice(0, 8),
                                "..."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                            lineNumber: 364,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 363,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                lineNumber: 347,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "md:col-span-2",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                            label: "Applicant Type",
                            value: localData.applicant_type || '',
                            onChange: (value)=>handleLocalChange('applicant_type', value),
                            options: [
                                {
                                    value: 'individual',
                                    label: 'Individual'
                                },
                                {
                                    value: 'company',
                                    label: 'Company'
                                },
                                {
                                    value: 'organization',
                                    label: 'Organization'
                                }
                            ],
                            required: true,
                            error: validationErrors.applicant_type
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                            lineNumber: 375,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 374,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        label: "First Name",
                        value: localData.first_name || '',
                        onChange: (e)=>handleLocalChange('first_name', e.target.value),
                        required: true,
                        error: validationErrors.first_name
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 390,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        label: "Last Name",
                        value: localData.last_name || '',
                        onChange: (e)=>handleLocalChange('last_name', e.target.value),
                        required: true,
                        error: validationErrors.last_name
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 398,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        label: "Middle Name",
                        value: localData.middle_name || '',
                        onChange: (e)=>handleLocalChange('middle_name', e.target.value),
                        error: validationErrors.middle_name
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 406,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        label: "Gender",
                        value: localData.gender || '',
                        onChange: (value)=>handleLocalChange('gender', value),
                        options: [
                            {
                                value: 'male',
                                label: 'Male'
                            },
                            {
                                value: 'female',
                                label: 'Female'
                            },
                            {
                                value: 'other',
                                label: 'Other'
                            }
                        ],
                        required: true,
                        error: validationErrors.gender
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 413,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        label: "Email Address",
                        type: "email",
                        value: localData.email || '',
                        onChange: (e)=>handleLocalChange('email', e.target.value),
                        required: true,
                        error: validationErrors.email
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 427,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        label: "Phone Number",
                        value: localData.phone || '',
                        onChange: (e)=>handleLocalChange('phone', e.target.value),
                        placeholder: "+265 1 234 567",
                        required: true,
                        error: validationErrors.phone
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 436,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        label: "National ID",
                        value: localData.national_id || '',
                        onChange: (e)=>handleLocalChange('national_id', e.target.value),
                        placeholder: "**********",
                        required: true,
                        error: validationErrors.national_id
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 446,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        label: "Date of Birth",
                        type: "date",
                        value: localData.date_of_birth || '',
                        onChange: (e)=>handleLocalChange('date_of_birth', e.target.value),
                        required: true,
                        error: validationErrors.date_of_birth
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 455,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        label: "Nationality",
                        value: localData.nationality || 'Malawian',
                        onChange: (value)=>handleLocalChange('nationality', value),
                        options: [
                            {
                                value: 'Malawian',
                                label: 'Malawian'
                            },
                            {
                                value: 'Other',
                                label: 'Other'
                            }
                        ],
                        required: true,
                        error: validationErrors.nationality
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 464,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                lineNumber: 372,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-gray-200 dark:border-gray-700 pt-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-md font-medium text-gray-900 dark:text-gray-100 mb-4",
                        children: "Address Information"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 479,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 md:grid-cols-2 gap-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "md:col-span-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    label: "Postal Address",
                                    value: localData.postal_address || '',
                                    onChange: (e)=>handleLocalChange('postal_address', e.target.value),
                                    placeholder: "P.O. Box 123",
                                    required: true,
                                    error: validationErrors.postal_address
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                    lineNumber: 485,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                lineNumber: 484,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "md:col-span-2",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    label: "Physical Address",
                                    value: localData.physical_address || '',
                                    onChange: (e)=>handleLocalChange('physical_address', e.target.value),
                                    placeholder: "Street address",
                                    required: true,
                                    error: validationErrors.physical_address
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                    lineNumber: 496,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                lineNumber: 495,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                label: "City",
                                value: localData.city || '',
                                onChange: (e)=>handleLocalChange('city', e.target.value),
                                required: true,
                                error: validationErrors.city
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                lineNumber: 506,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$Select$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                label: "District",
                                value: localData.district || '',
                                onChange: (value)=>handleLocalChange('district', value),
                                options: [
                                    {
                                        value: 'Blantyre',
                                        label: 'Blantyre'
                                    },
                                    {
                                        value: 'Lilongwe',
                                        label: 'Lilongwe'
                                    },
                                    {
                                        value: 'Mzuzu',
                                        label: 'Mzuzu'
                                    },
                                    {
                                        value: 'Zomba',
                                        label: 'Zomba'
                                    },
                                    {
                                        value: 'Other',
                                        label: 'Other'
                                    }
                                ],
                                required: true,
                                error: validationErrors.district
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                lineNumber: 514,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$common$2f$TextInput$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                label: "Postal Code",
                                value: localData.postal_code || '',
                                onChange: (e)=>handleLocalChange('postal_code', e.target.value),
                                error: validationErrors.postal_code
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                lineNumber: 529,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                        lineNumber: 483,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                lineNumber: 478,
                columnNumber: 7
            }, this),
            validationErrors.save && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                            lineNumber: 542,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-sm font-medium text-red-800 dark:text-red-200",
                                    children: "Error Saving Application"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                    lineNumber: 544,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-red-700 dark:text-red-300 text-sm mt-1",
                                    children: validationErrors.save
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                    lineNumber: 547,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                            lineNumber: 543,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                    lineNumber: 541,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                lineNumber: 540,
                columnNumber: 9
            }, this),
            applicationCreated && !isEditMode && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3"
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                            lineNumber: 559,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-sm font-medium text-green-800 dark:text-green-200",
                                    children: "Application Created Successfully!"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                    lineNumber: 561,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-green-700 dark:text-green-300 text-sm mt-1",
                                    children: "Your application has been created. You can now continue to the next step."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                    lineNumber: 564,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                            lineNumber: 560,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                    lineNumber: 558,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                lineNumber: 557,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",
                children: applicationCreated && !isEditMode ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: handleSave,
                            disabled: isSaving || isLoading,
                            className: "inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                            children: isSaving || isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-loader-4-line animate-spin mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                        lineNumber: 584,
                                        columnNumber: 19
                                    }, this),
                                    "Saving..."
                                ]
                            }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                        className: "ri-save-line mr-2"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                        lineNumber: 589,
                                        columnNumber: 19
                                    }, this),
                                    "Save Changes"
                                ]
                            }, void 0, true)
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                            lineNumber: 577,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: handleContinueToNextStep,
                            className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-arrow-right-line mr-2"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                    lineNumber: 600,
                                    columnNumber: 15
                                }, this),
                                "Continue to Company Profile"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                            lineNumber: 596,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true) : /* Create/Save Application Button */ /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleSave,
                    disabled: isSaving || isLoading,
                    className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                    children: isSaving || isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-loader-4-line animate-spin mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                lineNumber: 613,
                                columnNumber: 17
                            }, this),
                            applicationId ? 'Saving...' : 'Creating Application...'
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-save-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                                lineNumber: 618,
                                columnNumber: 17
                            }, this),
                            applicationId ? 'Save Changes' : 'Create Application'
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                    lineNumber: 606,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
                lineNumber: 573,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/application/steps/ApplicantInfo.tsx",
        lineNumber: 346,
        columnNumber: 5
    }, this);
};
_s(ApplicantInfo, "WRp+N6aV5sBPzapmFqmssEC+OzA=");
_c = ApplicantInfo;
const __TURBOPACK__default__export__ = ApplicantInfo;
var _c;
__turbopack_context__.k.register(_c, "ApplicantInfo");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/application/steps/ServiceScope.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/formValidation.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const ServiceScope = ({ formData, onChange, onSave, errors, applicationId, isLoading = false })=>{
    _s();
    const [localData, setLocalData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        services_offered: '',
        geographic_coverage: '',
        service_categories: '',
        target_customers: '',
        service_capacity: '',
        ...formData
    });
    const [validationErrors, setValidationErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Sync formData to localData only when formData changes and is different
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ServiceScope.useEffect": ()=>{
            if (formData && Object.keys(formData).length > 0) {
                setLocalData({
                    "ServiceScope.useEffect": (prev)=>{
                        const newData = {
                            ...prev,
                            ...formData
                        };
                        // Only update if there are actual changes to prevent infinite loops
                        const hasChanges = Object.keys(formData).some({
                            "ServiceScope.useEffect.hasChanges": (key)=>prev[key] !== formData[key]
                        }["ServiceScope.useEffect.hasChanges"]);
                        return hasChanges ? newData : prev;
                    }
                }["ServiceScope.useEffect"]);
            }
        }
    }["ServiceScope.useEffect"], [
        formData
    ]);
    const handleLocalChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "ServiceScope.useCallback[handleLocalChange]": (field, value)=>{
            setLocalData({
                "ServiceScope.useCallback[handleLocalChange]": (prev)=>({
                        ...prev,
                        [field]: value
                    })
            }["ServiceScope.useCallback[handleLocalChange]"]);
            // Call onChange with the field and value
            if (onChange) {
                onChange(field, value);
            }
            // Clear validation error for this field if it exists
            if (validationErrors && validationErrors[field]) {
                setValidationErrors({
                    "ServiceScope.useCallback[handleLocalChange]": (prev)=>({
                            ...prev,
                            [field]: ''
                        })
                }["ServiceScope.useCallback[handleLocalChange]"]);
            }
        }
    }["ServiceScope.useCallback[handleLocalChange]"], [
        onChange,
        validationErrors
    ]);
    const validateForm = ()=>{
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSection"])(localData, 'serviceScope');
        setValidationErrors(validation.errors);
        return validation.isValid;
    };
    const handleSave = async ()=>{
        if (!validateForm()) {
            return;
        }
        setIsSaving(true);
        try {
            await onSave(localData);
            console.log('Service scope saved');
        } catch (error) {
            console.error('Error saving service scope:', error);
        } finally{
            setIsSaving(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-200 dark:border-gray-700 pb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                        children: "Service Scope"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                        lineNumber: 86,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                        children: "Define the scope of services you plan to offer and your coverage area."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                        lineNumber: 89,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                lineNumber: 85,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Services Offered *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 96,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.services_offered || '',
                                onChange: (e)=>handleLocalChange('services_offered', e.target.value),
                                rows: 4,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe the services you plan to offer..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 99,
                                columnNumber: 11
                            }, this),
                            (validationErrors.services_offered || errors.services_offered) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.services_offered || errors.services_offered
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 107,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                        lineNumber: 95,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Geographic Coverage *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 114,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.geographic_coverage || '',
                                onChange: (e)=>handleLocalChange('geographic_coverage', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe the geographic areas you plan to serve..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 117,
                                columnNumber: 11
                            }, this),
                            (validationErrors.geographic_coverage || errors.geographic_coverage) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.geographic_coverage || errors.geographic_coverage
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 125,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                        lineNumber: 113,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Service Categories *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 132,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.service_categories || '',
                                onChange: (e)=>handleLocalChange('service_categories', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "List and categorize your services..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 135,
                                columnNumber: 11
                            }, this),
                            (validationErrors.service_categories || errors.service_categories) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.service_categories || errors.service_categories
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 143,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                        lineNumber: 131,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Target Customers *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 150,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.target_customers || '',
                                onChange: (e)=>handleLocalChange('target_customers', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe your target customer segments..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 153,
                                columnNumber: 11
                            }, this),
                            (validationErrors.target_customers || errors.target_customers) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.target_customers || errors.target_customers
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 161,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                        lineNumber: 149,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Service Capacity *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 168,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.service_capacity || '',
                                onChange: (e)=>handleLocalChange('service_capacity', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe your capacity to deliver services..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 171,
                                columnNumber: 11
                            }, this),
                            (validationErrors.service_capacity || errors.service_capacity) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.service_capacity || errors.service_capacity
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 179,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                        lineNumber: 167,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                lineNumber: 94,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleSave,
                    disabled: isSaving || isLoading,
                    className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                    children: isSaving || isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-loader-4-line animate-spin mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 195,
                                columnNumber: 15
                            }, this),
                            "Saving..."
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-save-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                                lineNumber: 200,
                                columnNumber: 15
                            }, this),
                            "Save Service Scope"
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                    lineNumber: 188,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
                lineNumber: 187,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/application/steps/ServiceScope.tsx",
        lineNumber: 84,
        columnNumber: 5
    }, this);
};
_s(ServiceScope, "kQoJkyOGva/3Lcqhan5f9DUOxBY=");
_c = ServiceScope;
const __TURBOPACK__default__export__ = ServiceScope;
var _c;
__turbopack_context__.k.register(_c, "ServiceScope");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/application/steps/BusinessPlan.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/formValidation.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const BusinessPlan = ({ formData, onChange, onSave, errors, applicationId, isLoading = false })=>{
    _s();
    const [localData, setLocalData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        executive_summary: '',
        market_analysis: '',
        financial_projections: '',
        revenue_model: '',
        investment_requirements: '',
        implementation_timeline: '',
        risk_analysis: '',
        success_metrics: '',
        ...formData
    });
    const [validationErrors, setValidationErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Sync formData to localData only when formData changes and is different
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BusinessPlan.useEffect": ()=>{
            if (formData && Object.keys(formData).length > 0) {
                setLocalData({
                    "BusinessPlan.useEffect": (prev)=>{
                        const newData = {
                            ...prev,
                            ...formData
                        };
                        // Only update if there are actual changes to prevent infinite loops
                        const hasChanges = Object.keys(formData).some({
                            "BusinessPlan.useEffect.hasChanges": (key)=>prev[key] !== formData[key]
                        }["BusinessPlan.useEffect.hasChanges"]);
                        return hasChanges ? newData : prev;
                    }
                }["BusinessPlan.useEffect"]);
            }
        }
    }["BusinessPlan.useEffect"], [
        formData
    ]);
    const handleLocalChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "BusinessPlan.useCallback[handleLocalChange]": (field, value)=>{
            setLocalData({
                "BusinessPlan.useCallback[handleLocalChange]": (prev)=>({
                        ...prev,
                        [field]: value
                    })
            }["BusinessPlan.useCallback[handleLocalChange]"]);
            // Call onChange with the field and value
            if (onChange) {
                onChange(field, value);
            }
            // Clear validation error for this field if it exists
            if (validationErrors && validationErrors[field]) {
                setValidationErrors({
                    "BusinessPlan.useCallback[handleLocalChange]": (prev)=>({
                            ...prev,
                            [field]: ''
                        })
                }["BusinessPlan.useCallback[handleLocalChange]"]);
            }
        }
    }["BusinessPlan.useCallback[handleLocalChange]"], [
        onChange,
        validationErrors
    ]);
    const validateForm = ()=>{
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSection"])(localData, 'businessPlan');
        setValidationErrors(validation.errors);
        return validation.isValid;
    };
    const handleSave = async ()=>{
        if (!validateForm()) {
            return;
        }
        setIsSaving(true);
        try {
            await onSave(localData);
            console.log('Business plan saved');
        } catch (error) {
            console.error('Error saving business plan:', error);
        } finally{
            setIsSaving(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-200 dark:border-gray-700 pb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                        children: "Business Plan"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 89,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                        children: "Provide your business plan including financial projections and strategy."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 92,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                lineNumber: 88,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Executive Summary *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 99,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.executive_summary || '',
                                onChange: (e)=>handleLocalChange('executive_summary', e.target.value),
                                rows: 4,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Provide a summary of your business plan..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 102,
                                columnNumber: 11
                            }, this),
                            (validationErrors.executive_summary || errors.executive_summary) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.executive_summary || errors.executive_summary
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 110,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 98,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Market Analysis *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 117,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.market_analysis || '',
                                onChange: (e)=>handleLocalChange('market_analysis', e.target.value),
                                rows: 4,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe your market analysis and opportunities..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 120,
                                columnNumber: 11
                            }, this),
                            (validationErrors.market_analysis || errors.market_analysis) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.market_analysis || errors.market_analysis
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 128,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 116,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Financial Projections *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 135,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.financial_projections || '',
                                onChange: (e)=>handleLocalChange('financial_projections', e.target.value),
                                rows: 4,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Provide your financial projections for the next 3-5 years..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 138,
                                columnNumber: 11
                            }, this),
                            (validationErrors.financial_projections || errors.financial_projections) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.financial_projections || errors.financial_projections
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 146,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 134,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Revenue Model *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 153,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.revenue_model || '',
                                onChange: (e)=>handleLocalChange('revenue_model', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe how your business will generate revenue..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 156,
                                columnNumber: 11
                            }, this),
                            (validationErrors.revenue_model || errors.revenue_model) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.revenue_model || errors.revenue_model
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 164,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 152,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Investment Requirements *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 171,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.investment_requirements || '',
                                onChange: (e)=>handleLocalChange('investment_requirements', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe your funding and investment requirements..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 174,
                                columnNumber: 11
                            }, this),
                            (validationErrors.investment_requirements || errors.investment_requirements) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.investment_requirements || errors.investment_requirements
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 182,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 170,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Implementation Timeline *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 189,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.implementation_timeline || '',
                                onChange: (e)=>handleLocalChange('implementation_timeline', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Provide your implementation timeline and milestones..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 192,
                                columnNumber: 11
                            }, this),
                            (validationErrors.implementation_timeline || errors.implementation_timeline) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.implementation_timeline || errors.implementation_timeline
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 200,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 188,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Risk Analysis *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 207,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.risk_analysis || '',
                                onChange: (e)=>handleLocalChange('risk_analysis', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Identify and analyze potential risks and mitigation strategies..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 210,
                                columnNumber: 11
                            }, this),
                            (validationErrors.risk_analysis || errors.risk_analysis) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.risk_analysis || errors.risk_analysis
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 218,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 206,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Success Metrics *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 225,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.success_metrics || '',
                                onChange: (e)=>handleLocalChange('success_metrics', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Define how you will measure success and performance..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 228,
                                columnNumber: 11
                            }, this),
                            (validationErrors.success_metrics || errors.success_metrics) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.success_metrics || errors.success_metrics
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 236,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                        lineNumber: 224,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                lineNumber: 97,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleSave,
                    disabled: isSaving || isLoading,
                    className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                    children: isSaving || isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-loader-4-line animate-spin mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 252,
                                columnNumber: 15
                            }, this),
                            "Saving..."
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-save-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                                lineNumber: 257,
                                columnNumber: 15
                            }, this),
                            "Save Business Plan"
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                    lineNumber: 245,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
                lineNumber: 244,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/application/steps/BusinessPlan.tsx",
        lineNumber: 87,
        columnNumber: 5
    }, this);
};
_s(BusinessPlan, "LtXvrYLZbK4LSiau9LnwZtjFdZs=");
_c = BusinessPlan;
const __TURBOPACK__default__export__ = BusinessPlan;
var _c;
__turbopack_context__.k.register(_c, "BusinessPlan");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/application/steps/LegalHistory.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/formValidation.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const LegalHistory = ({ formData, onChange, onSave, errors, applicationId, isLoading = false })=>{
    _s();
    const [localData, setLocalData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        criminal_history: false,
        criminal_details: '',
        bankruptcy_history: false,
        bankruptcy_details: '',
        regulatory_actions: false,
        regulatory_details: '',
        litigation_history: false,
        litigation_details: '',
        compliance_record: '',
        previous_licenses: '',
        declaration_accepted: false,
        ...formData
    });
    const [validationErrors, setValidationErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Sync formData to localData only when formData changes and is different
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "LegalHistory.useEffect": ()=>{
            if (formData && Object.keys(formData).length > 0) {
                setLocalData({
                    "LegalHistory.useEffect": (prev)=>{
                        const newData = {
                            ...prev,
                            ...formData
                        };
                        // Only update if there are actual changes to prevent infinite loops
                        const hasChanges = Object.keys(formData).some({
                            "LegalHistory.useEffect.hasChanges": (key)=>prev[key] !== formData[key]
                        }["LegalHistory.useEffect.hasChanges"]);
                        return hasChanges ? newData : prev;
                    }
                }["LegalHistory.useEffect"]);
            }
        }
    }["LegalHistory.useEffect"], [
        formData
    ]);
    const handleLocalChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "LegalHistory.useCallback[handleLocalChange]": (field, value)=>{
            setLocalData({
                "LegalHistory.useCallback[handleLocalChange]": (prev)=>({
                        ...prev,
                        [field]: value
                    })
            }["LegalHistory.useCallback[handleLocalChange]"]);
            // Call onChange with the field and value
            if (onChange) {
                onChange(field, value);
            }
            // Clear validation error for this field if it exists
            if (validationErrors && validationErrors[field]) {
                setValidationErrors({
                    "LegalHistory.useCallback[handleLocalChange]": (prev)=>({
                            ...prev,
                            [field]: ''
                        })
                }["LegalHistory.useCallback[handleLocalChange]"]);
            }
        }
    }["LegalHistory.useCallback[handleLocalChange]"], [
        onChange,
        validationErrors
    ]);
    const validateForm = ()=>{
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSection"])(localData, 'legalHistory');
        setValidationErrors(validation.errors);
        return validation.isValid;
    };
    const handleSave = async ()=>{
        if (!validateForm()) {
            return;
        }
        setIsSaving(true);
        try {
            await onSave(localData);
            console.log('Legal history saved');
        } catch (error) {
            console.error('Error saving legal history:', error);
        } finally{
            setIsSaving(false);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-200 dark:border-gray-700 pb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                        children: "Legal History & Compliance"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 92,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                        children: "Please provide information about your legal and compliance history."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 95,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                lineNumber: 91,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: "flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "checkbox",
                                    checked: localData.criminal_history || false,
                                    onChange: (e)=>handleLocalChange('criminal_history', e.target.checked),
                                    className: "h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 104,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "ml-2 text-sm text-gray-700 dark:text-gray-300",
                                    children: "I have a criminal history"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 110,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                            lineNumber: 103,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 102,
                        columnNumber: 9
                    }, this),
                    localData.criminal_history && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Criminal History Details *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 118,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.criminal_details || '',
                                onChange: (e)=>handleLocalChange('criminal_details', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Please provide details of your criminal history..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 121,
                                columnNumber: 13
                            }, this),
                            (validationErrors.criminal_details || errors.criminal_details) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.criminal_details || errors.criminal_details
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 129,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 117,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                lineNumber: 101,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: "flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "checkbox",
                                    checked: localData.bankruptcy_history || false,
                                    onChange: (e)=>handleLocalChange('bankruptcy_history', e.target.checked),
                                    className: "h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 141,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "ml-2 text-sm text-gray-700 dark:text-gray-300",
                                    children: "I have a bankruptcy history"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 147,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                            lineNumber: 140,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 139,
                        columnNumber: 9
                    }, this),
                    localData.bankruptcy_history && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Bankruptcy History Details *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 155,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.bankruptcy_details || '',
                                onChange: (e)=>handleLocalChange('bankruptcy_details', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Please provide details of your bankruptcy history..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 158,
                                columnNumber: 13
                            }, this),
                            (validationErrors.bankruptcy_details || errors.bankruptcy_details) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.bankruptcy_details || errors.bankruptcy_details
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 166,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 154,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                lineNumber: 138,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: "flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "checkbox",
                                    checked: localData.regulatory_actions || false,
                                    onChange: (e)=>handleLocalChange('regulatory_actions', e.target.checked),
                                    className: "h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 178,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "ml-2 text-sm text-gray-700 dark:text-gray-300",
                                    children: "I have been subject to regulatory actions"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 184,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                            lineNumber: 177,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 176,
                        columnNumber: 9
                    }, this),
                    localData.regulatory_actions && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Regulatory Actions Details *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 192,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.regulatory_details || '',
                                onChange: (e)=>handleLocalChange('regulatory_details', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Please provide details of regulatory actions..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 195,
                                columnNumber: 13
                            }, this),
                            (validationErrors.regulatory_details || errors.regulatory_details) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.regulatory_details || errors.regulatory_details
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 203,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 191,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                lineNumber: 175,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: "flex items-center",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "checkbox",
                                    checked: localData.litigation_history || false,
                                    onChange: (e)=>handleLocalChange('litigation_history', e.target.checked),
                                    className: "h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 215,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "ml-2 text-sm text-gray-700 dark:text-gray-300",
                                    children: "I have been involved in litigation"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 221,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                            lineNumber: 214,
                            columnNumber: 11
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 213,
                        columnNumber: 9
                    }, this),
                    localData.litigation_history && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Litigation History Details *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 229,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.litigation_details || '',
                                onChange: (e)=>handleLocalChange('litigation_details', e.target.value),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Please provide details of litigation history..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 232,
                                columnNumber: 13
                            }, this),
                            (validationErrors.litigation_details || errors.litigation_details) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.litigation_details || errors.litigation_details
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 240,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 228,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                lineNumber: 212,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Compliance Record *"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 250,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.compliance_record || '',
                        onChange: (e)=>handleLocalChange('compliance_record', e.target.value),
                        rows: 3,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "Describe your compliance record and any relevant certifications..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 253,
                        columnNumber: 9
                    }, this),
                    (validationErrors.compliance_record || errors.compliance_record) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                        children: validationErrors.compliance_record || errors.compliance_record
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 261,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                lineNumber: 249,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Previous Licenses"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 269,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.previous_licenses || '',
                        onChange: (e)=>handleLocalChange('previous_licenses', e.target.value),
                        rows: 3,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "List any previous licenses held or applied for..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 272,
                        columnNumber: 9
                    }, this),
                    (validationErrors.previous_licenses || errors.previous_licenses) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                        children: validationErrors.previous_licenses || errors.previous_licenses
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                        lineNumber: 280,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                lineNumber: 268,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-gray-200 dark:border-gray-700 pt-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-gray-50 dark:bg-gray-800 p-4 rounded-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                            className: "flex items-start",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                    type: "checkbox",
                                    checked: localData.declaration_accepted || false,
                                    onChange: (e)=>handleLocalChange('declaration_accepted', e.target.checked),
                                    className: "h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded mt-1"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 290,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "ml-2 text-sm text-gray-700 dark:text-gray-300",
                                    children: "I declare that all information provided is true and accurate to the best of my knowledge. I understand that providing false information may result in the rejection of my application or revocation of any license granted. *"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                    lineNumber: 296,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                            lineNumber: 289,
                            columnNumber: 11
                        }, this),
                        (validationErrors.declaration_accepted || errors.declaration_accepted) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mt-1 text-sm text-red-600 dark:text-red-400",
                            children: validationErrors.declaration_accepted || errors.declaration_accepted
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                            lineNumber: 303,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                    lineNumber: 288,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                lineNumber: 287,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleSave,
                    disabled: isSaving || isLoading,
                    className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                    children: isSaving || isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-loader-4-line animate-spin mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 319,
                                columnNumber: 15
                            }, this),
                            "Saving..."
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-save-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                                lineNumber: 324,
                                columnNumber: 15
                            }, this),
                            "Save Legal History"
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                    lineNumber: 312,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
                lineNumber: 311,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/application/steps/LegalHistory.tsx",
        lineNumber: 90,
        columnNumber: 5
    }, this);
};
_s(LegalHistory, "MFcLVrHNMbpXEatdp0GhdeI/AL0=");
_c = LegalHistory;
const __TURBOPACK__default__export__ = LegalHistory;
var _c;
__turbopack_context__.k.register(_c, "LegalHistory");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/application/steps/ReviewSubmit.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
const ReviewSubmit = ({ formData, allFormData, onChange, onSave, onSubmit, errors, applicationId, isLoading = false, isSubmitting = false })=>{
    _s();
    const [isReviewing, setIsReviewing] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const handleSubmit = async ()=>{
        setIsReviewing(true);
        try {
            await onSubmit();
        } catch (error) {
            console.error('Error submitting application:', error);
        } finally{
            setIsReviewing(false);
        }
    };
    const renderSectionSummary = (title, data)=>{
        if (!data || Object.keys(data).length === 0) {
            return null;
        }
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-gray-50 dark:bg-gray-800 p-4 rounded-lg",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                    className: "text-md font-medium text-gray-900 dark:text-gray-100 mb-3",
                    children: title
                }, void 0, false, {
                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                    lineNumber: 48,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "space-y-2",
                    children: Object.entries(data).map(([key, value])=>{
                        if (!value || value === '' || value === false) return null;
                        const label = key.replace(/_/g, ' ').replace(/\b\w/g, (l)=>l.toUpperCase());
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col sm:flex-row",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm font-medium text-gray-600 dark:text-gray-400 sm:w-1/3",
                                    children: [
                                        label,
                                        ":"
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 59,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-sm text-gray-900 dark:text-gray-100 sm:w-2/3",
                                    children: typeof value === 'boolean' ? value ? 'Yes' : 'No' : String(value)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 62,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, key, true, {
                            fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                            lineNumber: 58,
                            columnNumber: 15
                        }, this);
                    })
                }, void 0, false, {
                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                    lineNumber: 51,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
            lineNumber: 47,
            columnNumber: 7
        }, this);
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-200 dark:border-gray-700 pb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                        children: "Review & Submit Application"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                        lineNumber: 76,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                        children: "Please review all information before submitting your application."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                        lineNumber: 79,
                        columnNumber: 9
                    }, this),
                    applicationId && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-sm text-green-700 dark:text-green-300",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-check-line mr-1"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 85,
                                    columnNumber: 15
                                }, this),
                                "Application ID: ",
                                applicationId
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                            lineNumber: 84,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                        lineNumber: 83,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                lineNumber: 75,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-6",
                children: [
                    renderSectionSummary('Applicant Information', allFormData.applicantInfo),
                    renderSectionSummary('Company Profile', allFormData.companyProfile),
                    renderSectionSummary('Business Information', allFormData.businessInfo),
                    renderSectionSummary('Service Scope', allFormData.serviceScope),
                    renderSectionSummary('Business Plan', allFormData.businessPlan),
                    renderSectionSummary('Legal History', allFormData.legalHistory)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                lineNumber: 93,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-gray-200 dark:border-gray-700 pt-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                            className: "text-md font-medium text-blue-900 dark:text-blue-100 mb-2",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-information-line mr-2"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 106,
                                    columnNumber: 13
                                }, this),
                                "Important Information"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                            lineNumber: 105,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ul", {
                            className: "text-sm text-blue-800 dark:text-blue-200 space-y-1",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "• Your application will be reviewed by MACRA within 30 business days"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 110,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "• You will receive email notifications about the status of your application"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 111,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "• Additional documentation may be requested during the review process"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 112,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "• Application fees are non-refundable"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 113,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    children: "• You can track your application status in your dashboard"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 114,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                            lineNumber: 109,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                    lineNumber: 104,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                lineNumber: 103,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-center pt-6 border-t border-gray-200 dark:border-gray-700",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleSubmit,
                    disabled: isSubmitting || isReviewing || isLoading,
                    className: "inline-flex items-center px-8 py-3 border border-transparent text-base font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed",
                    children: isSubmitting || isReviewing ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-loader-4-line animate-spin mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                lineNumber: 128,
                                columnNumber: 15
                            }, this),
                            "Submitting Application..."
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-send-plane-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                lineNumber: 133,
                                columnNumber: 15
                            }, this),
                            "Submit Application"
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                    lineNumber: 121,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                lineNumber: 120,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-alert-line text-yellow-600 dark:text-yellow-400 mr-2 mt-0.5"
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                            lineNumber: 143,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                    className: "text-sm font-medium text-yellow-800 dark:text-yellow-200",
                                    children: "Before Submitting"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 145,
                                    columnNumber: 13
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-sm text-yellow-700 dark:text-yellow-300 mt-1",
                                    children: "Please ensure all information is accurate and complete. Once submitted, you will not be able to modify your application without contacting MACRA support."
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                                    lineNumber: 148,
                                    columnNumber: 13
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                            lineNumber: 144,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                    lineNumber: 142,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
                lineNumber: 141,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/application/steps/ReviewSubmit.tsx",
        lineNumber: 74,
        columnNumber: 5
    }, this);
};
_s(ReviewSubmit, "7xLCdgb5+C5GvC2cgRs3icRJcuI=");
_c = ReviewSubmit;
const __TURBOPACK__default__export__ = ReviewSubmit;
var _c;
__turbopack_context__.k.register(_c, "ReviewSubmit");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/application/steps/BusinessInfo.tsx [app-client] (ecmascript) <locals>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/formValidation.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationFormDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationFormDataService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationProgressService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationProgressService.ts [app-client] (ecmascript)");
// Create placeholder components for the remaining steps
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ServiceScope$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/ServiceScope.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$BusinessPlan$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/BusinessPlan.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$LegalHistory$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/LegalHistory.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ReviewSubmit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/ReviewSubmit.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
const BusinessInfo = ({ applicationId, licenseTypeId, licenseCategoryId, isEditMode = false, onStepComplete, onStepError, onNavigate })=>{
    _s();
    const [localData, setLocalData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        business_model: '',
        operational_structure: '',
        target_market: '',
        competitive_advantage: '',
        facilities_description: '',
        equipment_description: '',
        operational_areas: '',
        service_delivery_model: '',
        quality_assurance: '',
        customer_support: ''
    });
    const [validationErrors, setValidationErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [isLoading, setIsLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [hasUnsavedChanges, setHasUnsavedChanges] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Load existing data when component mounts (for edit mode)
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "BusinessInfo.useEffect": ()=>{
            const loadExistingData = {
                "BusinessInfo.useEffect.loadExistingData": async ()=>{
                    // Add more robust validation for applicationId
                    if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {
                        console.log('Loading existing business info data for application:', applicationId);
                        setIsLoading(true);
                        try {
                            const existingData = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationFormDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationFormDataService"].getFormSection(applicationId, 'businessInfo');
                            if (existingData && existingData.section_data && Object.keys(existingData.section_data).length > 0) {
                                console.log('Loaded existing business info data:', existingData.section_data);
                                setLocalData({
                                    "BusinessInfo.useEffect.loadExistingData": (prev)=>({
                                            ...prev,
                                            ...existingData.section_data
                                        })
                                }["BusinessInfo.useEffect.loadExistingData"]);
                            } else {
                                console.log('No existing business info data found for application:', applicationId);
                            }
                        } catch (error) {
                            console.error('Error loading existing business info data:', error);
                            // Don't call onStepError as it would hide the form
                            // Just log the error and continue with empty form
                            console.log('Continuing with empty form due to load error');
                        } finally{
                            setIsLoading(false);
                        }
                    } else {
                        console.log('Skipping business info data load - not in edit mode or invalid applicationId:', {
                            isEditMode,
                            applicationId
                        });
                    }
                }
            }["BusinessInfo.useEffect.loadExistingData"];
            loadExistingData();
        }
    }["BusinessInfo.useEffect"], [
        applicationId,
        isEditMode
    ]);
    // Handle local data changes
    const handleLocalChange = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useCallback"])({
        "BusinessInfo.useCallback[handleLocalChange]": (field, value)=>{
            setLocalData({
                "BusinessInfo.useCallback[handleLocalChange]": (prev)=>({
                        ...prev,
                        [field]: value
                    })
            }["BusinessInfo.useCallback[handleLocalChange]"]);
            setHasUnsavedChanges(true);
            // Clear validation error for this field if it exists
            if (validationErrors && validationErrors[field]) {
                setValidationErrors({
                    "BusinessInfo.useCallback[handleLocalChange]": (prev)=>({
                            ...prev,
                            [field]: ''
                        })
                }["BusinessInfo.useCallback[handleLocalChange]"]);
            }
            // Clear save error when user starts making changes
            if (validationErrors.save) {
                setValidationErrors({
                    "BusinessInfo.useCallback[handleLocalChange]": (prev)=>({
                            ...prev,
                            save: ''
                        })
                }["BusinessInfo.useCallback[handleLocalChange]"]);
            }
        }
    }["BusinessInfo.useCallback[handleLocalChange]"], [
        validationErrors
    ]);
    // Validate form data
    const validateForm = ()=>{
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSection"])(localData, 'businessInfo');
        const errors1 = validation.errors || {};
        setValidationErrors(errors1);
        return validation.isValid;
    };
    // Save data to backend
    const saveData = async ()=>{
        const validation = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSection"])(localData, 'businessInfo');
        const errors1 = validation.errors || {};
        setValidationErrors(errors1);
        if (!validation.isValid) {
            // Don't call onStepError as it hides the form
            // Instead, let the form show validation errors inline
            console.log('Validation failed:', errors1);
            return false;
        }
        setIsSaving(true);
        try {
            if (applicationId && applicationId !== 'new') {
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationFormDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationFormDataService"].saveOrUpdateFormSection(applicationId, 'businessInfo', localData);
                // Mark step as completed in progress tracking
                await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationProgressService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationProgressService"].markStepCompleted(applicationId, 'business-info', localData);
                console.log('Business info data saved for application:', applicationId);
            } else {
                console.warn('No application ID available for saving business info');
            }
            setHasUnsavedChanges(false);
            onStepComplete?.('business-info', localData);
            return true;
        } catch (error) {
            console.error('Error saving business info data:', error);
            // Extract meaningful error message
            let errorMessage = 'Failed to save business information';
            if (error.response?.data?.message) {
                errorMessage = error.response.data.message;
            } else if (error.message) {
                errorMessage = error.message;
            }
            // Show error in the form itself instead of hiding the form
            setValidationErrors((prev)=>({
                    ...prev,
                    save: errorMessage
                }));
            return false;
        } finally{
            setIsSaving(false);
        }
    };
    // Handle save button click
    const handleSave = async ()=>{
        await saveData();
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-200 dark:border-gray-700 pb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                        children: "Business Information"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 151,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                        children: "Describe your business operations, structure, and service delivery model."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 154,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                lineNumber: 150,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "space-y-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Business Model *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 162,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.business_model || '',
                                onChange: (e)=>handleLocalChange('business_model', e.target.value),
                                onBlur: ()=>handleBlur('business_model'),
                                rows: 4,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe your business model, revenue streams, and value proposition..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 165,
                                columnNumber: 11
                            }, this),
                            (validationErrors.business_model || errors.business_model) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.business_model || errors.business_model
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 174,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Operational Structure *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 181,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.operational_structure || '',
                                onChange: (e)=>handleLocalChange('operational_structure', e.target.value),
                                onBlur: ()=>handleBlur('operational_structure'),
                                rows: 4,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe your organizational structure, departments, and operational processes..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 184,
                                columnNumber: 11
                            }, this),
                            (validationErrors.operational_structure || errors.operational_structure) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.operational_structure || errors.operational_structure
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 193,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 180,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Target Market *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 200,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.target_market || '',
                                onChange: (e)=>handleLocalChange('target_market', e.target.value),
                                onBlur: ()=>handleBlur('target_market'),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "Describe your target customers and market segments..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 203,
                                columnNumber: 11
                            }, this),
                            (validationErrors.target_market || errors.target_market) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.target_market || errors.target_market
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 212,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 199,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                children: "Competitive Advantage *"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 219,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                value: localData.competitive_advantage || '',
                                onChange: (e)=>handleLocalChange('competitive_advantage', e.target.value),
                                onBlur: ()=>handleBlur('competitive_advantage'),
                                rows: 3,
                                className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                placeholder: "What makes your business unique and competitive in the market..."
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 222,
                                columnNumber: 11
                            }, this),
                            (validationErrors.competitive_advantage || errors.competitive_advantage) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                children: validationErrors.competitive_advantage || errors.competitive_advantage
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 231,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 218,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                lineNumber: 160,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-gray-200 dark:border-gray-700 pt-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-md font-medium text-gray-900 dark:text-gray-100 mb-4",
                        children: "Facilities and Equipment"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 240,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "Facilities Description *"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 246,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                        value: localData.facilities_description || '',
                                        onChange: (e)=>handleLocalChange('facilities_description', e.target.value),
                                        onBlur: ()=>handleBlur('facilities_description'),
                                        rows: 3,
                                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                        placeholder: "Describe your office locations, facilities, and infrastructure..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 249,
                                        columnNumber: 13
                                    }, this),
                                    (validationErrors.facilities_description || errors.facilities_description) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                        children: validationErrors.facilities_description || errors.facilities_description
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 258,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 245,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "Equipment Description *"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 265,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                        value: localData.equipment_description || '',
                                        onChange: (e)=>handleLocalChange('equipment_description', e.target.value),
                                        onBlur: ()=>handleBlur('equipment_description'),
                                        rows: 3,
                                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                        placeholder: "List and describe your equipment, technology, and tools..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 268,
                                        columnNumber: 13
                                    }, this),
                                    (validationErrors.equipment_description || errors.equipment_description) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                        children: validationErrors.equipment_description || errors.equipment_description
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 277,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 264,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "Operational Areas *"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 284,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                        value: localData.operational_areas || '',
                                        onChange: (e)=>handleLocalChange('operational_areas', e.target.value),
                                        onBlur: ()=>handleBlur('operational_areas'),
                                        rows: 3,
                                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                        placeholder: "Describe the geographic areas where you operate or plan to operate..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 287,
                                        columnNumber: 13
                                    }, this),
                                    (validationErrors.operational_areas || errors.operational_areas) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                        children: validationErrors.operational_areas || errors.operational_areas
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 296,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 283,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 244,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                lineNumber: 239,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-t border-gray-200 dark:border-gray-700 pt-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-md font-medium text-gray-900 dark:text-gray-100 mb-4",
                        children: "Service Delivery"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 306,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "Service Delivery Model *"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 312,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                        value: localData.service_delivery_model || '',
                                        onChange: (e)=>handleLocalChange('service_delivery_model', e.target.value),
                                        onBlur: ()=>handleBlur('service_delivery_model'),
                                        rows: 3,
                                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                        placeholder: "Describe how you deliver services to customers..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 315,
                                        columnNumber: 13
                                    }, this),
                                    (validationErrors.service_delivery_model || errors.service_delivery_model) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                        children: validationErrors.service_delivery_model || errors.service_delivery_model
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 324,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 311,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "Quality Assurance *"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 331,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                        value: localData.quality_assurance || '',
                                        onChange: (e)=>handleLocalChange('quality_assurance', e.target.value),
                                        onBlur: ()=>handleBlur('quality_assurance'),
                                        rows: 3,
                                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                        placeholder: "Describe your quality control and assurance processes..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 334,
                                        columnNumber: 13
                                    }, this),
                                    (validationErrors.quality_assurance || errors.quality_assurance) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                        children: validationErrors.quality_assurance || errors.quality_assurance
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 343,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 330,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                                        children: "Customer Support *"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 350,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                        value: localData.customer_support || '',
                                        onChange: (e)=>handleLocalChange('customer_support', e.target.value),
                                        onBlur: ()=>handleBlur('customer_support'),
                                        rows: 3,
                                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                                        placeholder: "Describe your customer support and service processes..."
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 353,
                                        columnNumber: 13
                                    }, this),
                                    (validationErrors.customer_support || errors.customer_support) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                                        children: validationErrors.customer_support || errors.customer_support
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                        lineNumber: 362,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 349,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                        lineNumber: 310,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                lineNumber: 305,
                columnNumber: 7
            }, this),
            validationErrors.save && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "flex items-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                            className: "ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"
                        }, void 0, false, {
                            fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                            lineNumber: 374,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                    className: "text-sm font-medium text-red-800 dark:text-red-200",
                                    children: "Error Saving Business Information"
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                    lineNumber: 376,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                    className: "text-red-700 dark:text-red-300 text-sm mt-1",
                                    children: validationErrors.save
                                }, void 0, false, {
                                    fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                    lineNumber: 379,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                            lineNumber: 375,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                    lineNumber: 373,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                lineNumber: 372,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    onClick: handleSave,
                    disabled: isSaving || isLoading,
                    className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                    children: isSaving || isLoading ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-loader-4-line animate-spin mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 396,
                                columnNumber: 15
                            }, this),
                            "Saving..."
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-save-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                                lineNumber: 401,
                                columnNumber: 15
                            }, this),
                            "Save Business Information"
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                    lineNumber: 389,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
                lineNumber: 388,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/application/steps/BusinessInfo.tsx",
        lineNumber: 149,
        columnNumber: 5
    }, this);
};
_s(BusinessInfo, "D87J4E3uEY/WzPdwe0w7t8Cv1cY=");
_c = BusinessInfo;
const __TURBOPACK__default__export__ = BusinessInfo;
;
;
;
;
var _c;
__turbopack_context__.k.register(_c, "BusinessInfo");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/customer/application/steps/BusinessInfo.tsx [app-client] (ecmascript) <module evaluation>": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/formValidation.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationFormDataService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationFormDataService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationProgressService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationProgressService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ServiceScope$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/ServiceScope.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$BusinessPlan$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/BusinessPlan.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$LegalHistory$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/LegalHistory.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ReviewSubmit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/ReviewSubmit.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$BusinessInfo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/BusinessInfo.tsx [app-client] (ecmascript) <locals>");
}}),
"[project]/src/components/customer/application/steps/ProfessionalServices.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/formValidation.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
const ProfessionalServices = ({ formData, onChange, onSave, errors, applicationId, isLoading = false })=>{
    _s();
    const [localData, setLocalData] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({
        consultants: '',
        service_providers: '',
        technical_support: '',
        maintenance_arrangements: '',
        professional_partnerships: '',
        outsourced_services: '',
        quality_assurance: '',
        training_programs: '',
        ...formData
    });
    const [validationErrors, setValidationErrors] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])({});
    const [isSaving, setIsSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    // Sync with parent form data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ProfessionalServices.useEffect": ()=>{
            if (formData && Object.keys(formData).length > 0) {
                setLocalData({
                    "ProfessionalServices.useEffect": (prev)=>({
                            ...prev,
                            ...formData
                        })
                }["ProfessionalServices.useEffect"]);
            }
        }
    }["ProfessionalServices.useEffect"], [
        formData
    ]);
    // Handle local data changes
    const handleLocalChange = (field, value)=>{
        setLocalData((prev)=>({
                ...prev,
                [field]: value
            }));
        // Clear validation error when user starts typing
        if (validationErrors[field]) {
            setValidationErrors((prev)=>({
                    ...prev,
                    [field]: ''
                }));
        }
    };
    // Validation function
    const validateForm = ()=>{
        const errors = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$formValidation$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["validateSection"])('professionalServices', localData);
        setValidationErrors(errors);
        return Object.keys(errors).length === 0;
    };
    // Handle save
    const handleSave = async ()=>{
        if (!validateForm()) {
            return;
        }
        setIsSaving(true);
        try {
            await onSave(localData);
            console.log('Professional services data saved');
        } catch (error) {
            console.error('Error saving professional services data:', error);
        } finally{
            setIsSaving(false);
        }
    };
    // Auto-save on blur
    const handleBlur = (field)=>{
        if (localData[field] && !validationErrors[field]) {
            onChange(field, localData[field]);
        }
    };
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-b border-gray-200 dark:border-gray-700 pb-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                        children: "Professional Services"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 88,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-sm text-gray-600 dark:text-gray-400 mt-1",
                        children: "Provide information about external consultants, service providers, and support arrangements."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 91,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 87,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "External Consultants *"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 98,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.consultants || '',
                        onChange: (e)=>handleLocalChange('consultants', e.target.value),
                        onBlur: ()=>handleBlur('consultants'),
                        rows: 4,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "List any external consultants, their areas of expertise, and their role in your operations..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 101,
                        columnNumber: 9
                    }, this),
                    (validationErrors.consultants || errors.consultants) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                        children: validationErrors.consultants || errors.consultants
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 110,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 97,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Service Providers *"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 118,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.service_providers || '',
                        onChange: (e)=>handleLocalChange('service_providers', e.target.value),
                        onBlur: ()=>handleBlur('service_providers'),
                        rows: 4,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "Describe your service providers, their services, and contractual arrangements..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 121,
                        columnNumber: 9
                    }, this),
                    (validationErrors.service_providers || errors.service_providers) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                        children: validationErrors.service_providers || errors.service_providers
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 130,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 117,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Technical Support Arrangements *"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 138,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.technical_support || '',
                        onChange: (e)=>handleLocalChange('technical_support', e.target.value),
                        onBlur: ()=>handleBlur('technical_support'),
                        rows: 4,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "Describe your technical support arrangements, including internal and external resources..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 141,
                        columnNumber: 9
                    }, this),
                    (validationErrors.technical_support || errors.technical_support) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                        children: validationErrors.technical_support || errors.technical_support
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 150,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 137,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Maintenance Arrangements *"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 158,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.maintenance_arrangements || '',
                        onChange: (e)=>handleLocalChange('maintenance_arrangements', e.target.value),
                        onBlur: ()=>handleBlur('maintenance_arrangements'),
                        rows: 4,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "Detail your maintenance arrangements for equipment, systems, and infrastructure..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 161,
                        columnNumber: 9
                    }, this),
                    (validationErrors.maintenance_arrangements || errors.maintenance_arrangements) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "mt-1 text-sm text-red-600 dark:text-red-400",
                        children: validationErrors.maintenance_arrangements || errors.maintenance_arrangements
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 170,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 157,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Professional Partnerships"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 178,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.professional_partnerships || '',
                        onChange: (e)=>handleLocalChange('professional_partnerships', e.target.value),
                        onBlur: ()=>handleBlur('professional_partnerships'),
                        rows: 3,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "Describe any professional partnerships or strategic alliances..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 181,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 177,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Outsourced Services"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 193,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.outsourced_services || '',
                        onChange: (e)=>handleLocalChange('outsourced_services', e.target.value),
                        onBlur: ()=>handleBlur('outsourced_services'),
                        rows: 3,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "List any services that will be outsourced and the rationale..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 196,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 192,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Quality Assurance Measures"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 208,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.quality_assurance || '',
                        onChange: (e)=>handleLocalChange('quality_assurance', e.target.value),
                        onBlur: ()=>handleBlur('quality_assurance'),
                        rows: 3,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "Describe your quality assurance processes and standards..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 211,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 207,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                        className: "block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",
                        children: "Training Programs"
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 223,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                        value: localData.training_programs || '',
                        onChange: (e)=>handleLocalChange('training_programs', e.target.value),
                        onBlur: ()=>handleBlur('training_programs'),
                        rows: 3,
                        className: "w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100",
                        placeholder: "Describe training programs for staff and ongoing professional development..."
                    }, void 0, false, {
                        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                        lineNumber: 226,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 222,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-end pt-4",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                    type: "button",
                    onClick: handleSave,
                    disabled: isSaving || isLoading,
                    className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed",
                    children: isSaving ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                                lineNumber: 246,
                                columnNumber: 15
                            }, this),
                            "Saving..."
                        ]
                    }, void 0, true) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["Fragment"], {
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-save-line mr-2"
                            }, void 0, false, {
                                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                                lineNumber: 251,
                                columnNumber: 15
                            }, this),
                            "Save Professional Services"
                        ]
                    }, void 0, true)
                }, void 0, false, {
                    fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                    lineNumber: 238,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
                lineNumber: 237,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/customer/application/steps/ProfessionalServices.tsx",
        lineNumber: 86,
        columnNumber: 5
    }, this);
};
_s(ProfessionalServices, "4FlpKup2opCSmQbRGZPO2KeHJgE=");
_c = ProfessionalServices;
const __TURBOPACK__default__export__ = ProfessionalServices;
var _c;
__turbopack_context__.k.register(_c, "ProfessionalServices");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/customer/applications/apply/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/CustomerLayout.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/licenseCategoryService.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/services/applicationService.ts [app-client] (ecmascript)");
// Import step components
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ApplicantInfo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/ApplicantInfo.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$BusinessInfo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/BusinessInfo.tsx [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$BusinessInfo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/BusinessInfo.tsx [app-client] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ProfessionalServices$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/ProfessionalServices.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ServiceScope$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/ServiceScope.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$BusinessPlan$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/BusinessPlan.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$LegalHistory$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/LegalHistory.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ReviewSubmit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/customer/application/steps/ReviewSubmit.tsx [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
'use client';
;
;
;
;
;
;
;
;
;
;
;
;
;
// Define application steps
const APPLICATION_STEPS = [
    {
        id: 'applicant-info',
        name: 'Applicant Information',
        component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ApplicantInfo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    },
    {
        id: 'business-info',
        name: 'Business Information',
        component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$BusinessInfo$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$locals$3e$__["default"]
    },
    {
        id: 'professional-services',
        name: 'Professional Services',
        component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ProfessionalServices$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    },
    {
        id: 'service-scope',
        name: 'Service Scope',
        component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ServiceScope$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    },
    {
        id: 'business-plan',
        name: 'Business Plan',
        component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$BusinessPlan$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    },
    {
        id: 'legal-history',
        name: 'Legal History',
        component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$LegalHistory$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    },
    {
        id: 'review-submit',
        name: 'Review & Submit',
        component: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$application$2f$steps$2f$ReviewSubmit$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"]
    }
];
const ApplicationFormPage = ()=>{
    _s();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const searchParams = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"])();
    const { isAuthenticated, loading: authLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    // Get query parameters
    const licenseCategoryId = searchParams.get('license_category_id');
    const applicationId = searchParams.get('application_id');
    const stepParam = searchParams.get('step') || 'applicant-info';
    // State
    const [licenseCategory, setLicenseCategory] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [currentStepIndex, setCurrentStepIndex] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    // Find current step index
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApplicationFormPage.useEffect": ()=>{
            const stepIndex = APPLICATION_STEPS.findIndex({
                "ApplicationFormPage.useEffect.stepIndex": (step)=>step.id === stepParam
            }["ApplicationFormPage.useEffect.stepIndex"]);
            setCurrentStepIndex(stepIndex >= 0 ? stepIndex : 0);
        }
    }["ApplicationFormPage.useEffect"], [
        stepParam
    ]);
    // Authentication check
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApplicationFormPage.useEffect": ()=>{
            if (!authLoading && !isAuthenticated) {
                router.push('/customer/auth/login');
                return;
            }
        }
    }["ApplicationFormPage.useEffect"], [
        isAuthenticated,
        authLoading,
        router
    ]);
    // Validate required parameters and load data
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "ApplicationFormPage.useEffect": ()=>{
            const loadApplicationData = {
                "ApplicationFormPage.useEffect.loadApplicationData": async ()=>{
                    if (!licenseCategoryId) {
                        setError('License category is required');
                        setLoading(false);
                        return;
                    }
                    try {
                        setLoading(true);
                        setError(null);
                        // Load license category data
                        const category = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$licenseCategoryService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["licenseCategoryService"].getLicenseCategoryById(licenseCategoryId);
                        if (!category) {
                            setError('License category not found');
                            setLoading(false);
                            return;
                        }
                        setLicenseCategory(category);
                        // If editing existing application, validate application exists
                        if (applicationId) {
                            try {
                                const application = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$services$2f$applicationService$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["applicationService"].getApplicationById(applicationId);
                                if (!application) {
                                    setError('Application not found');
                                    setLoading(false);
                                    return;
                                }
                                // Validate that application belongs to the specified license category
                                if (application.license_category_id !== licenseCategoryId) {
                                    setError('Application does not match the specified license category');
                                    setLoading(false);
                                    return;
                                }
                            } catch (err) {
                                console.error('Error loading application:', err);
                                setError('Failed to load application data');
                                setLoading(false);
                                return;
                            }
                        }
                        setLoading(false);
                    } catch (err) {
                        console.error('Error loading application data:', err);
                        setError('Failed to load application data');
                        setLoading(false);
                    }
                }
            }["ApplicationFormPage.useEffect.loadApplicationData"];
            if (isAuthenticated && !authLoading) {
                loadApplicationData();
            }
        }
    }["ApplicationFormPage.useEffect"], [
        licenseCategoryId,
        applicationId,
        isAuthenticated,
        authLoading
    ]);
    // Navigation functions
    const updateUrlStep = (stepId)=>{
        const params = new URLSearchParams(searchParams.toString());
        params.set('step', stepId);
        router.push(`/customer/applications/apply?${params.toString()}`);
    };
    const handleNextStep = ()=>{
        if (currentStepIndex < APPLICATION_STEPS.length - 1) {
            const nextStep = APPLICATION_STEPS[currentStepIndex + 1];
            updateUrlStep(nextStep.id);
        }
    };
    const handlePreviousStep = ()=>{
        if (currentStepIndex > 0) {
            const prevStep = APPLICATION_STEPS[currentStepIndex - 1];
            updateUrlStep(prevStep.id);
        }
    };
    const handleStepClick = (stepIndex)=>{
        const step = APPLICATION_STEPS[stepIndex];
        updateUrlStep(step.id);
    };
    const handleBackToApplications = ()=>{
        router.push('/customer/applications');
    };
    // Loading state
    if (authLoading || loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center min-h-96",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 156,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 dark:text-gray-400",
                            children: "Loading application form..."
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 157,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                    lineNumber: 155,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                lineNumber: 154,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
            lineNumber: 153,
            columnNumber: 7
        }, this);
    }
    // Error state
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-center min-h-96",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "text-center",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-4",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                className: "ri-error-warning-line text-4xl text-red-500"
                            }, void 0, false, {
                                fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                lineNumber: 171,
                                columnNumber: 15
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 170,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-lg font-medium text-gray-900 dark:text-gray-100 mb-2",
                            children: "Error Loading Application"
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 173,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "text-gray-600 dark:text-gray-400 mb-6",
                            children: error
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 176,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: handleBackToApplications,
                            className: "inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-arrow-left-line mr-2"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                    lineNumber: 183,
                                    columnNumber: 15
                                }, this),
                                "Back to Applications"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 179,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                    lineNumber: 169,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                lineNumber: 168,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
            lineNumber: 167,
            columnNumber: 7
        }, this);
    }
    const currentStep = APPLICATION_STEPS[currentStepIndex];
    const CurrentStepComponent = currentStep.component;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$customer$2f$CustomerLayout$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: handleBackToApplications,
                            className: "inline-flex items-center text-sm text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 mb-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                    className: "ri-arrow-left-line mr-1"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                    lineNumber: 204,
                                    columnNumber: 13
                                }, this),
                                "Back to Applications"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 200,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                            className: "text-3xl font-bold text-gray-900 dark:text-gray-100",
                            children: [
                                licenseCategory?.name,
                                " Application"
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 207,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                            className: "mt-2 text-gray-600 dark:text-gray-400",
                            children: applicationId ? 'Edit your application' : 'Complete your license application'
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 210,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                    lineNumber: 199,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "mb-8",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("nav", {
                        "aria-label": "Progress",
                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("ol", {
                            className: "flex items-center",
                            children: APPLICATION_STEPS.map((step, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("li", {
                                    className: `relative ${index !== APPLICATION_STEPS.length - 1 ? 'pr-8 sm:pr-20' : ''}`,
                                    children: [
                                        index !== APPLICATION_STEPS.length - 1 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "absolute inset-0 flex items-center",
                                            "aria-hidden": "true",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "h-0.5 w-full bg-gray-200 dark:bg-gray-700"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                                lineNumber: 223,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                            lineNumber: 222,
                                            columnNumber: 21
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                            onClick: ()=>handleStepClick(index),
                                            className: `relative w-8 h-8 flex items-center justify-center rounded-full border-2 ${index === currentStepIndex ? 'border-primary bg-primary text-white' : index < currentStepIndex ? 'border-primary bg-primary text-white' : 'border-gray-300 bg-white text-gray-500 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-400'} hover:border-primary transition-colors`,
                                            children: index < currentStepIndex ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("i", {
                                                className: "ri-check-line text-sm"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                                lineNumber: 237,
                                                columnNumber: 23
                                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "text-sm font-medium",
                                                children: index + 1
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                                lineNumber: 239,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                            lineNumber: 226,
                                            columnNumber: 19
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                            className: "absolute top-10 left-1/2 transform -translate-x-1/2 text-xs font-medium text-gray-900 dark:text-gray-100 whitespace-nowrap",
                                            children: step.name
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                            lineNumber: 242,
                                            columnNumber: 19
                                        }, this)
                                    ]
                                }, step.id, true, {
                                    fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                    lineNumber: 220,
                                    columnNumber: 17
                                }, this))
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 218,
                            columnNumber: 13
                        }, this)
                    }, void 0, false, {
                        fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                        lineNumber: 217,
                        columnNumber: 11
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                    lineNumber: 216,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "px-6 py-4 border-b border-gray-200 dark:border-gray-700",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-medium text-gray-900 dark:text-gray-100",
                                children: currentStep.name
                            }, void 0, false, {
                                fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                lineNumber: 254,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 253,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "p-6",
                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(CurrentStepComponent, {
                                licenseCategoryId: licenseCategoryId,
                                applicationId: applicationId,
                                licenseTypeId: licenseCategory?.license_type_id,
                                isEditMode: !!applicationId,
                                onNext: handleNextStep,
                                onPrevious: handlePreviousStep,
                                isFirstStep: currentStepIndex === 0,
                                isLastStep: currentStepIndex === APPLICATION_STEPS.length - 1
                            }, void 0, false, {
                                fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                                lineNumber: 260,
                                columnNumber: 13
                            }, this)
                        }, void 0, false, {
                            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                            lineNumber: 259,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/customer/applications/apply/page.tsx",
                    lineNumber: 252,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/customer/applications/apply/page.tsx",
            lineNumber: 197,
            columnNumber: 7
        }, this)
    }, void 0, false, {
        fileName: "[project]/src/app/customer/applications/apply/page.tsx",
        lineNumber: 196,
        columnNumber: 5
    }, this);
};
_s(ApplicationFormPage, "Ozjx0uMbTGn8JzSPwNfWmef3j/w=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useSearchParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = ApplicationFormPage;
const __TURBOPACK__default__export__ = ApplicationFormPage;
var _c;
__turbopack_context__.k.register(_c, "ApplicationFormPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_fee0bfec._.js.map