'use client';

import React, { useState, useEffect, useCallback } from 'react';
import TextInput from '@/components/common/TextInput';
import Select from '@/components/common/Select';
import { validateSection } from '@/utils/formValidation';
import { applicationFormDataService } from '@/services/applicationFormDataService';
import { applicationService } from '@/services/applicationService';
import { applicantService } from '@/services/applicantService';
import { applicationProgressService } from '@/services/applicationProgressService';
import { IndependentStepProps } from '../types';

interface ApplicantInfoProps extends IndependentStepProps {}

const ApplicantInfo: React.FC<ApplicantInfoProps> = ({
  applicationId,
  licenseTypeId,
  licenseCategoryId,
  isEditMode = false,
  onNext,
  onPrevious,
  isFirstStep = true,
  isLastStep = false,
  onStepComplete,
  onStepError,
  onNavigate
}) => {

  // Debug logging for props
  console.log('ApplicantInfo component props:', {
    applicationId,
    licenseTypeId,
    licenseCategoryId,
    isEditMode
  });

  const [localData, setLocalData] = useState({
    applicant_type: '',
    first_name: '',
    last_name: '',
    middle_name: '',
    email: '',
    phone: '',
    national_id: '',
    date_of_birth: '',
    nationality: 'Malawian',
    gender: '',
    postal_address: '',
    physical_address: '',
    city: '',
    district: '',
    postal_code: ''
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [applicationCreated, setApplicationCreated] = useState(false);
  const [createdApplicationId, setCreatedApplicationId] = useState<string | null>(null);

  // Load existing data when component mounts (for edit mode)
  useEffect(() => {
    const loadExistingData = async () => {
      // Add more robust validation for applicationId
      if (isEditMode && applicationId && applicationId !== 'new' && applicationId !== 'undefined' && applicationId.trim() !== '') {
        console.log('Loading existing data for application:', applicationId);
        setIsLoading(true);
        try {
          // Load data from multiple sources since ApplicantInfo saves to different places
          const dataPromises = [];

          // 1. Load form data (individual person fields)
          dataPromises.push(
            applicationFormDataService.getFormSection(applicationId, 'applicantInfo')
              .then(data => ({ source: 'formData', data }))
              .catch(error => {
                console.warn('Could not load form data:', error);
                return { source: 'formData', data: null };
              })
          );

          // 2. Load application data (includes applicant relation)
          dataPromises.push(
            applicationService.getApplication(applicationId)
              .then(data => ({ source: 'application', data }))
              .catch(error => {
                console.warn('Could not load application data:', error);
                return { source: 'application', data: null };
              })
          );

          const results = await Promise.all(dataPromises);

          // Merge data from different sources
          let mergedData = { ...localData };

          for (const result of results) {
            if (result.data) {
              if (result.source === 'formData') {
                // Type guard: check if it's form data with section_data
                const formData = result.data as any;
                if (formData && formData.section_data) {
                  console.log('Loaded form data:', formData.section_data);
                  mergedData = { ...mergedData, ...formData.section_data };
                }
              } else if (result.source === 'application') {
                // Type guard: check if it's application data with applicant
                const applicationData = result.data as any;
                if (applicationData && applicationData.applicant) {
                  const applicant = applicationData.applicant;
                  console.log('Loaded application with applicant:', applicant);

                  // Map applicant business data to form fields where applicable
                  if (applicant.name && !mergedData.first_name && !mergedData.last_name) {
                    // If no individual names, try to split business name
                    const nameParts = applicant.name.split(' ');
                    if (nameParts.length >= 2) {
                      mergedData.first_name = nameParts[0];
                      mergedData.last_name = nameParts.slice(1).join(' ');
                    } else {
                      mergedData.first_name = applicant.name;
                    }
                  }

                  if (applicant.email && !mergedData.email) {
                    mergedData.email = applicant.email;
                  }

                  if (applicant.phone && !mergedData.phone) {
                    mergedData.phone = applicant.phone;
                  }

                  if (applicant.place_incorporation && !mergedData.city) {
                    mergedData.city = applicant.place_incorporation;
                  }
                }
              }
            }
          }

          console.log('Merged applicant data from all sources:', mergedData);
          setLocalData(mergedData);

        } catch (error) {
          console.error('Error loading existing applicant data:', error);
          // Don't call onStepError as it would hide the form
          // Just log the error and continue with empty form
          console.log('Continuing with empty form due to load error');
        } finally {
          setIsLoading(false);
        }
      } else {
        console.log('Skipping data load - not in edit mode or invalid applicationId:', { isEditMode, applicationId });
      }
    };

    loadExistingData();
  }, [applicationId, isEditMode]);

  // Handle local changes
  const handleLocalChange = useCallback((field: string, value: any) => {
    setLocalData((prev: any) => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }

    // Clear save error when user starts making changes
    if (validationErrors.save) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, save: '' }));
    }
  }, [validationErrors]);

  // Validate form data
  const validateForm = () => {
    const validation = validateSection(localData, 'applicantInfo');
    setValidationErrors(validation.errors);
    return validation.isValid;
  };

  // Save data to backend
  const saveData = async () => {
    const validation = validateSection(localData, 'applicantInfo');
    setValidationErrors(validation.errors);

    if (!validation.isValid) {
      // Don't call onStepError as it hides the form
      // Instead, let the form show validation errors inline
      console.log('Validation failed:', validation.errors);
      return false;
    }

    setIsSaving(true);
    try {
      if (isEditMode && applicationId && applicationId !== 'new') {
        // Update existing application data
        await applicationFormDataService.saveOrUpdateFormSection(applicationId, 'applicantInfo', localData);

        // Mark step as completed in progress tracking
        await applicationProgressService.markStepCompleted(applicationId, 'applicant-info', localData);

        console.log('Applicant info updated for application:', applicationId);
      } else {
        // Create new applicant and application
        // Map individual person data to business applicant structure
        const applicantPayload = {
          name: `${localData.first_name} ${localData.last_name}`.trim(),
          email: localData.email,
          phone: localData.phone,
          business_registration_number: `BRN-${Date.now()}`,
          tpin: `TPIN-${Date.now()}`,
          website: 'https://example.com',
          date_incorporation: new Date().toISOString(),
          place_incorporation: localData.city || 'Malawi'
        };

        const createdApplicant = await applicantService.createApplicant(applicantPayload);



        console.log('Created applicant response:', createdApplicant);
        console.log('Applicant ID from response:', createdApplicant?.applicant_id);

        // Validate that we got a valid applicant ID
        if (!createdApplicant || !createdApplicant.applicant_id) {
          throw new Error('Failed to create applicant: No applicant ID returned');
        }

        const applicationPayload = {
          application_number: `APP-${Date.now()}`,
          applicant_id: createdApplicant.applicant_id,
          license_category_id: licenseCategoryId,
          status: 'draft' as any
        };

        console.log('Creating application with payload:', applicationPayload);
        const createdApplication = await applicationService.createApplication(applicationPayload);

        console.log('Created application response:', createdApplication);
        console.log('Application ID from response:', createdApplication?.application_id);

        // Validate that we got a valid application ID
        if (!createdApplication || !createdApplication.application_id) {
          throw new Error('Failed to create application: No application ID returned');
        }

        const newApplicationId = createdApplication.application_id;
        console.log('Using application ID:', newApplicationId);

        // Save form data
        await applicationFormDataService.saveOrUpdateFormSection(
          newApplicationId,
          'applicantInfo',
          localData
        );

        console.log('New application created:', newApplicationId);

        // Initialize progress tracking for the new application
        if (licenseTypeId) {
          await applicationProgressService.initializeProgress(newApplicationId, licenseTypeId);
        }

        // Mark this step as completed
        await applicationProgressService.markStepCompleted(newApplicationId, 'applicant-info', localData);

        console.log('Applicant info step completed, passing application ID to parent:', newApplicationId);

        // Set application created state
        setApplicationCreated(true);
        setCreatedApplicationId(newApplicationId);

        // Pass the application ID to the parent - let the parent handle navigation
        onStepComplete?.('applicant-info', { ...localData, applicationId: newApplicationId });

        // Don't navigate here - show continue button instead
        return true;
      }

      setHasUnsavedChanges(false);
      onStepComplete?.('applicant-info', localData);
      return true;
    } catch (error: any) {
      console.error('Error saving applicant info:', error);
      console.error('Error details:', {
        message: error.message,
        response: error.response?.data,
        status: error.response?.status
      });

      // Extract meaningful error message
      let errorMessage = 'Failed to save applicant information';
      if (error.message && error.message.includes('Invalid applicationId')) {
        errorMessage = 'Error creating application record. Please try again.';
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      // Don't call onStepError as it hides the form - instead show inline error
      // onStepError?.('applicant-info', { save: errorMessage });

      // Show error in the form itself
      setValidationErrors({ save: errorMessage });
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Handle save button click
  const handleSave = async () => {
    await saveData();
  };

  // Handle continue to next step
  const handleContinueToNextStep = () => {
    console.log('Continue to next step clicked');
    console.log('Created application ID:', createdApplicationId);
    console.log('License category ID:', licenseCategoryId);

    if (createdApplicationId && onNext) {
      // Update the URL with the application ID and move to next step
      const params = new URLSearchParams(window.location.search);
      params.set('application_id', createdApplicationId);
      const newUrl = `/customer/applications/apply?${params.toString()}`;
      window.history.replaceState({}, '', newUrl);

      // Call the onNext callback to move to the next step
      onNext();
    } else if (onNext) {
      // If no application ID yet, just move to next step
      onNext();
    } else {
      console.error('No navigation callback available');
    }
  };



  return (
    <div className="space-y-6">
      <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
        <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
          Applicant Information
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
          Please provide your personal information. This will create your application record.
        </p>
        {!applicationId && (
          <div className="mt-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              <i className="ri-information-line mr-1"></i>
              Your application will be created when you save this step.
            </p>
          </div>
        )}
        {applicationId && (
          <div className="mt-2 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
            <p className="text-sm text-green-700 dark:text-green-300">
              <i className="ri-check-line mr-1"></i>
              Application created: {applicationId.slice(0, 8)}...
            </p>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Applicant Type */}
        <div className="md:col-span-2">
          <Select
            label="Applicant Type"
            value={localData.applicant_type || ''}
            onChange={(value) => handleLocalChange('applicant_type', value)}
            options={[
              { value: 'individual', label: 'Individual' },
              { value: 'company', label: 'Company' },
              { value: 'organization', label: 'Organization' }
            ]}
            required
            error={validationErrors.applicant_type}
          />
        </div>

        {/* Personal Information */}
        <TextInput
          label="First Name"
          value={localData.first_name || ''}
          onChange={(e) => handleLocalChange('first_name', e.target.value)}
          required
          error={validationErrors.first_name}
        />

        <TextInput
          label="Last Name"
          value={localData.last_name || ''}
          onChange={(e) => handleLocalChange('last_name', e.target.value)}
          required
          error={validationErrors.last_name}
        />

        <TextInput
          label="Middle Name"
          value={localData.middle_name || ''}
          onChange={(e) => handleLocalChange('middle_name', e.target.value)}
          error={validationErrors.middle_name}
        />

        <Select
          label="Gender"
          value={localData.gender || ''}
          onChange={(value) => handleLocalChange('gender', value)}
          options={[
            { value: 'male', label: 'Male' },
            { value: 'female', label: 'Female' },
            { value: 'other', label: 'Other' }
          ]}
          required
          error={validationErrors.gender}
        />

        {/* Contact Information */}
        <TextInput
          label="Email Address"
          type="email"
          value={localData.email || ''}
          onChange={(e) => handleLocalChange('email', e.target.value)}
          required
          error={validationErrors.email}
        />

        <TextInput
          label="Phone Number"
          value={localData.phone || ''}
          onChange={(e) => handleLocalChange('phone', e.target.value)}
          placeholder="+265 1 234 567"
          required
          error={validationErrors.phone}
        />

        {/* Identification */}
        <TextInput
          label="National ID"
          value={localData.national_id || ''}
          onChange={(e) => handleLocalChange('national_id', e.target.value)}
          placeholder="**********"
          required
          error={validationErrors.national_id}
        />

        <TextInput
          label="Date of Birth"
          type="date"
          value={localData.date_of_birth || ''}
          onChange={(e) => handleLocalChange('date_of_birth', e.target.value)}
          required
          error={validationErrors.date_of_birth}
        />

        <Select
          label="Nationality"
          value={localData.nationality || 'Malawian'}
          onChange={(value) => handleLocalChange('nationality', value)}
          options={[
            { value: 'Malawian', label: 'Malawian' },
            { value: 'Other', label: 'Other' }
          ]}
          required
          error={validationErrors.nationality}
        />
      </div>

      {/* Address Information */}
      <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
        <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
          Address Information
        </h4>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="md:col-span-2">
            <TextInput
              label="Postal Address"
              value={localData.postal_address || ''}
              onChange={(e) => handleLocalChange('postal_address', e.target.value)}
              placeholder="P.O. Box 123"
              required
              error={validationErrors.postal_address}
            />
          </div>

          <div className="md:col-span-2">
            <TextInput
              label="Physical Address"
              value={localData.physical_address || ''}
              onChange={(e) => handleLocalChange('physical_address', e.target.value)}
              placeholder="Street address"
              required
              error={validationErrors.physical_address}
            />
          </div>

          <TextInput
            label="City"
            value={localData.city || ''}
            onChange={(e) => handleLocalChange('city', e.target.value)}
            required
            error={validationErrors.city}
          />

          <Select
            label="District"
            value={localData.district || ''}
            onChange={(value) => handleLocalChange('district', value)}
            options={[
              { value: 'Blantyre', label: 'Blantyre' },
              { value: 'Lilongwe', label: 'Lilongwe' },
              { value: 'Mzuzu', label: 'Mzuzu' },
              { value: 'Zomba', label: 'Zomba' },
              { value: 'Other', label: 'Other' }
            ]}
            required
            error={validationErrors.district}
          />

          <TextInput
            label="Postal Code"
            value={localData.postal_code || ''}
            onChange={(e) => handleLocalChange('postal_code', e.target.value)}
            error={validationErrors.postal_code}
          />
        </div>
      </div>

      {/* Error Display */}
      {validationErrors.save && (
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
          <div className="flex items-center">
            <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"></i>
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Error Saving Application
              </h3>
              <p className="text-red-700 dark:text-red-300 text-sm mt-1">
                {validationErrors.save}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Success Message */}
      {applicationCreated && !isEditMode && (
        <div className="bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4">
          <div className="flex items-center">
            <i className="ri-check-circle-line text-green-600 dark:text-green-400 text-lg mr-3"></i>
            <div>
              <h3 className="text-sm font-medium text-green-800 dark:text-green-200">
                Application Created Successfully!
              </h3>
              <p className="text-green-700 dark:text-green-300 text-sm mt-1">
                Your application has been created. You can now continue to the next step.
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
        {applicationCreated && !isEditMode ? (
          <>
            {/* Save Changes Button (for edit mode) */}
            <button
              onClick={handleSave}
              disabled={isSaving || isLoading}
              className="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 text-sm font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSaving || isLoading ? (
                <>
                  <i className="ri-loader-4-line animate-spin mr-2"></i>
                  Saving...
                </>
              ) : (
                <>
                  <i className="ri-save-line mr-2"></i>
                  Save Changes
                </>
              )}
            </button>

            {/* Continue to Next Step Button */}
            <button
              onClick={handleContinueToNextStep}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-arrow-right-line mr-2"></i>
              Continue to Company Profile
            </button>
          </>
        ) : (
          /* Create/Save Application Button */
          <button
            onClick={handleSave}
            disabled={isSaving || isLoading}
            className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSaving || isLoading ? (
              <>
                <i className="ri-loader-4-line animate-spin mr-2"></i>
                {applicationId ? 'Saving...' : 'Creating Application...'}
              </>
            ) : (
              <>
                <i className="ri-save-line mr-2"></i>
                {applicationId ? 'Save Changes' : 'Create Application'}
              </>
            )}
          </button>
        )}
      </div>
    </div>
  );
};

export default ApplicantInfo;
