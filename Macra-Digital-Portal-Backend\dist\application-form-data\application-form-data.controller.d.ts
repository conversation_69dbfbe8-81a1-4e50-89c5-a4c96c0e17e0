import { ApplicationFormDataService } from './application-form-data.service';
import { CreateApplicationFormDataDto } from '../dto/application-form-data/create-application-form-data.dto';
import { UpdateApplicationFormDataDto } from '../dto/application-form-data/update-application-form-data.dto';
import { ApplicationFormData } from '../entities/application-form-data.entity';
export declare class ApplicationFormDataController {
    private readonly applicationFormDataService;
    constructor(applicationFormDataService: ApplicationFormDataService);
    create(createApplicationFormDataDto: CreateApplicationFormDataDto, req: any): Promise<ApplicationFormData>;
    findByApplicationAndSection(applicationId: string, sectionName: string): Promise<ApplicationFormData>;
    findByApplication(applicationId: string): Promise<ApplicationFormData[]>;
    update(applicationId: string, sectionName: string, updateApplicationFormDataDto: UpdateApplicationFormDataDto, req: any): Promise<ApplicationFormData>;
    remove(applicationId: string, sectionName: string): Promise<void>;
    removeByApplication(applicationId: string): Promise<void>;
}
