'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CustomerLayout from '@/components/customer/CustomerLayout';
import { useAuth } from '@/contexts/AuthContext';
import { LicenseCategory } from '@/services/licenseCategoryService';
import { applicationService } from '@/services/applicationService';
import { applicationProgressService } from '@/services/applicationProgressService';
import { validateSection } from '@/utils/formValidation';
import { getLicenseTypeStepConfig, StepConfig, LicenseTypeStepConfig } from '@/config/licenseTypeStepConfig';
import { CustomerApiService } from '@/lib/customer-api';

const BusinessInfoPage: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { isAuthenticated, loading: authLoading } = useAuth();

  // Create customer API service instance
  const customerApi = new CustomerApiService();

  // Get query parameters
  const licenseCategoryId = searchParams.get('license_category_id');
  const applicationId = searchParams.get('application_id');

  // State
  const [licenseCategory, setLicenseCategory] = useState<LicenseCategory | null>(null);
  const [licenseTypeConfig, setLicenseTypeConfig] = useState<LicenseTypeStepConfig | null>(null);
  const [applicationSteps, setApplicationSteps] = useState<StepConfig[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Form data state
  const [formData, setFormData] = useState({
    business_model: '',
    operational_structure: '',
    target_market: '',
    competitive_advantage: '',
    facilities_description: '',
    equipment_description: '',
    operational_areas: '',
    service_delivery_model: '',
    quality_assurance: '',
    customer_support: ''
  });

  const [validationErrors, setValidationErrors] = useState<Record<string, string>>({});
  const [isSaving, setIsSaving] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      if (!licenseCategoryId) {
        setError('License category ID is required');
        setLoading(false);
        return;
      }

      try {
        setLoading(true);

        // Load license category
        const category = await customerApi.getLicenseCategory(licenseCategoryId);
        setLicenseCategory(category);

        // Get license type configuration
        const config = getLicenseTypeStepConfig(category.license_type.name);
        setLicenseTypeConfig(config);
        setApplicationSteps(config.steps);

        // Load existing application data if in edit mode
        if (applicationId && applicationId !== 'new') {
          try {
            const application = await applicationService.getApplication(applicationId);
            if (application && application.business_info) {
              setFormData(prev => ({ ...prev, ...application.business_info }));
            }
          } catch (error) {
            console.error('Error loading existing application data:', error);
          }
        }

      } catch (error: any) {
        console.error('Error loading data:', error);
        setError(error.message || 'Failed to load application data');
      } finally {
        setLoading(false);
      }
    };

    if (!authLoading) {
      loadData();
    }
  }, [licenseCategoryId, applicationId, authLoading]);

  // Handle form field changes
  const handleChange = useCallback((field: string, value: any) => {
    setFormData((prev: any) => ({ ...prev, [field]: value }));
    setHasUnsavedChanges(true);

    // Clear validation error for this field if it exists
    if (validationErrors && validationErrors[field]) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, [field]: '' }));
    }

    // Clear save error when user starts making changes
    if (validationErrors.save) {
      setValidationErrors((prev: Record<string, string>) => ({ ...prev, save: '' }));
    }
  }, [validationErrors]);

  // Handle field blur for validation
  const handleBlur = (field: string) => {
    // Validate single field on blur
    const validation = validateSection({ [field]: formData[field] }, 'businessInfo');
    if (validation.errors && validation.errors[field]) {
      setValidationErrors(prev => ({ ...prev, [field]: validation.errors[field] }));
    }
  };

  // Validate form data
  const validateForm = () => {
    const validation = validateSection(formData, 'businessInfo');
    const errors = validation.errors || {};
    setValidationErrors(errors);
    return validation.isValid;
  };

  // Save data to backend
  const saveData = async () => {
    const validation = validateSection(formData, 'businessInfo');
    const errors = validation.errors || {};
    setValidationErrors(errors);

    if (!validation.isValid) {
      console.log('Validation failed:', errors);
      return false;
    }

    if (!applicationId || applicationId === 'new') {
      setValidationErrors(prev => ({ ...prev, save: 'Application ID is required to save business information' }));
      return false;
    }

    setIsSaving(true);
    try {
      // Update application with business info
      await applicationService.updateApplication(applicationId, {
        business_info: formData
      });

      // Mark step as completed in progress tracking
      await applicationProgressService.markStepCompleted(applicationId, 'business-info', formData);

      console.log('Business info data saved for application:', applicationId);
      setHasUnsavedChanges(false);
      return true;
    } catch (error: any) {
      console.error('Error saving business info data:', error);

      // Extract meaningful error message
      let errorMessage = 'Failed to save business information';
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      setValidationErrors(prev => ({ ...prev, save: errorMessage }));
      return false;
    } finally {
      setIsSaving(false);
    }
  };

  // Handle save button click
  const handleSave = async () => {
    const success = await saveData();
    if (success) {
      // Navigate to next step or show success message
      const currentStepIndex = applicationSteps.findIndex(step => step.id === 'business-info');
      if (currentStepIndex >= 0 && currentStepIndex < applicationSteps.length - 1) {
        const nextStep = applicationSteps[currentStepIndex + 1];
        router.push(`/customer/applications/apply/${nextStep.id}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
      }
    }
  };

  // Handle navigation
  const handleNavigation = (stepId: string) => {
    if (hasUnsavedChanges) {
      if (confirm('You have unsaved changes. Do you want to save before navigating?')) {
        saveData().then(() => {
          router.push(`/customer/applications/apply/${stepId}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
        });
      } else {
        router.push(`/customer/applications/apply/${stepId}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
      }
    } else {
      router.push(`/customer/applications/apply/${stepId}?license_category_id=${licenseCategoryId}&application_id=${applicationId}`);
    }
  };

  // Loading state
  if (authLoading || loading) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <i className="ri-loader-4-line animate-spin text-4xl text-primary mb-4"></i>
            <p className="text-gray-600 dark:text-gray-400">Loading business information form...</p>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Error state
  if (error) {
    return (
      <CustomerLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <i className="ri-error-warning-line text-4xl text-red-500 mb-4"></i>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">Error Loading Form</h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <button
              onClick={() => router.back()}
              className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Go Back
            </button>
          </div>
        </div>
      </CustomerLayout>
    );
  }

  // Authentication check
  if (!isAuthenticated) {
    router.push('/customer/auth/login');
    return null;
  }

  return (
    <CustomerLayout>
      <div className="max-w-4xl mx-auto py-8 px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
                Business Information
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {licenseCategory?.name} - {licenseCategory?.license_type?.name}
              </p>
            </div>
            <button
              onClick={() => router.back()}
              className="inline-flex items-center px-3 py-2 border border-gray-300 dark:border-gray-600 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary"
            >
              <i className="ri-arrow-left-line mr-2"></i>
              Back
            </button>
          </div>
        </div>

        {/* Progress Steps */}
        {applicationSteps.length > 0 && (
          <div className="mb-8">
            <div className="flex items-center justify-between">
              {applicationSteps.map((step, index) => {
                const isActive = step.id === 'business-info';
                const isCompleted = false; // You can implement completion tracking

                return (
                  <div key={step.id} className="flex items-center">
                    <div
                      className={`flex items-center justify-center w-8 h-8 rounded-full border-2 cursor-pointer ${
                        isActive
                          ? 'border-primary bg-primary text-white'
                          : isCompleted
                          ? 'border-green-500 bg-green-500 text-white'
                          : 'border-gray-300 dark:border-gray-600 text-gray-500 dark:text-gray-400'
                      }`}
                      onClick={() => handleNavigation(step.id)}
                    >
                      {isCompleted ? (
                        <i className="ri-check-line text-sm"></i>
                      ) : (
                        <span className="text-sm font-medium">{index + 1}</span>
                      )}
                    </div>
                    <span
                      className={`ml-2 text-sm font-medium cursor-pointer ${
                        isActive
                          ? 'text-primary'
                          : isCompleted
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-gray-500 dark:text-gray-400'
                      }`}
                      onClick={() => handleNavigation(step.id)}
                    >
                      {step.name}
                    </span>
                    {index < applicationSteps.length - 1 && (
                      <div className="flex-1 h-0.5 bg-gray-300 dark:bg-gray-600 mx-4"></div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {/* Form Content */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6">
          <div className="space-y-6">
            <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">
                Business Information
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                Describe your business operations, structure, and service delivery model.
              </p>
            </div>

            {/* Business Model */}
            <div className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Business Model *
                </label>
                <textarea
                  value={formData.business_model || ''}
                  onChange={(e) => handleChange('business_model', e.target.value)}
                  onBlur={() => handleBlur('business_model')}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Describe your business model, revenue streams, and value proposition..."
                />
                {validationErrors.business_model && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {validationErrors.business_model}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Operational Structure *
                </label>
                <textarea
                  value={formData.operational_structure || ''}
                  onChange={(e) => handleChange('operational_structure', e.target.value)}
                  onBlur={() => handleBlur('operational_structure')}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Describe your organizational structure, departments, and operational processes..."
                />
                {validationErrors.operational_structure && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {validationErrors.operational_structure}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Target Market *
                </label>
                <textarea
                  value={formData.target_market || ''}
                  onChange={(e) => handleChange('target_market', e.target.value)}
                  onBlur={() => handleBlur('target_market')}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Describe your target customers and market segments..."
                />
                {validationErrors.target_market && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {validationErrors.target_market}
                  </p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Competitive Advantage *
                </label>
                <textarea
                  value={formData.competitive_advantage || ''}
                  onChange={(e) => handleChange('competitive_advantage', e.target.value)}
                  onBlur={() => handleBlur('competitive_advantage')}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                  placeholder="What makes your business unique and competitive in the market..."
                />
                {validationErrors.competitive_advantage && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                    {validationErrors.competitive_advantage}
                  </p>
                )}
              </div>
            </div>

            {/* Facilities and Equipment */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Facilities and Equipment
              </h4>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Facilities Description *
                  </label>
                  <textarea
                    value={formData.facilities_description || ''}
                    onChange={(e) => handleChange('facilities_description', e.target.value)}
                    onBlur={() => handleBlur('facilities_description')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                    placeholder="Describe your office locations, facilities, and infrastructure..."
                  />
                  {validationErrors.facilities_description && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {validationErrors.facilities_description}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Equipment Description *
                  </label>
                  <textarea
                    value={formData.equipment_description || ''}
                    onChange={(e) => handleChange('equipment_description', e.target.value)}
                    onBlur={() => handleBlur('equipment_description')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                    placeholder="List and describe your equipment, technology, and tools..."
                  />
                  {validationErrors.equipment_description && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {validationErrors.equipment_description}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Operational Areas *
                  </label>
                  <textarea
                    value={formData.operational_areas || ''}
                    onChange={(e) => handleChange('operational_areas', e.target.value)}
                    onBlur={() => handleBlur('operational_areas')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                    placeholder="Describe the geographic areas where you operate or plan to operate..."
                  />
                  {validationErrors.operational_areas && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {validationErrors.operational_areas}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Service Delivery */}
            <div className="border-t border-gray-200 dark:border-gray-700 pt-6">
              <h4 className="text-md font-medium text-gray-900 dark:text-gray-100 mb-4">
                Service Delivery
              </h4>

              <div className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Service Delivery Model *
                  </label>
                  <textarea
                    value={formData.service_delivery_model || ''}
                    onChange={(e) => handleChange('service_delivery_model', e.target.value)}
                    onBlur={() => handleBlur('service_delivery_model')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                    placeholder="Describe how you deliver services to customers..."
                  />
                  {validationErrors.service_delivery_model && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {validationErrors.service_delivery_model}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Quality Assurance *
                  </label>
                  <textarea
                    value={formData.quality_assurance || ''}
                    onChange={(e) => handleChange('quality_assurance', e.target.value)}
                    onBlur={() => handleBlur('quality_assurance')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                    placeholder="Describe your quality control and assurance processes..."
                  />
                  {validationErrors.quality_assurance && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {validationErrors.quality_assurance}
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Customer Support *
                  </label>
                  <textarea
                    value={formData.customer_support || ''}
                    onChange={(e) => handleChange('customer_support', e.target.value)}
                    onBlur={() => handleBlur('customer_support')}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-gray-100"
                    placeholder="Describe your customer support and service processes..."
                  />
                  {validationErrors.customer_support && (
                    <p className="mt-1 text-sm text-red-600 dark:text-red-400">
                      {validationErrors.customer_support}
                    </p>
                  )}
                </div>
              </div>
            </div>

            {/* Error Display */}
            {validationErrors.save && (
              <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
                <div className="flex items-center">
                  <i className="ri-error-warning-line text-red-600 dark:text-red-400 text-lg mr-3"></i>
                  <div>
                    <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                      Error Saving Business Information
                    </h3>
                    <p className="text-red-700 dark:text-red-300 text-sm mt-1">
                      {validationErrors.save}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Save Button */}
            <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
              <button
                onClick={handleSave}
                disabled={isSaving || isLoading}
                className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-primary hover:bg-primary-dark focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving || isLoading ? (
                  <>
                    <i className="ri-loader-4-line animate-spin mr-2"></i>
                    Saving...
                  </>
                ) : (
                  <>
                    <i className="ri-save-line mr-2"></i>
                    Save Business Information
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      </div>
    </CustomerLayout>
  );
};

export default BusinessInfoPage;
