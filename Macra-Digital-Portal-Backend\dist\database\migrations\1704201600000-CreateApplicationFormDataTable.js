"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.CreateApplicationFormDataTable1704201600000 = void 0;
const typeorm_1 = require("typeorm");
class CreateApplicationFormDataTable1704201600000 {
    name = 'CreateApplicationFormDataTable1704201600000';
    async up(queryRunner) {
        await queryRunner.createTable(new typeorm_1.Table({
            name: 'application_form_data',
            columns: [
                {
                    name: 'form_data_id',
                    type: 'varchar',
                    length: '36',
                    isPrimary: true,
                    generationStrategy: 'uuid',
                    default: 'uuid()',
                },
                {
                    name: 'application_id',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'section_name',
                    type: 'varchar',
                    length: '100',
                    isNullable: false,
                },
                {
                    name: 'section_data',
                    type: 'json',
                    isNullable: false,
                },
                {
                    name: 'completed',
                    type: 'boolean',
                    default: false,
                },
                {
                    name: 'created_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'created_by',
                    type: 'varchar',
                    length: '36',
                    isNullable: false,
                },
                {
                    name: 'updated_at',
                    type: 'timestamp',
                    default: 'CURRENT_TIMESTAMP',
                    onUpdate: 'CURRENT_TIMESTAMP',
                },
                {
                    name: 'updated_by',
                    type: 'varchar',
                    length: '36',
                    isNullable: true,
                },
                {
                    name: 'deleted_at',
                    type: 'timestamp',
                    isNullable: true,
                },
            ],
        }), true);
        await queryRunner.createIndex('application_form_data', new typeorm_1.Index('IDX_application_form_data_app_section', ['application_id', 'section_name'], {
            isUnique: true,
        }));
        await queryRunner.createForeignKey('application_form_data', new typeorm_1.ForeignKey({
            columnNames: ['application_id'],
            referencedColumnNames: ['application_id'],
            referencedTableName: 'applications',
            onDelete: 'CASCADE',
            onUpdate: 'CASCADE',
        }));
        await queryRunner.createForeignKey('application_form_data', new typeorm_1.ForeignKey({
            columnNames: ['created_by'],
            referencedColumnNames: ['user_id'],
            referencedTableName: 'users',
            onDelete: 'RESTRICT',
            onUpdate: 'CASCADE',
        }));
        await queryRunner.createForeignKey('application_form_data', new typeorm_1.ForeignKey({
            columnNames: ['updated_by'],
            referencedColumnNames: ['user_id'],
            referencedTableName: 'users',
            onDelete: 'SET NULL',
            onUpdate: 'CASCADE',
        }));
    }
    async down(queryRunner) {
        await queryRunner.dropTable('application_form_data');
    }
}
exports.CreateApplicationFormDataTable1704201600000 = CreateApplicationFormDataTable1704201600000;
//# sourceMappingURL=1704201600000-CreateApplicationFormDataTable.js.map