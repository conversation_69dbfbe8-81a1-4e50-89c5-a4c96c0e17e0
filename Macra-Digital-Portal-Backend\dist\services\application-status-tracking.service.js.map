{"version": 3, "file": "application-status-tracking.service.js", "sourceRoot": "", "sources": ["../../src/services/application-status-tracking.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoF;AACpF,6CAAmD;AACnD,qCAAyC;AACzC,yEAA+D;AAC/D,qGAAyF;AACzF,yDAA+C;AAQxC,IAAM,gCAAgC,GAAtC,MAAM,gCAAgC;IAGjC;IAEA;IAEA;IANV,YAEU,sBAAgD,EAEhD,uBAA6D,EAE7D,cAAgC;QAJhC,2BAAsB,GAAtB,sBAAsB,CAA0B;QAEhD,4BAAuB,GAAvB,uBAAuB,CAAsC;QAE7D,mBAAc,GAAd,cAAc,CAAkB;IACvC,CAAC;IAEJ,KAAK,CAAC,uBAAuB,CAC3B,aAAqB,EACrB,eAA2C,EAC3C,MAAc;QAGd,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC5D,KAAK,EAAE,EAAE,cAAc,EAAE,aAAa,EAAE;YACxC,SAAS,EAAE,CAAC,WAAW,EAAE,kBAAkB,CAAC;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,aAAa,YAAY,CAAC,CAAC;QAChF,CAAC;QAGD,IAAI,CAAC,wBAAwB,CAAC,WAAW,CAAC,MAAM,EAAE,eAAe,CAAC,MAAM,CAAC,CAAC;QAG1E,MAAM,cAAc,GAAG,WAAW,CAAC,MAAM,CAAC;QAG1C,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG,IAAI,CAAC,wBAAwB,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;QAEvF,WAAW,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;QAC5C,WAAW,CAAC,YAAY,GAAG,OAAO,CAAC;QACnC,WAAW,CAAC,mBAAmB,GAAG,WAAW,CAAC;QAC9C,WAAW,CAAC,UAAU,GAAG,MAAM,CAAC;QAGhC,IAAI,eAAe,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE,CAAC;YACxE,WAAW,CAAC,YAAY,GAAG,IAAI,IAAI,EAAE,CAAC;QACxC,CAAC;QAGD,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;QAGpD,MAAM,aAAa,GAAG,IAAI,4DAAwB,EAAE,CAAC;QACrD,aAAa,CAAC,cAAc,GAAG,aAAa,CAAC;QAC7C,aAAa,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;QAC9C,aAAa,CAAC,eAAe,GAAG,cAAc,CAAC;QAC/C,aAAa,CAAC,QAAQ,GAAG,eAAe,CAAC,QAAQ,CAAC;QAClD,aAAa,CAAC,MAAM,GAAG,eAAe,CAAC,MAAM,CAAC;QAC9C,aAAa,CAAC,UAAU,GAAG,eAAe,CAAC,UAAU,IAAI,MAAM,CAAC;QAEhE,IAAI,eAAe,CAAC,yBAAyB,EAAE,CAAC;YAC9C,aAAa,CAAC,yBAAyB,GAAG,IAAI,IAAI,CAAC,eAAe,CAAC,yBAAyB,CAAC,CAAC;QAChG,CAAC;QAED,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QAGvD,OAAO,IAAI,CAAC,4BAA4B,CAAC,aAAa,CAAC,CAAC;IAC1D,CAAC;IAED,KAAK,CAAC,4BAA4B,CAAC,aAAqB;QACtD,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC5D,KAAK,EAAE,EAAE,cAAc,EAAE,aAAa,EAAE;YACxC,SAAS,EAAE,CAAC,WAAW,EAAE,kBAAkB,CAAC;SAC7C,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,aAAa,YAAY,CAAC,CAAC;QAChF,CAAC;QAGD,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YAC5D,KAAK,EAAE,EAAE,cAAc,EAAE,aAAa,EAAE;YACxC,SAAS,EAAE,CAAC,MAAM,CAAC;YACnB,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;QAGH,MAAM,kBAAkB,GAA0C,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YAC9F,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,eAAe,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,QAAQ;YACjG,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;SAC7D,CAAC,CAAC,CAAC;QAEJ,OAAO;YACL,cAAc,EAAE,WAAW,CAAC,cAAc;YAC1C,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;YAClD,cAAc,EAAE,WAAW,CAAC,MAAM;YAClC,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,mBAAmB,EAAE,WAAW,CAAC,mBAAmB;YACpD,YAAY,EAAE,WAAW,CAAC,YAAY;YACtC,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,UAAU,EAAE,WAAW,CAAC,UAAU;YAClC,cAAc,EAAE,kBAAkB;YAClC,SAAS,EAAE;gBACT,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,IAAI;gBAChC,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,KAAK;gBAClC,4BAA4B,EAAE,WAAW,CAAC,SAAS,CAAC,4BAA4B;aACjF;YACD,gBAAgB,EAAE;gBAChB,IAAI,EAAE,WAAW,CAAC,gBAAgB,CAAC,IAAI;gBACvC,WAAW,EAAE,WAAW,CAAC,gBAAgB,CAAC,WAAW;aACtD;SACF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,uBAAuB,CAAC,MAAc;QAC1C,MAAM,YAAY,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,IAAI,CAAC;YAC1D,KAAK,EAAE,EAAE,MAAM,EAAE;YACjB,SAAS,EAAE,CAAC,WAAW,EAAE,kBAAkB,EAAE,+BAA+B,CAAC;YAC7E,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;QAEH,IAAI,YAAY,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC9B,OAAO,EAAE,CAAC;QACZ,CAAC;QAGD,MAAM,cAAc,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACnE,MAAM,eAAe,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YAC9D,KAAK,EAAE,EAAE,cAAc,EAAE,IAAA,YAAE,EAAC,cAAc,CAAC,EAAE;YAC7C,SAAS,EAAE,CAAC,MAAM,CAAC;YACnB,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;QAGH,MAAM,gBAAgB,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,OAAO,EAAE,EAAE;YAC/D,IAAI,CAAC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,EAAE,CAAC;gBACjC,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,GAAG,EAAE,CAAC;YACnC,CAAC;YACD,GAAG,CAAC,OAAO,CAAC,cAAc,CAAC,CAAC,IAAI,CAAC,OAAO,CAAC,CAAC;YAC1C,OAAO,GAAG,CAAC;QACb,CAAC,EAAE,EAA2B,CAAC,CAAC;QAGhC,OAAO,YAAY,CAAC,GAAG,CAAC,WAAW,CAAC,EAAE;YACpC,MAAM,YAAY,GAAG,gBAAgB,CAAC,WAAW,CAAC,cAAc,CAAC,IAAI,EAAE,CAAC;YACxE,MAAM,kBAAkB,GAAG,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;gBACtD,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,cAAc,EAAE,OAAO,CAAC,cAAc;gBACtC,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,eAAe,EAAE,OAAO,CAAC,eAAe;gBACxC,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;gBACtB,eAAe,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,QAAQ;gBACjG,UAAU,EAAE,OAAO,CAAC,UAAU;gBAC9B,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;aAC7D,CAAC,CAAC,CAAC;YAEJ,OAAO;gBACL,cAAc,EAAE,WAAW,CAAC,cAAc;gBAC1C,kBAAkB,EAAE,WAAW,CAAC,kBAAkB;gBAClD,cAAc,EAAE,WAAW,CAAC,MAAM;gBAClC,YAAY,EAAE,WAAW,CAAC,YAAY;gBACtC,mBAAmB,EAAE,WAAW,CAAC,mBAAmB;gBACpD,YAAY,EAAE,WAAW,CAAC,YAAY;gBACtC,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,UAAU,EAAE,WAAW,CAAC,UAAU;gBAClC,cAAc,EAAE,kBAAkB;gBAClC,SAAS,EAAE;oBACT,IAAI,EAAE,WAAW,CAAC,SAAS,CAAC,IAAI;oBAChC,KAAK,EAAE,WAAW,CAAC,SAAS,CAAC,KAAK;oBAClC,4BAA4B,EAAE,WAAW,CAAC,SAAS,CAAC,4BAA4B;iBACjF;gBACD,gBAAgB,EAAE;oBAChB,IAAI,EAAE,WAAW,CAAC,gBAAgB,CAAC,IAAI;oBACvC,WAAW,EAAE,WAAW,CAAC,gBAAgB,CAAC,WAAW;iBACtD;aACF,CAAC;QACJ,CAAC,CAAC,CAAC;IACL,CAAC;IAEO,wBAAwB,CAAC,aAAqB,EAAE,SAAiB;QACvE,MAAM,gBAAgB,GAA6B;YACjD,OAAO,EAAE,CAAC,WAAW,CAAC;YACtB,WAAW,EAAE,CAAC,cAAc,CAAC;YAC7B,cAAc,EAAE,CAAC,YAAY,EAAE,UAAU,CAAC;YAC1C,YAAY,EAAE,CAAC,UAAU,EAAE,UAAU,CAAC;YACtC,UAAU,EAAE,EAAE;YACd,UAAU,EAAE,EAAE;YACd,WAAW,EAAE,EAAE;SAChB,CAAC;QAEF,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,IAAI,CAAC,gBAAgB,CAAC,aAAa,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,EAAE,CAAC;YAC7F,MAAM,IAAI,4BAAmB,CAC3B,kCAAkC,aAAa,OAAO,SAAS,EAAE,CAClE,CAAC;QACJ,CAAC;IACH,CAAC;IAEO,wBAAwB,CAAC,MAAc;QAC7C,MAAM,aAAa,GAAuD;YACxE,OAAO,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;YAClC,WAAW,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;YACtC,cAAc,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;YACzC,YAAY,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,EAAE;YACvC,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,GAAG,EAAE;YACtC,UAAU,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;YACpC,WAAW,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,QAAQ,EAAE,CAAC,EAAE;SACtC,CAAC;QAEF,MAAM,OAAO,GAAG,aAAa,CAAC,MAAM,CAAC,CAAC;QACtC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,4BAAmB,CAAC,mBAAmB,MAAM,EAAE,CAAC,CAAC;QAC7D,CAAC;QAED,OAAO;YACL,OAAO,EAAE,OAAO,CAAC,IAAI;YACrB,WAAW,EAAE,OAAO,CAAC,QAAQ;SAC9B,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,aAAqB;QAC1C,MAAM,WAAW,GAAG,MAAM,IAAI,CAAC,sBAAsB,CAAC,OAAO,CAAC;YAC5D,KAAK,EAAE,EAAE,cAAc,EAAE,aAAa,EAAE;SACzC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,MAAM,IAAI,0BAAiB,CAAC,uBAAuB,aAAa,YAAY,CAAC,CAAC;QAChF,CAAC;QAED,MAAM,aAAa,GAAG,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC;YAC5D,KAAK,EAAE,EAAE,cAAc,EAAE,aAAa,EAAE;YACxC,SAAS,EAAE,CAAC,MAAM,CAAC;YACnB,KAAK,EAAE,EAAE,UAAU,EAAE,MAAM,EAAE;SAC9B,CAAC,CAAC;QAEH,OAAO,aAAa,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,CAAC;YACnC,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,cAAc,EAAE,OAAO,CAAC,cAAc;YACtC,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,eAAe,EAAE,OAAO,CAAC,eAAe;YACxC,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,eAAe,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,IAAI,CAAC,UAAU,IAAI,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,QAAQ;YACjG,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,yBAAyB,EAAE,OAAO,CAAC,yBAAyB;SAC7D,CAAC,CAAC,CAAC;IACN,CAAC;CACF,CAAA;AA3PY,4EAAgC;2CAAhC,gCAAgC;IAD5C,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,0BAAgB,EAAC,kCAAY,CAAC,CAAA;IAE9B,WAAA,IAAA,0BAAgB,EAAC,4DAAwB,CAAC,CAAA;IAE1C,WAAA,IAAA,0BAAgB,EAAC,kBAAI,CAAC,CAAA;qCAHS,oBAAU;QAET,oBAAU;QAEnB,oBAAU;GAPzB,gCAAgC,CA2P5C"}