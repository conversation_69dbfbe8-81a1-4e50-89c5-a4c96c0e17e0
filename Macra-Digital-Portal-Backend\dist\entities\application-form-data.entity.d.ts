import { Applications } from './applications.entity';
import { User } from './user.entity';
export declare class ApplicationFormData {
    form_data_id: string;
    application_id: string;
    section_name: string;
    section_data: Record<string, any>;
    completed: boolean;
    created_at: Date;
    created_by: string;
    updated_at: Date;
    updated_by?: string;
    deleted_at?: Date;
    application: Applications;
    creator: User;
    updater?: User;
    generateId(): void;
}
